from django.db import models
from user.models import UserInfo


# Create your models here.

class Semester(models.Model):
    """
    学期模型
    """
    name = models.CharField(max_length=50, verbose_name='学期名称')
    start_date = models.DateField(verbose_name='开始日期')
    end_date = models.DateField(verbose_name='结束日期')
    is_active = models.BooleanField(default=True, verbose_name='激活状态')

    class Meta:
        db_table = 'semester'  # 数据库表名
        default_permissions = ()  # 禁用默认权限
        permissions = (
            ("add_course", "课程添加"),
            ("view_course", "课程查询"),
            ("change_course", "课程修改"),
            ("delete_course", "课程删除"),
        )
        verbose_name = '学期'  # 模型名称
        verbose_name_plural = verbose_name  # 模型复数名称


class Course(models.Model):
    """
    课程模型
    """
    title = models.CharField(max_length=400, verbose_name='课程标题', unique=True)
    description = models.TextField(verbose_name='课程描述', null=True, blank=True)
    image = models.ImageField(upload_to='course_images/', verbose_name='课程图片', null=True, blank=True)
    knowledge_uid = models.CharField(max_length=50, verbose_name='知识库UID', null=True, blank=True)
    created_at = models.DateTimeField(verbose_name='创建时间', auto_now_add=True)  # 创建时间
    updated_at = models.DateTimeField(verbose_name='更新时间', auto_now=True)  # 更新时间
    # user = models.ManyToManyField(UserInfo, through='CourseTeacher', verbose_name='创建者', related_name='courses')
    semester = models.ManyToManyField(Semester, through='CourseSemester', verbose_name='学期', related_name='courses')

    class Meta:
        db_table = 'course'  # 数据库表名
        default_permissions = ()  # 禁用默认权限
        permissions = (
            ("add_course", "课程添加"),
            ("view_course", "课程查询"),
            ("change_course", "课程修改"),
            ("delete_course", "课程删除"),
            ("owner_course", "课程拥有者查询"),
            ("semester_course", "课程及学期信息查询"),
            ("knowledge_course", "课程知识点列表查询"),
            ("reopen_course", "课程重开"),
            ("copy_course", "课程复制"),
            ("chapter_course", "用户关联课程章节列表查询"),
        )
        verbose_name = '课程'  # 模型名称
        verbose_name_plural = verbose_name  # 模型复数名称


class CourseSemester(models.Model):
    """
    课程学期模型
    """
    course = models.ForeignKey(Course, on_delete=models.CASCADE, verbose_name='所属课程',
                               related_name='course_semester')
    semester = models.ForeignKey(Semester, on_delete=models.CASCADE, verbose_name='所属学期',
                                 related_name='course_semester')
    user = models.ForeignKey(UserInfo, verbose_name='创建者', on_delete=models.SET_NULL, null=True, blank=True,
                             related_name='semester_courses')
    is_active = models.BooleanField(verbose_name='是否活跃', default=True)

    class Meta:
        db_table = 'course_semester'  # 数据库表名
        default_permissions = ()  # 禁用默认权限
        permissions = (
            ("add_course_semester", "课程学期添加"),
            ("view_course_semester", "课程学期查询"),
            ("change_course_semester", "课程学期修改"),
            ("delete_course_semester", "课程学期删除"),
            ("owner_course_semester", "课程学期拥有者查询"),
        )
        verbose_name = '课程学期'  # 模型名称
        verbose_name_plural = verbose_name  # 模型复数名称


class CourseTeacher(models.Model):
    """
    课程教师模型
    """
    ROLE = (
        ('teacher', '教师'),
        ('assistant', '助教'),
    )
    course_semester = models.ForeignKey(CourseSemester, on_delete=models.CASCADE, verbose_name='课程学期',
                                        related_name='teachers', default=1)
    teacher = models.ForeignKey(UserInfo, on_delete=models.CASCADE, verbose_name='教师', related_name='teacher_courses')
    role = models.CharField(max_length=20, verbose_name='角色', choices=ROLE, default='teacher')
    is_active = models.BooleanField(verbose_name='是否活跃', default=True)
    joined_at = models.DateTimeField(verbose_name='加入时间', auto_now_add=True)  # 加入时间

    class Meta:
        db_table = 'course_teacher'  # 数据库表名
        default_permissions = ()  # 禁用默认权限
        permissions = (
            ("add_course_teacher", "课程教师添加"),
            ("view_course_teacher", "课程教师查询"),
            ("batch_destroy_course_teacher", "课程教师删除"),
        )
        verbose_name = '课程教师'  # 模型名称
        verbose_name_plural = verbose_name  # 模型复数名称


class Chapter(models.Model):
    """
    章节模型
    """
    title = models.CharField(max_length=400, verbose_name='章节标题')
    content = models.TextField(verbose_name='章节内容', null=True, blank=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, verbose_name='父章节', null=True, blank=True,
                               related_name='children')
    order = models.IntegerField(verbose_name='章节顺序', default=0)
    course = models.ForeignKey(Course, on_delete=models.CASCADE, verbose_name='所属课程', related_name='chapters',
                               null=True, blank=True)

    class Meta:
        db_table = 'chapter'  # 数据库表名
        default_permissions = ()  # 禁用默认权限
        permissions = (
            ("add_chapter", "章节添加"),
            ("view_chapter", "章节查询"),
            ("change_chapter", "章节修改"),
            ("delete_chapter", "章节删除"),
            ("tables_chapter", "章节目录查询"),
            ("owner_chapter", "用户所有课程章节目录查询"),
        )
        verbose_name = '章节'  # 模型名称
        verbose_name_plural = verbose_name  # 模型复数名称
        # 按父节点和排序字段默认排序
        ordering = ['parent__id', 'order']
        # 约束：同一父章节下的 order 必须唯一（避免排序冲突）
        unique_together = ('parent', 'order')


class Class(models.Model):
    """
    班级模型
    """
    user = models.ForeignKey(UserInfo, on_delete=models.CASCADE, verbose_name='所属用户')
    name = models.CharField(max_length=50, verbose_name='班级名称')
    description = models.TextField(verbose_name='班级描述', null=True, blank=True)
    created_at = models.DateTimeField(verbose_name='创建时间', auto_now_add=True)  # 创建时间
    course_semester = models.ForeignKey(CourseSemester, on_delete=models.CASCADE, verbose_name='所属课程学期',
                                        related_name='classes', null=True, blank=True)

    class Meta:
        db_table = 'class'  # 数据库表名
        unique_together = ('course_semester', 'name')  # 课程学期和班级名称唯一
        default_permissions = ()  # 禁用默认权限
        permissions = (
            ("add_class", "班级添加"),
            ("view_class", "班级查询"),
            ("change_class", "班级修改"),
            ("delete_class", "班级删除"),
            ("batch_destroy_class", "班级批量删除")
        )
        verbose_name = '班级'  # 模型名称
        verbose_name_plural = verbose_name  # 模型复数名称


# class ClassCourseSemester(models.Model):
#     """
#     班级课程模型
#     """
#     class_obj = models.ForeignKey(Class, on_delete=models.CASCADE, verbose_name='所属班级',
#                                   related_name='class_courses')
#     coursesemester = models.ForeignKey(CourseSemester, on_delete=models.CASCADE, verbose_name='课程', related_name='class_courses')
#
#     class Meta:
#         db_table = 'class_course'  # 数据库表名
#         default_permissions = ()  # 禁用默认权限
#         # permissions = (
#         #     ("add_class_course", "班级课程添加"),
#         #     ("view_class_course", "班级课程查询"),
#         #     ("change_class_course", "班级课程修改"),
#         #     ("delete_class_course", "班级课程删除"),
#         # )
#         verbose_name = '班级课程'  # 模型名称
#         verbose_name_plural = verbose_name  # 模型复数名称


class ClassMember(models.Model):
    """
    班级成员模型
    """
    course_semester = models.ForeignKey(CourseSemester, on_delete=models.CASCADE, verbose_name='所属课程学期',
                                        related_name='class_members', null=True, blank=True)
    class_obj = models.ForeignKey(Class, on_delete=models.CASCADE, verbose_name='所属班级', related_name='members')
    student = models.ForeignKey(UserInfo, on_delete=models.CASCADE, verbose_name='学生', related_name='students')
    role = models.CharField(max_length=20, verbose_name='角色', null=True, blank=True)
    joined_at = models.DateTimeField(verbose_name='加入时间', auto_now_add=True)  # 加入时间
    is_active = models.BooleanField(verbose_name='是否活跃', default=True)

    class Meta:
        db_table = 'class_member'  # 数据库表名
        default_permissions = ()  # 禁用默认权限
        permissions = (
            ("add_class_member", "班级成员添加"),
            ("view_class_member", "班级成员查询"),
            ("change_class_member", "班级成员修改"),
            ("delete_class_member", "班级成员删除"),  # 注意这里的权限名称
            ("batch_destroy_class_member", "班级成员批量删除")
        )

        # 同一课程学期下同一学生只能有一个班级成员
        unique_together = ('course_semester', 'student')


class ChapterCompletion(models.Model):
    """
    章节完成模型
    """
    student = models.ForeignKey(UserInfo, on_delete=models.CASCADE, related_name='chapter_completions',
                                verbose_name='学生')
    chapter = models.ForeignKey(Chapter, on_delete=models.CASCADE, related_name='student_completions',
                                verbose_name='章节')
    course_semester = models.ForeignKey(CourseSemester, on_delete=models.CASCADE,
                                        related_name='course_student_completions',
                                        verbose_name='课程学期', default=1)
    percentage = models.FloatField(null=True, blank=True, verbose_name='完成百分比')
    completed = models.BooleanField(default=False, verbose_name='是否完成')
    completed_at = models.DateTimeField(auto_now_add=True, verbose_name='完成时间')

    class Meta:
        unique_together = ('student', 'chapter', 'course_semester')  # 唯一约束
        db_table = 'chapter_completion'  # 数据库表名
        default_permissions = ()  # 禁用默认权限


class KnowledgePoint(models.Model):
    """
    知识点模型
    """
    name = models.CharField(max_length=400, verbose_name='知识点名称')
    description = models.TextField(verbose_name='知识点内容', null=True, blank=True)
    knowledge_type = models.CharField(max_length=50, verbose_name='知识点类型', null=True, blank=True)
    importance = models.IntegerField(verbose_name='重要程度', default=0, null=True, blank=True)
    metadata = models.JSONField(verbose_name='元数据', null=True, blank=True)
    created_at = models.DateTimeField(verbose_name='创建时间', auto_now_add=True)  # 创建时间
    updated_at = models.DateTimeField(verbose_name='更新时间', auto_now=True)  # 更新时间
    chapter = models.ForeignKey(Chapter, on_delete=models.CASCADE, verbose_name='所属章节',
                                related_name='knowledge_points',
                                null=True, blank=True)

    class Meta:
        db_table = 'knowledge_point'  # 数据库表名
        default_permissions = ()  # 禁用默认权限
        permissions = (
            ("add_knowledge_point", "知识点添加"),
            ("view_knowledge_point", "知识点查询"),
            ("change_knowledge_point", "知识点修改"),
            ("delete_knowledge_point", "知识点删除"),  # 注意这里的权限名称
        )
