from drf_spectacular.extensions import OpenApiAuthenticationExtension
from rest_framework import authentication


class DevUserAuthenticationScheme(OpenApiAuthenticationExtension):
    """
    开发者用户认证方案 - 自动登录为 ID=1 的用户，无需任何认证信息
    """
    target_class = 'abstract.swagger.DevUserAuthentication'
    name = 'DevUser'

    def get_security_definition(self, auto_schema):
        return {
            'type': 'apiKey',
            'in': 'header',
            'name': 'X-Dev-Auth',
            'description': '开发者认证 - 输入任意值（如 "dev" 或 "test"）即可自动登录为测试用户'
        }


class SessionAuthenticationScheme(OpenApiAuthenticationExtension):
    """
    Session 认证方案 - 使用 Django 的 Session 认证
    """
    target_class = 'rest_framework.authentication.SessionAuthentication'
    name = 'SessionAuth'

    def get_security_definition(self, auto_schema):
        return {
            'type': 'apiKey',
            'in': 'cookie',
            'name': 'sessionid',
            'description': '自动使用浏览器 session，无需手动输入'
        }


class CsrfAuthenticationScheme(OpenApiAuthenticationExtension):
    """
    CSRF 认证方案 - 确保 Swagger UI 发送正确的 CSRF 令牌
    """
    target_class = 'rest_framework.authentication.SessionAuthentication'
    name = 'CSRF'

    def get_security_definition(self, auto_schema):
        return {
            'type': 'apiKey',
            'in': 'header',
            'name': 'X-Csrftoken',
            'description': 'CSRF 保护 - 自动包含 CSRF 令牌'
        }


class DevUserAuthentication(authentication.BaseAuthentication):
    """
    开发者用户认证类 - 用于开发和测试环境
    只要请求头包含 X-Dev-Auth 字段（值可以是任意内容），就自动登录为 ID=1 的用户
    """

    def authenticate(self, request):
        # 检查是否有 X-Dev-Auth 头部
        auth_header = request.META.get('HTTP_X_DEV_AUTH')
        if not auth_header:
            return None

        try:
            from user.models import UserInfo
            user = UserInfo.objects.get(id=1)
            return (user, None)
        except Exception:
            return None


class FakeLoginAuthentication(authentication.BaseAuthentication):
    """
    假登录认证类 - 配合 FakeLoginView 使用
    检查用户是否已经通过 FakeLoginView 登录并有有效的 session
    """

    def authenticate(self, request):
        # 检查用户是否已经通过 session 认证
        if hasattr(request, 'user') and request.user.is_authenticated:
            return (request.user, None)
        return None
