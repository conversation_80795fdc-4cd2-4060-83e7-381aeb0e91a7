import json
import re
import pandas as pd
from django.utils import timezone
from django.http import JsonResponse
from typing import Union, List, Dict

from .prompt import *
from .models import *



with open(
    "./apps/exam/generate_format/Single_choice_question.json", "r", encoding="utf-8"
) as f:
    single_choice_response_json = json.load(f)
with open(
    "./apps/exam/generate_format/Multiple_choice_question.json", "r", encoding="utf-8"
) as f:
    multiple_choice_response_json = json.load(f)
with open(
    "./apps/exam/generate_format/Boolean_question.json", "r", encoding="utf-8"
) as f:
    boolean_choice_response_json = json.load(f)
with open("./apps/exam/generate_format/FAQ_question.json", "r", encoding="utf-8") as f:
    faq_response_json = json.load(f)
with open(
    "./apps/exam/generate_format/Blank_filling_question.json", "r", encoding="utf-8"
) as f:
    blank_filling_response_json = json.load(f)


def api_response(code, message, data=None, status=200):
    """
    封装 API 返回格式
    :param code: 状态码（自定义业务状态码）
    :param message: 返回信息
    :param data: 返回数据
    :param status: HTTP 状态码
    :return: JsonResponse
    """
    response_data = {
        'code': code,
        'message': message,
        'data': data if data is not None else {}
    }
    return JsonResponse(response_data, status=status)


def generate_quiz(data):
    """
    大模型AI生成题目
    tone 参数
    定义：tone 参数用于控制生成的问题或题目的语气或风格。
    作用：它决定了问题的表述方式和语言风格，使其适合不同的教学或评估场景。
    示例：
    简单：问题的表述简单明了，适合初学者。
    中等：问题的表述较为复杂，适合有一定基础的学生。
    困难：问题的表述复杂且具有挑战性，适合高级学生或专家。

    difficulty 参数
    定义：difficulty 参数用于控制生成的问题或题目的难度级别。
    作用：它决定了问题的复杂性和挑战性，帮助区分不同水平的学生。
    示例：
    简单：问题相对容易，适合初学者。
    中等：问题具有一定的复杂性，适合有一定基础的学生。
    困难：问题非常复杂且具有挑战性，适合高级学生或专家。

    """
    
    content_data = data.get('content','')
    single_question_count = data.get('single_question_count',0)
    multiple_question_count = data.get('multiple_question_count',0)
    boolean_question_count = data.get('boolean_question_count',0)
    faq_question_count = data.get('faq_question_count',0)
    blank_filling_question_count = data.get('blank_filling_question_count',0)
    tone = data.get('difficulty')
    difficulty = data.get('difficulty')
    direction = data.get('direction', '')  # 知识方向即考核目标，可选记忆、理解、应用、创造、分析、评价等

    knowledge_point_ids = data.get('knowledge_point_ids', [])
    if isinstance(knowledge_point_ids, str):
        try:
            knowledge_point_ids = json.loads(knowledge_point_ids)
        except json.JSONDecodeError:
            knowledge_point_ids = []

    try:
        knowledge_info = KnowledgePoint.objects.filter(
            id__in=knowledge_point_ids
        ).values('name', 'description')
        # 拼接成 "name：description \n name：description" 格式
        knowledge_name = '\n'.join([
            f"{point['name']}：{point['description'] or '暂无描述'}"
            for point in knowledge_info
        ])
        print(f"knowledge_name is {knowledge_name}")

    except Exception as e:
        knowledge_name = ''
        print(f"获取知识点名称失败: {str(e)}")
    
    print(f"knowledge_point_ids is {knowledge_point_ids}")

    
    # 去掉题目编号
    def remove_question_numbers(questions):
        return list(questions.values())

    try:
        all_responses = []
        if single_question_count > 0:
            single_choice_response = single_choice_quiz_chain(
                {
                    "text": content_data,
                    "number": single_question_count,
                    "knowledge_name": knowledge_name,
                    "tone": tone,
                    "difficulty": difficulty,
                    "direction": direction,
                    "response_json": single_choice_response_json
                }
            ).get("quiz",{})
            # print(single_choice_response)
            print("======================================")
            single_choice_response = remove_question_numbers(single_choice_response)
            for q in single_choice_response:
                # 手动添加字段
                q.update({
                    "resourcetype": "SingleChoiceQuestion",
                    "type": "单选题",
                    "knowledge_point_ids": knowledge_point_ids,
                    "difficulty": difficulty,
                    "direction": direction,
                    "is_ai_generated":True
                })
                q.pop('knowledge_name', None)  # 移除knowledge_name
            print("single choice question num is " + str(len(single_choice_response)))
            all_responses.extend(single_choice_response)
        if multiple_question_count > 0:
            multiple_choice_response = multiple_choice_quiz_chain(
                {
                    "text": content_data,
                    "number": multiple_question_count,
                    "knowledge_name": knowledge_name,
                    "tone": tone,
                    "difficulty": difficulty,
                    "direction": direction,
                    "response_json": multiple_choice_response_json
                }
            ).get("quiz", {})
            # print(multiple_choice_response)
            print("======================================")
            multiple_choice_response = remove_question_numbers(multiple_choice_response)
            for q in multiple_choice_response:
                # 手动添加字段
                q.update({
                    "resourcetype": "MultipleChoiceQuestion",
                    "type": "多选题",
                    "knowledge_point_ids":knowledge_point_ids,
                    "difficulty": difficulty,
                    "direction": direction,
                    "is_ai_generated":True
                })
                q.pop('knowledge_name', None)  # 移除knowledge_name
            print(
                "multiple choice question num is " + str(len(multiple_choice_response))
            )
            all_responses.extend(multiple_choice_response)
        if boolean_question_count > 0:
            boolean_response = boolean_quiz_chain(
                {
                    "text": content_data,
                    "number": boolean_question_count,
                    "knowledge_name": knowledge_name,
                    "tone": tone,
                    "difficulty": difficulty,
                    "direction": direction,
                    "response_json": boolean_choice_response_json
                }
            ).get("quiz", {})
            # print(boolean_response)
            print("======================================")
            boolean_response = remove_question_numbers(boolean_response)
            for q in boolean_response:
                # 手动添加字段
                q.update({
                    "resourcetype": "TrueOrFalseQuestion",
                    "type": "判断题",
                    "knowledge_point_ids": knowledge_point_ids,
                    "difficulty": difficulty,
                    "direction": direction,
                    "is_ai_generated":True
                })
                q.pop('knowledge_name', None)  # 移除knowledge_name
            print("boolean question num is " + str(len(boolean_response)))
            all_responses.extend(boolean_response)
        if faq_question_count > 0:
            faq_response = faq_quiz_chain(
                {
                    "text": content_data,
                    "number": faq_question_count,
                    "knowledge_name": knowledge_name,
                    "tone": tone,
                    "difficulty": difficulty,
                    "direction": direction,
                    "response_json": faq_response_json,
                }
            ).get("quiz", {})
            # print(faq_response)
            print("======================================")
            faq_response = remove_question_numbers(faq_response)
            for q in faq_response:
                # 手动添加字段
                q.update({
                    "resourcetype": "QuestionAndAnswerQuestion",
                    "type": "问答题",
                    "knowledge_point_ids": knowledge_point_ids,
                    "difficulty": difficulty,
                    "direction": direction,
                    "is_ai_generated":True
                })
                q.pop('knowledge_name', None)  # 移除knowledge_name
            print("faq question num is " + str(len(faq_response)))
            all_responses.extend(faq_response)
        if blank_filling_question_count > 0:
            blank_filling_response = blank_quiz_chain(
                {
                    "text": content_data,
                    "number": blank_filling_question_count,
                    "knowledge_name": knowledge_name,
                    "tone": tone,
                    "difficulty": difficulty,
                    "direction": direction,
                    "response_json": blank_filling_response_json,
                }
            ).get("quiz", {})
            # print(blank_filling_response)
            print("======================================")
            blank_filling_response = remove_question_numbers(blank_filling_response)
            for q in blank_filling_response:
                # 手动添加字段
                q.update({
                    "resourcetype": "FillInBlankQuestion",
                    "type": "填空题",
                    "knowledge_point_ids": knowledge_point_ids,
                    "difficulty": difficulty,
                    "direction": direction,
                    "is_ai_generated":True
                })
                q.pop('knowledge_name', None)  # 移除knowledge_name
            print("blank filling question num is " + str(len(blank_filling_response)))
            all_responses.extend(blank_filling_response)

        print("all question num is " + str(len(all_responses)))
        # all_responses = json.dumps(all_responses, ensure_ascii=False)
        print(all_responses)
        # 解析JSON字符串为Python对象
        # all_responses = json.loads(all_responses)

        # 确保所有的knowledge_point_ids都是列表类型
        for q in all_responses:
            if isinstance(q.get('knowledge_point_ids'), str):
                try:
                    q['knowledge_point_ids'] = json.loads(q['knowledge_point_ids'])
                except json.JSONDecodeError:
                    q['knowledge_point_ids'] = []
        return JsonResponse({"data": all_responses}, status=200, safe=False)
    except Exception as e:
        # print(traceback.format_exc())
        return JsonResponse({"error": str(e)}, status=500)


def parse_excel_data(df):
    """
    解析Excel数据为题目列表
    """
    # 将正确答案-answer转换为object类型
    df = df.astype({"正确答案": object})
    # 定义映射
    column_mapping = {
        "题干": "stem",
        "题型": "type",
        "选项": "options",
        "正确答案": "answer",
        "解析": "explanation",
        "知识方向": "direction",
        "知识点": "knowledge",
        "难度": "difficulty",
        "标签ID": "label_id",
        "可见状态": "visible",
        "是否AI生成": "is_ai_generated",
        "其他": "extra",
    }
    # 转换列名
    df = df.rename(columns=column_mapping)

    # 转换共性数据类型
    type_conversions = {
        "stem": str,
        "difficulty": int,
        "label_id": int,
        "direction": str,
        "knowledge": str,
        "explanation": str,
        "visible": str,
        "is_ai_generated": bool,
        "extra": str,
    }
    for col, dtype in type_conversions.items():
        if col in df.columns:
            df[col] = df[col].astype(dtype)

    # 选项分割并转换为列表
    df["options"] = df["options"].apply(
        lambda x: transf_options(x) if pd.notna(x) else []
    )
    # 转换个性化数据类型
    for index, row in df.iterrows():
        question_type = row["type"]
        answer = row["answer"]
        try:
            if question_type in ["单选题", "判断题", "问答题"]:
                df.at[index, "answer"] = str(answer) if pd.notna(answer) else ""
            elif question_type in ["多选题"]:
                # 多选题答案转换为列表
                if pd.notna(answer):
                    answer_clean = str(answer).strip("[]\"'")
                    answer_list = [
                        ans.strip() for ans in re.split(r"[，,\s]+", answer_clean)
                    ]
                    df.at[index, "answer"] = answer_list
                else:
                    df.at[index, "answer"] = []
            elif question_type in ["填空题"]:
                # 填空题答案转换为[{"blank": "填空1"}, {"blank":"填空2"} ...]形式
                if pd.notna(answer):
                    if isinstance(answer, str):
                        df.at[index, "answer"] = [
                            {"blank": item} for item in answer.split("|")
                        ]
                    elif isinstance(answer, list):
                        df.at[index, "answer"] = [{"blank": item} for item in answer]
                    else:
                        df.at[index, "answer"] = []
                else:
                    df.at[index, "answer"] = []

        except Exception as e:
            raise ValueError(f"第 {index + 1} 行数据处理失败: {str(e)}")

    # 转换为字典列表
    questions = df.to_dict(orient="records")
    return questions


def parse_colon_separated(text, item_name, key_name, value_name):
    """
    解析冒号分隔的字符串
    输入：
    text: 待解析的字符串，字符串类型
    item_name: 项名称，用于错误信息
    key_name: 键名称，用于返回的字典
    value_name: 值名称，用于返回的字典
    输出：
    解析后的字典列表，格式为[{"key":键, "value":值},...]
    """
    if pd.isna(text) or not str(text).strip():
        return []  # 如果文本为空，则返回空列表
    try:
        items = []
        parts = re.split(r"[，,\s]+", text.strip())  # 分割多个项
        for part in parts:
            part = part.strip()
            if not part:
                continue  # 跳过空项
            if ":" not in part:
                raise ValueError(f"{item_name}格式错误: {part}")
            key, value = part.split(":", 1)
            key = key.strip()
            value = value.strip()
            items.append({key_name: key, value_name: float(value)})
        return items
    except ValueError as e:
        raise ValueError(f"{item_name}处理失败: {str(e)}")


def transf_options(options):
    """
    分割选项内容，并转换为key-value形式
    输入：
    options: 选项内容，字符串类型，格式为"选项1.选项1内容|选项2.选项2内容|..."
    输出：
    json格式的选项内容，格式为[{"key":"选项1", "value":"选项1内容"},{"key":"选项2", "value":"选项2内容"}...]
    """
    if pd.isna(options) or not str(options).strip():
        return []  # 如果选项为空，则返回空列表

    parts = options.split("|")
    result_list = []  # 改为列表存储对象
    for part in parts:
        # 清理前后空格
        cleaned_part = part.strip()
        if not cleaned_part:
            continue  # 跳过空字符串
        # 创建选项对象
        option_obj = {"key": "", "value": ""}
        # 按遇到的第一个'.'分割键值对
        if "." in cleaned_part:
            try:
                key, value = cleaned_part.split(".", 1)
                option_obj["key"] = key.strip()  # 清理键的前后空格
                option_obj["value"] = value.strip()  # 清理值的前后空格
            except ValueError:
                # 如果分割失败，则将整个部分作为key, value为空
                option_obj["key"] = cleaned_part
        else:
            # 如果没有'.'，则将整个部分作为key, value为空
            option_obj["key"] = cleaned_part
        result_list.append(option_obj)

    return result_list


@transaction.atomic
def create_question_version(question):
    """
    创建试题版本记录
    """
    from .serializers import QuestionVersionSerializer  # 避免循环引用
    last_version = QuestionVersion.objects.filter(question=question).order_by('-version').first()
    question_data = QuestionVersionSerializer(
        data = {
            'content': {
                'id': question.id,
                'type': getattr(question, 'type', None),
                'stem': question.stem,
                'options': getattr(question, 'options', None),
                'answer': getattr(question, 'answer', None),
                'author_id': question.author.id if question.author else None,
                'difficulty': question.difficulty,
                'direction': question.direction,
                'knowledge':question.knowledge,
                'label_id': question.label_id,
                'explanation': question.explanation,
                'status': question.status,
                'visible': question.visible,
                'is_ai_generated': question.is_ai_generated,
                'extra': question.extra,
                'created_at': question.created_at.isoformat() if question.created_at else None,
                'updated_at': timezone.now().isoformat(),
            }
        }
    )
    question_data.is_valid(raise_exception=True)
    QuestionVersion.objects.create(
        question=question,
        version=last_version.version + 1 if last_version else 1,
        content=question_data.validated_data['content'],
        created_at=timezone.now()
    )
    return question_data.validated_data['content']

def parse_feedback(feedback: str, question_type: str) -> Union[str, list[Dict], List[str]]:
    """
    解析学生用户提交的答案内容
    :param feedback: 前端传递的字符串答案
    :param question_type: 题目类型
    :return: 解析后的答案格式
    """
    if not feedback:
        return [] if question_type in ['多选题', '填空题'] else ''
    feedback = feedback.strip()
    
    try:
        if question_type == '单选题':
            return feedback.upper()
        
        elif question_type == '多选题':
            # 多选题："A, B, C" -> ["A", "B", "C"]
            return [item.strip().upper() for item in re.split(r'\s*,\s*', feedback)]
        elif question_type == '判断题':
            return feedback.lower()
        elif question_type == '填空题':
            # 填空题："答案1,答案2" -> ["答案1", "答案2"]
            return [item.strip() for item in feedback.split(",") if item.strip()]
        elif question_type == '问答题':
            return feedback
        else:
            return feedback
    except Exception as e:
        raise ValueError(f"解析答案失败: {str(e)}")


