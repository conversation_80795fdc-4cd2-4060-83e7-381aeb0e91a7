import io
import os
import time

import openpyxl
from django.conf import settings
from django.http import HttpResponse
from openpyxl.styles import Alignment, Font, PatternFill, Border, Side
from urllib.parse import quote

def len_byte(value):
    length = len(value)
    utf8_length = len(value.encode('utf-8'))
    length = (utf8_length - length) / 2 + length
    return int(length)

def export_excel(request, field_data: list, data: list, file_name: str, file_path: str = settings.MEDIA_ROOT):
    """
    Excel导出（xlsx）
    :param request: 请求request
    :param data: 数据源
    :param field_data: 首行数据源（表头）
    :param file_path: 文件保存路径（默认保存在media路径）
    :param file_name: 文件保存名字
    :return:返回文件的下载url完整路径
    """
    file_name = file_name.replace('\n', '').replace('\r', '').strip()
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = 'Sheet1'

    # 表头样式
    header_font = Font(name='楷体', bold=True, color='000000')
    header_fill = PatternFill('solid', fgColor='C0C0C0')
    header_alignment = Alignment(horizontal='center', vertical='center')
    thin = Side(border_style="thin", color="00008B")
    header_border = Border(left=thin, right=thin, top=thin, bottom=thin)

    # 写表头
    for col, ele in enumerate(field_data, 1):
        cell = ws.cell(row=1, column=col, value=ele)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = header_border

    # 写内容
    content_font = Font(name='楷体', color='000000')
    content_fill = PatternFill('solid', fgColor='FFFFFF')
    content_alignment = Alignment(horizontal='center', vertical='center')
    content_border = Border(left=thin, right=thin, top=thin, bottom=thin)

    for row_idx, row_data in enumerate(data, 2):
        for col_idx, value in enumerate(row_data.values(), 1):
            cell = ws.cell(row=row_idx, column=col_idx, value=value)
            cell.font = content_font
            cell.fill = content_fill
            cell.alignment = content_alignment
            cell.border = content_border

    # 自动调整列宽
    for col_idx, ele in enumerate(field_data, 1):
        max_length = len(str(ele))
        for row_data in data:
            value = str(list(row_data.values())[col_idx - 1])
            if len(value) > max_length:
                max_length = len(value)
        ws.column_dimensions[openpyxl.utils.get_column_letter(col_idx)].width = max(14, min(max_length + 6, 36))

    month_time = time.strftime('%Y-%m-%d', time.localtime(time.time()))
    path_root = os.path.join(file_path, 'systemexport', month_time)
    if not os.path.exists(path_root):
        os.makedirs(path_root)
    path_name = os.path.join(path_root, file_name)
    if not path_name.endswith('.xlsx'):
        path_name += '.xlsx'

    # wb.save(path_name)
    # return getfulldomian(request) + settings.MEDIA_URL + 'export' + "/" + month_time + "/" + os.path.basename(path_name)
    # 创建一个内存文件对象
    output = io.BytesIO()
    wb.save(output)
    output.seek(0)

    # 创建HTTP响应对象
    response = HttpResponse(
        output.getvalue(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    # response['Content-Disposition'] = f'attachment; filename="{file_name}"'
    # 使用RFC 5987编码文件名
    encoded_filename = quote(file_name)
    response['Content-Disposition'] = f'attachment; filename="{encoded_filename}"; filename*=UTF-8\'\'{encoded_filename}'
    return response
def excel_to_data(file_url, field_data):
    """
    读取导入的excel文件（xlsx）
    :param file_url: 文件路径
    :param field_data: 首行数据源
    :return: 数据列表
    """
    import openpyxl
    file_path = os.path.join(settings.BASE_DIR.replace('\\', os.sep), *file_url.split(os.sep))
    wb = openpyxl.load_workbook(file_path)
    ws = wb.active
    tables = []
    for i, row in enumerate(ws.iter_rows(min_row=2, max_row=ws.max_row, max_col=len(field_data), values_only=True)):
        array = {}
        for index, ele in enumerate(field_data.keys()):
            cell_value = row[index]
            if isinstance(cell_value, float) and str(cell_value).split('.')[1] == '0':
                cell_value = int(str(cell_value).split('.')[0])
            if isinstance(cell_value, str):
                cell_value = cell_value.strip(' \t\n\r')
            array[ele] = cell_value
        tables.append(array)
    return tables

def get_full_domain(requests):
    host = '{scheme}://{host}'.format(scheme=requests.scheme, host=requests.get_host())
    return host