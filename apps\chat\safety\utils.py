import os

from flashtext import KeywordProcessor

from django.dispatch import Signal

from chat.models import KeywordRule

keyword_intercepted = Signal()
class SafetyFilter:
    def __init__(self):
        # self.keyword_processor = KeywordProcessor(case_sensitive=False)
        self.whitelist = set()
        self.load_rules()

    def load_rules(self):
        # 获取关键词模型
        # KeywordRule = apps.get_model('chat', 'KeywordRule')
        # WhitelistTerm = apps.get_model('chat', 'WhitelistTerm')

        # 清空现有规则
        self.keyword_processor = KeywordProcessor(case_sensitive=False)
        import os
        #加载同级目录的txt文件
        current_dir = os.path.dirname(os.path.abspath(__file__))
        file_path = os.path.join(current_dir, 'sensitive_words_lines.txt')
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        self.keyword_processor.add_keyword(line)

        # # 清空白名单

        # self.whitelist = set()
        #
        # # # 加载白名单
        # # for term in WhitelistTerm.objects.values_list('term', flat=True):
        # #     self.whitelist.add(term.strip().lower())
        #
        # 加载关键词规则

        keyword_list= list(KeywordRule.objects.values_list('keyword',flat=True))
        self.keyword_processor.add_keywords_from_list(keyword_list)


    # def is_whitelisted(self, text):
    #     """检查文本是否包含白名单术语"""
    #     lower_text = text.lower()
    #     return any(term in lower_text for term in self.whitelist)

    def filter_text(self, text,user_id,**kwargs):
        """过滤文本并返回结果"""
        if not text:
            return text, False

        # # 检查白名单
        # if self.is_whitelisted(text):
        #     return text, False, []

        # 查找关键词
        found_keywords = self.keyword_processor.extract_keywords(text, span_info=True)

        # 如果没有找到关键词，直接返回
        if not found_keywords:
            return text, False

        # 发送拦截信号
        if found_keywords:
            found_keywords=' '.join([k[0] for k in found_keywords])
            keyword_intercepted.send(sender=self.__class__, keywords=found_keywords, user_id=user_id,question=text)

        return found_keywords,True

        # # 处理关键词
        # should_block = False
        # # spans_to_remove = []
        # # modified_text = text
        #
        # # 按开始位置倒序处理（避免替换时索引变化）
        # for found in sorted(found_keywords, key=lambda x: x[1], reverse=True):
        #     keyword = found[0]
        #     start = found[1]
        #     end = found[2]
        #     info = found[3]  # 添加关键词时的附加信息
        #
        #     # 处理动作
        #     action = info.get('action', 'replace')
        #
        #     if action == 'block':
        #         should_block = True
        #         # 添加拦截日志等
        #     elif action == 'replace':
        #         replacement = info.get('value', '*' * len(keyword))
        #         # 替换关键词
        #         modified_text = modified_text[:start] + replacement + modified_text[end:]
        #         # 记录替换位置
        #         spans_to_remove.append({
        #             'original': keyword,
        #             'start': start,
        #             'end': end,
        #             'replaced': replacement,
        #             'kwargs':kwargs
        #         })
        #     elif action == 'warn':
        #         # 警��类处理，可以记录日志但不修改文本
        #         pass
        #
        # return modified_text, should_block, spans_to_remove

safety_filter = SafetyFilter()



if __name__=='__main__':
    current_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(current_dir, 'sensitive_words_lines.txt')
    # 读取所有行，去重后写回文件
    unique_lines = set()
    with open(file_path, 'r', encoding='utf-8') as f:
        i =1
        for line in f:
            i +=1
            line = line.strip()
            if line and len(line)!=1:
                unique_lines.add(line)
            if len(line)==2:
                print(i,line)

    # 覆盖写入去重后的内容
    with open(file_path, 'w', encoding='utf-8') as f:
        for line in unique_lines:
            f.write(line + '\n')
