from django.urls import path, include
from rest_framework import routers

from chat.views import ChatViewSet, ConversationViewSet, KeywordRuleViewSet, SummaryViewSet

router = routers.SimpleRouter()
router.register(r'conversation', ConversationViewSet)
router.register(r'chat', ChatViewSet, basename='chat')
router.register('keyword_rule', KeywordRuleViewSet, basename='keyword_rule')
urlpatterns = [

    # path('chat/', ChatViewSet.as_view({'post':'post','delete':'delete'})),
    # path('get_all_chats/', test_get_saverinfo, name='get_all_chats'),
    path('', include(router.urls)),
    path('summary/',SummaryViewSet.as_view()),

]
