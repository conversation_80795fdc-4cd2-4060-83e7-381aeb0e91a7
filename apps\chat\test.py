import os

from chat.chat_models import CustomChatQwQ

if __name__ == '__main__':
    LLM_BASE_URL_LESSON = os.environ.get('LLM_BASE_URL_LESSON', 'http://192.168.5.249:32450/v1')
    LLM_API_KEY = os.environ.get('LLM_API_KEY', 'model-manager_fa4435d373b1de21_89b25e72be482826832fdbd4a43fc6a1')
    LLM_NAME = os.environ.get('LLM_NAME', 'Qwen3-32B')

    llm = CustomChatQwQ(
        model=LLM_NAME,
        api_key=LLM_API_KEY,
        base_url=LLM_BASE_URL_LESSON,
        max_tokens=30000,
        temperature=0.6,
        streaming=True,
    )
    result= llm.invoke('介绍自己')
    print(result)