from django.db import models
from django.db import transaction
from django.utils import timezone
from polymorphic.models import PolymorphicModel

from course.models import *
from user.models import *


class BaseQuestion(PolymorphicModel):
    """
    基础试题模型
    """
    author = models.ForeignKey(
        UserInfo,
        on_delete=models.SET_NULL,  # 设置为null而不是级联删除
        verbose_name='创建者ID',
        null=True,  # 允许为空
        blank=True  # 允许为空
    )  # 关联到用户表的外键id
    stem = models.TextField(verbose_name='题干')  # 题干
    difficulty = models.IntegerField(verbose_name='难度', choices=((0, '简单'), (1, '中等'), (2, '困难')), default=0)  # 难度
    direction = models.CharField(verbose_name='知识方向', max_length=30, null=True, blank=True)  # 问题所属的知识方向
    knowledge = models.CharField(verbose_name='知识点', max_length=30, null=True, blank=True)  # 问题涉及的知识点
    label_id = models.IntegerField(verbose_name='分类标签', null=True, blank=True)  # 标签ID
    explanation = models.TextField(verbose_name='解析', null=True, blank=True)  # 解析
    status = models.CharField(verbose_name='状态', max_length=10, default='未发布', choices=(('未发布', '未发布'), ('审核中', '审核中'), ('已发布', '已发布'), ('已退回', '已退回'), ('跟随试卷', '跟随试卷')))  # 状态
    visible = models.CharField(verbose_name='可见状态', max_length=10, default='个人可见', choices=(('公开', '公开'), ('个人可见', '个人可见'), ('同单位教师可见', '同单位教师可见')))  # 可见范围
    is_ai_generated = models.BooleanField(verbose_name='是否AI生成', default=False)  # 是否AI生成
    extra = models.TextField(verbose_name='其他', null=True, blank=True)  # 额外信息
    is_deleted = models.BooleanField(verbose_name='是否删除', default=False)  # 软删除标记
    deleted_at = models.DateTimeField(verbose_name='删除时间', null=True, blank=True)  # 软删除时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')  # 创建时间
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')  # 更新时间
    knowledge_point = models.ManyToManyField(
        KnowledgePoint,
        through='QuestionKnowledgePoint',
        related_name='question_knowledge_points',
        verbose_name='关联知识点',
        blank=True
    )  # 从KnowledgePoint中反向查询

    # class Meta:
    #     abstract = True  # 抽象模型，不创建数据库表


class SingleChoiceQuestion(BaseQuestion):
    """
    单选题模型
    """
    type = models.CharField(verbose_name='题型', max_length=8, default='单选题')  # 题型
    options = models.JSONField(verbose_name='选项')  # 选项
    answer = models.CharField(verbose_name='正确答案', max_length=8)  # 正确答案

    class Meta:
        db_table = 'single_choice_question'  # 数据库表名
        verbose_name = '单选题'  # 模型名称
        verbose_name_plural = verbose_name  # 模型复数名称


class MultipleChoiceQuestion(BaseQuestion):
    """
    多选题模型
    """
    type = models.CharField(verbose_name='题型', max_length=8, default='多选题')  # 题型
    options = models.JSONField(verbose_name='选项')  # 选项
    answer = models.JSONField(verbose_name='正确答案')  # 正确答案

    class Meta:
        db_table = 'multiple_choice_question'  # 数据库表名
        verbose_name = '多选题'  # 模型名称
        verbose_name_plural = verbose_name  # 模型复数名称


class TrueOrFalseQuestion(BaseQuestion):
    """
    判断题模型
    """
    type = models.CharField(verbose_name='题型', max_length=8, default='判断题')  # 题型
    options = models.JSONField(verbose_name='选项')  # 选项
    answer = models.CharField(verbose_name='正确答案', max_length=8)  # 正确答案

    class Meta:
        db_table = 'true_or_false_question'  # 数据库表名
        verbose_name = '判断题'  # 模型名称
        verbose_name_plural = verbose_name  # 模型复数名称


class FillInBlankQuestion(BaseQuestion):
    """
    填空题模型
    """
    type = models.CharField(verbose_name='题型', max_length=8, default='填空题')  # 题型
    answer = models.JSONField(verbose_name='正确答案')  # 正确答案

    class Meta:
        db_table = 'fill_in_blank_question'  # 数据库表名
        verbose_name = '填空题'  # 模型名称
        verbose_name_plural = verbose_name  # 模型复数名称


class QuestionAndAnswerQuestion(BaseQuestion):
    """
    问答题模型
    """
    type = models.CharField(verbose_name='题型', max_length=8, default='问答题')  # 题型
    answer = models.TextField(verbose_name='正确答案')  # 正确答案
    class Meta:
        db_table = 'question_and_answer_question'  # 数据库表名
        verbose_name = '问答题'  # 模型名称
        verbose_name_plural = verbose_name  # 模型复数名称

class QuestionVersion(models.Model):
    """
    试题版本模型
    """
    question = models.ForeignKey(
        BaseQuestion,
        on_delete=models.SET_NULL,  # 设置为null而不是级联删除,
        verbose_name='试题ID',
        null=True,  # 允许为空
        blank=True  # 允许为空
    )  # 关联到试题模型的外键id
    version = models.IntegerField(verbose_name='版本号')  # 版本号
    content = models.JSONField(verbose_name='试题版本内容', default=dict, blank=True, null=True)  # 存储试题的完整JSON内容
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')  # 创建时间

    class Meta:
        db_table = 'question_version'  # 数据库表名
        verbose_name = '试题版本'  # 模型名称
        verbose_name_plural = verbose_name  # 模型复数名称
        unique_together = ('question', 'version')  # 唯一约束，确保每个试题的版本号是唯一的


class QuestionKnowledgePoint(models.Model):
    """
    试题知识点关联模型
    """
    question = models.ForeignKey(
        BaseQuestion,
        on_delete=models.CASCADE,
        verbose_name='问题ID',
        related_name='question_kp_relations'
    )  # 关联到问题模型的外键id，从BaseQuestion反向查询
    knowledge_point = models.ForeignKey(
        KnowledgePoint,
        on_delete=models.CASCADE,
        verbose_name='知识点ID',
        related_name='qs_knowledgepoint_relations'
    )  # 关联到知识点模型的外键id，从KnowledgePoint反向查询
    relevance = models.DecimalField(
        max_digits=24,
        decimal_places=6,
        verbose_name='相关度',
        null=True,  # 允许为空
        blank=True  # 允许为空
    )
    is_primary = models.BooleanField(
        verbose_name='是否主知识点',
        default=False,
        null=True,
        blank=True
    )
    class Meta:
        db_table = 'question_knowledge_point'  # 数据库表名
        verbose_name = '试题知识点'  # 模型名称
        verbose_name_plural = verbose_name  # 模型复数名称

class Paper(models.Model):
    """
    试卷模型
    """
    name = models.CharField(verbose_name='名称', max_length=30)  # 试卷名称
    direction = models.CharField(verbose_name='知识方向', max_length=30)  # 试卷所属的知识方向
    author = models.ForeignKey(
        UserInfo,
        on_delete=models.SET_NULL,
        verbose_name='作者',
        null=True,  # 允许为空
        blank=True  # 允许为空
    )  # 关联用户表的外键id 
    difficulty = models.CharField(verbose_name='难度', max_length=20)  # 试卷整体难度
    status = models.CharField(verbose_name='状态', default='未发布', max_length=10, choices=(('未发布', '未发布'), ('已发布', '已发布')))  # 试卷状态
    visible = models.CharField(verbose_name='可见状态', max_length=10, default='公开', choices=(('公开', '公开'), ('个人可见', '个人可见'), ('同单位教师可见', '同单位教师可见')))  # 试卷可见状态
    disrupt = models.BooleanField(verbose_name='是否打乱试题顺序', default=False)  # 是否打乱试题展示顺序
    extra = models.TextField(verbose_name='其他', blank=True, null=True)  # 其他信息
    is_deleted = models.BooleanField(verbose_name='是否删除', default=False)  # 软删除标志
    deleted_at = models.DateTimeField(verbose_name='删除时间', null=True, blank=True)  # 软删除时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')  # 创建时间
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')  # 更新时间
    question_version = models.ManyToManyField(
        QuestionVersion,
        verbose_name='试题版本',
        through='PaperQuestionVersion',
        related_name='paper_question_versions'
    ) # 关联到试题版本模型的多对多关系，从QuestionVersion反向查询
    
    class Meta:
        db_table = 'paper'  # 数据库表名
        verbose_name = '试卷'  # 模型名称
        verbose_name_plural = verbose_name  # 模型复数名称

class PaperManager(models.Model):
    """
    试卷管理日志表
    """
    user = models.ForeignKey(
        UserInfo,
        on_delete=models.CASCADE,
        verbose_name='用户ID',
    )  # 记录操作的用户，关联到用户表的外键id
    paper = models.ForeignKey(
        Paper,
        on_delete=models.CASCADE,
        verbose_name='试卷ID',
    )  # 关联试卷表的外键id
    behavior = models.CharField(
        verbose_name='行为',
        max_length=50,
        default='创建',
        choices=(('创建', '创建'), ('发布', '发布'), ('删除', '删除'), ('编辑', '编辑'))
    )
    time = models.DateTimeField(auto_now=True, verbose_name='操作时间') # 操作的时间戳
    result = models.TextField(verbose_name='结果', null=True, blank=True)
    extra = models.TextField(verbose_name='其他', null=True, blank=True)
    
    class Meta:
        db_table = 'paper_manager'  # 数据库表名
        verbose_name = '试卷管理日志'  # 模型名称
        verbose_name_plural = verbose_name  # 模型复数名称

class PaperQuestionVersion(models.Model):
    """
    试卷试题版本关联模型
    """
    paper = models.ForeignKey(
        Paper,
        related_name='paper_qv_relations',
        on_delete=models.SET_NULL,
        verbose_name='试卷ID',
        null=True,
        blank=True
    )  # 关联到试卷模型的外键id，从Paper反向查询
    question_version = models.ForeignKey(
        QuestionVersion,
        related_name='pp_questionversion_relations',
        on_delete=models.SET_NULL,
        verbose_name='试题版本ID',
        null=True,
        blank=True
    )  # 关联到试题版本模型的外键id，从QuestionVersion反向查询
    index = models.IntegerField(verbose_name='序号')  # 题目在试卷中的序号
    score = models.FloatField(verbose_name='分值')  # 题目在试卷中的分值
    attached = models.BooleanField(verbose_name='是否依附试卷', default=False)  # 题目是否跟随试卷状态

    class Meta:
        db_table = 'paper_question_version'  # 数据库表名
        verbose_name = '试卷试题版本关联'  # 模型名称
        verbose_name_plural = verbose_name  # 模型复数名称

class KnowledgeExam(models.Model):
    """
    考试模型
    """
    name = models.CharField(verbose_name='考试名称', max_length=30, default='考试')  # 考试名称
    author = models.ForeignKey(
        UserInfo,
        on_delete=models.SET_NULL,
        verbose_name='作者ID',
        null=True,
        blank=True
    )  # 关联用户表的外键id 
    paper = models.ForeignKey(
        Paper,
        on_delete=models.CASCADE,
        verbose_name='试卷ID',
        null=True,
        blank=True
    )  # 关联到试卷模型的外键id
    duration = models.IntegerField(verbose_name='考试时长', null=True, blank=True)  # 考试持续时间（分钟）
    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True)  # 考试开始时间
    end_time = models.DateTimeField(verbose_name='结束时间', null=True, blank=True)  # 考试结束时间
    pass_rate = models.FloatField(verbose_name='及格百分比', default=60, null=True, blank=True)  # 及格线百分比
    status = models.CharField(
        verbose_name='状态',
        max_length=10,
        default='未发布',
        choices=(('未发布', '未发布'), ('已发布', '已发布'))
    )  # 考试的状态
    introduction = models.CharField(verbose_name='说明', max_length=500, null=True, blank=True)  # 考试说明和要求
    is_deleted = models.BooleanField(verbose_name='是否删除', default=False)  # 软删除标志
    deleted_at = models.DateTimeField(verbose_name='删除时间', null=True, blank=True)  # 软删除时间
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')  # 创建时间
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')  # 更新时间
    class_id = models.ManyToManyField(
        Class,
        through='ClassKnowledgeExam',
        verbose_name='班级ID',
        related_name='class_exams',
    )  # 关联到班级模型的外键id，从Class反向查询
    course_semester = models.ManyToManyField(
        CourseSemester,
        through='CourseSemesterExam',
        verbose_name='课程ID',
        related_name='course_semester_exams',
        # default=1
    )  # 从CourseSemester反向查询

    class Meta:
        db_table = 'knowledge_exam'
        verbose_name = '考试'
        verbose_name_plural = verbose_name


class ClassKnowledgeExam(models.Model):
    """
    班级考试关联模型
    """
    class_id = models.ForeignKey(
        Class,
        on_delete=models.CASCADE,
        verbose_name='班级ID',
        related_name='class_em_relations',
    )  # 关联到班级模型的外键id，从Class反向查询
    exam_id = models.ForeignKey(
        KnowledgeExam,
        on_delete=models.CASCADE,
        verbose_name='考试ID',
        related_name='cls_exam_relations',
    )  # 关联到考试模型的外键id，从KnowledgeExam反向查询
    analysis_exams = models.TextField(verbose_name='考试分析结果', null=True, blank=True)  # 考试分析结果
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')  # 创建时间
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')  # 更新时间

    class Meta:
        db_table = 'class_knowledge_exam'
        verbose_name = '班级考试关联'
        verbose_name_plural = verbose_name

class CourseSemesterExam(models.Model):
    """
    课程考试关联模型
    """
    course_semester = models.ForeignKey(
        CourseSemester,
        on_delete=models.CASCADE,
        verbose_name='课程学期ID',
        related_name='coursesemester_em_relations',
    )  # 关联到课程模型的外键id，从CourseSemester反向查询
    exam_id = models.ForeignKey(
        KnowledgeExam,
        on_delete=models.CASCADE,
        verbose_name='考试ID',
        related_name='cs_exam_relations',
    )  # 关联到考试模型的外键id，从KnowledgeExam反向查询
    added_by = models.ForeignKey(
        UserInfo,
        on_delete=models.CASCADE,
        verbose_name='添加人ID',
    )  # 关联到用户模型的外键id
    added_at = models.DateTimeField(auto_now_add=True, verbose_name='添加时间')
    usage_type = models.CharField(
        verbose_name='用途类型',
        max_length=20,
        default='homework',
        choices=(
            ('homework', '作业'),
            ('exam', '考试'))
    )  # 考试的用途类型
    order = models.IntegerField(verbose_name='排序', default=0, null=True, blank=True)  # 显示顺序
    notes = models.TextField(verbose_name='教师备注', blank=True, null=True)  # 教师对该考试的备注说明
    analysis_course_exams = models.TextField(verbose_name='课程分析结果', null=True, blank=True)  # 课程分析结果

    class Meta:
        db_table = 'course_semester_exam'
        verbose_name = '课程考试关联'
        verbose_name_plural = verbose_name

class UserTakePartExam(models.Model):
    """
    用户参加考试记录模型
    """
    user = models.ForeignKey(
        UserInfo,
        on_delete=models.CASCADE,
        verbose_name='用户ID'
    )  # 关联到用户模型的外键id
    exam_id = models.ForeignKey(
        KnowledgeExam,
        on_delete=models.SET_NULL,
        verbose_name='考试ID',
        null=True,
        blank=True
    )  # 关联到考试模型的外键id
    start_time = models.DateTimeField(verbose_name='开始时间', null=True, blank=True)  # 用户开始答题时间
    end_time = models.DateTimeField(verbose_name='结束时间', null=True, blank=True)  # 用户提交答案时间
    take_part_status = models.CharField(
        verbose_name='状态',
        max_length=10,
        default='未提交',
        choices=(('未提交', '未提交'), ('阅卷中', '阅卷中'), ('已阅卷', '已阅卷'), ('已发布', '已发布'))
    )  # 用户参加考试的状态
    total_score = models.FloatField(verbose_name='总分数', default=0, null=True, blank=True)  # 用户参加考试的总分数

    class Meta:
        db_table = 'user_take_part_exam'
        verbose_name = '用户参加考试记录'
        verbose_name_plural = verbose_name

class UserExamQuestionFeedback(models.Model):
    """
    用户考试答题反馈表
    """
    exam_id = models.ForeignKey(
        KnowledgeExam,
        on_delete=models.SET_NULL,
        verbose_name='考试ID',
        null=True,
        blank=True
    )  # 关联到考试模型的外键id
    question_version = models.ForeignKey(
        QuestionVersion,
        on_delete=models.CASCADE,
        verbose_name='试题版本ID',
    )  # 关联到试题版本模型的外键id
    user = models.ForeignKey(
        UserInfo,
        on_delete=models.CASCADE,
        verbose_name='用户ID',
    )  # 关联到用户模型的外键id
    user_take_exam = models.ForeignKey(
        UserTakePartExam,
        on_delete=models.CASCADE,
        verbose_name='用户参加考试记录ID',
        default=1
    )  # 关联到用户参加考试记录的外键id
    feedback = models.TextField(verbose_name='用户答题内容', null=True, blank=True)  # 用户提交的答案
    is_correct = models.BooleanField(verbose_name='是否答题正确', null=True, blank=True)  # 答案是否正确
    is_judged = models.BooleanField(verbose_name='是否已评阅', default=False)  # 是否已经评阅（针对主观题）
    get_score = models.FloatField(verbose_name='得分', default=0, null=True, blank=True)  # 该题获得的分数
    extra = models.TextField(verbose_name='其他内容', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')  # 创建时间
    initial_score = models.CharField(verbose_name='初评分数', max_length=10, null=True, blank=True) # AI阅卷的初评分数
    calibrated_score = models.CharField(verbose_name='校准分数', max_length=10, null=True, blank=True) # AI阅卷校准后的分数
    justification = models.CharField(verbose_name='评分依据', max_length=200, null=True, blank=True) # 教师/AI阅卷的评分理由（评语）
    error_types = models.CharField(verbose_name='错误类型', max_length=100, null=True, blank=True) # AI阅卷给出的错误类型

    
    class Meta:
        db_table = 'user_exam_question_feedback'
        verbose_name = '用户考试答题反馈'
        verbose_name_plural = verbose_name