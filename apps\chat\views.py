import os
from typing import List

from uuid import uuid4
from django.db import transaction
from django.db.transaction import atomic
from django.http import StreamingHttpResponse
from django_redis import get_redis_connection
from drf_spectacular.utils import extend_schema
from langchain_core.messages import HumanMessage, AIMessage
from numpy.core.defchararray import rstrip
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.request import Request
from rest_framework.views import APIView
from rest_framework.viewsets import GenericViewSet

from abstract.export_excel import export_excel
from abstract.upload import file_upload
from core.utils import SuccessResponse, ErrorResponse
from core.viewset import CustomModelViewSet
from chat.agents.base import ChatDocumentSpliter, SummaryDocumentSpliter

from chat.agents.rag_chat_agant import ChatAgent, create_title_with_llm
from chat.constants import CHUNK_DETAIL, CHUNK_DETAIL_REDIS_KEY, SENSITIVE_RESPONSE_CONTENT
from chat.models import Conversation, ChatMessage, CommonFile, KeywordRule
from chat.safety.utils import safety_filter
from chat.serializers import ChatMessageSerializer, ConversationSerializer, UpdateConversationSerializer, \
    ExportChatHistorySerializer, KeywordRuleSerializer, ChatCreateSerializer, CommonFileSerializer, \
    KeywordRuleDeleteSerializer
from chat.streams import StreamChunk, PartsHandler, get_proto_content, StreamProcessor, append_proto_instance, \
    CatchableStreamProcessor, InterruptibleStreamingHttpResponse
from knowledge_base.models import KnowledgeBase
from knowledge_base.views import HEADERS, BASE_URL
from user.permissions import IsAdminRoleUser


class ConversationViewSet(CustomModelViewSet):
    permission_classes = [IsAuthenticated]
    """
    会话
    """
    serializer_class = ConversationSerializer
    update_serializer_class = UpdateConversationSerializer
    queryset = Conversation.objects.all()
    ordering_fields = ['create_at','id','update_at']
    ordering = ('-update_at',)
    @extend_schema(
        operation_id='retrieve_conversation_messages',
        summary='获取会话消息列表',
        description='根据会话ID获取该会话下的所有消息记录',
        parameters=[
            {
                'name': 'pk',
                'in': 'path',
                'required': True,
                'type': 'string',
                'description': '会话主键ID'
            }
        ],
        responses={
            200: ChatMessageSerializer(many=True),
            404: {'description': '会话不存在'}
        }
    )
    def retrieve(self, request, *args, **kwargs):
        """
        获取消息列表
        Args:
            pk 会话主键
        Returns:
            list[Msg]
                id,content,role,times
        """
        id = kwargs.get('pk')
        qs = ChatMessage.objects.filter(conversation__id=id)
        serializer = ChatMessageSerializer(qs, many=True)
        return SuccessResponse(serializer.data)

    @extend_schema(
        operation_id='list_user_conversations',
        summary='获取用户会话列表',
        description='获取当前用户的所有会话列表，按创建时间排序',
        responses={
            200: ConversationSerializer(many=True),
        }
    )
    def list(self, request, *args, **kwargs):
        """
        获取个人会话列表
        Returns:
            list[Conversation]
                id,title,user_id,times....
        """
        self.queryset = self.queryset.filter(user=request.user)
        return super().list(request, *args, **kwargs)

    @extend_schema(
        operation_id='create_conversation',
        summary='创建新会话',
        description='创建一个新的会话',
        request=ConversationSerializer,
        responses={
            201: ConversationSerializer,
            400: {'description': '请求参数错误'}
        }
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @extend_schema(
        operation_id='update_conversation',
        summary='更新会话信息',
        description='更新会话的标题等信息',
        request=UpdateConversationSerializer,
        responses={
            200: ConversationSerializer,
            400: {'description': '请求参数错误'},
            404: {'description': '会话不存在'}
        }
    )
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @extend_schema(
        operation_id='partial_update_conversation',
        summary='部分更新会话信息',
        description='部分更新会话的标题等信息',
        request=UpdateConversationSerializer,
        responses={
            200: ConversationSerializer,
            400: {'description': '请求参数错误'},
            404: {'description': '会话不存在'}
        }
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    @extend_schema(
        operation_id='delete_conversation',
        summary='删除除会话',
        description='删除指定的会话及其所有消息记录',
        responses={
            204: {'description': '删除成功'},
            404: {'description': '会话不存在'}
        }
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)





class ChatViewSet(GenericViewSet):
    """
    对话
    """
    create_serializer_class = ChatCreateSerializer
    permission_classes = [IsAuthenticated]
    @extend_schema(
        request=ChatCreateSerializer,
        operation_id='create_chat',
        summary='发起问答 |普通对话|知识库对话|文件对话',
        description="""
        总：创建新对话或在已有会话中继续对话
        区分原则：根据是否包含知识库id或file_path判断当前是普通对话还是知识库对话还是文件问答
        特殊说明一：文件上传时【upload接口】需要提供会话id，之后会根据会话id判断是否为文件问答，因此之后不再需要传递file_path，传了也没事
        特殊说明二：知识库对话时，需要一直传递dataset_ids，否则根据是否上传过文档，判断是普通对话，还是文件对话。
        入参说明：
            conversation_id会话id，必填 （string类型）
            question，用户最新的问题，必填 （string类型）
            dataset_ids:知识库id集合，选填 （集合类型）
            course_id:等同于知识库对话
            document_ids：知识库文件id集合（集合类型）
            file_path:文件路径，选填 （string类型）【通过upload接口上传文件后返回的file_path】
            open_stream:是否开启流式，选填，默认true
            enable_thinking：是否开启思考，选填，默认true
            model_name:模型名称，选填，默认：Qwen3-32B
        """,
        responses={200: None}  # 使用 None 表示流式响应
    )
    @transaction.atomic
    def create(self, request, *args, **kwargs):
        self.serializer_class = self.create_serializer_class
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        conversation_id = serializer.validated_data["conversation_id"]
        question = serializer.validated_data["question"]
        title = question
        stream_protocol = serializer.validated_data.get("stream_protocol",'data')
        datasets = serializer.validated_data.get("dataset_ids",None)
        dataset_ids = []
        if datasets:
            dataset_ids = [dataset.id for dataset in datasets]

        document_ids = serializer.validated_data.get("document_ids",[])
        course_id=serializer.validated_data.get('course_id',None)
        if course_id:
            dataset_ids = list(KnowledgeBase.objects.filter(course_kb_links=course_id.id).values_list('id', flat=True))
            if not dataset_ids:
                print("该课程没有绑定知识库")
        file_path = serializer.validated_data.get("file_path",None)
        open_stream = serializer.validated_data.get("stream", True)
        enable_thinking = serializer.validated_data.get("enable_thinking", True)
        model_name = serializer.validated_data.get("model_name", "Qwen3-32B")
        find_keyword,match=safety_filter.filter_text(question,request.user.id)


        if match:
            def inner():
                for item in [SENSITIVE_RESPONSE_CONTENT]:
                    yield StreamChunk.text(item)
            return self.wrap_response(StreamingHttpResponse(streaming_content=inner()))


        # 创建agent -> 指定模型名称
        agent = ChatAgent(model=model_name)
        # 创建会话 或补充title
        self.conversation = Conversation.objects.filter(pk=conversation_id).first()
        #将标题融入返回流中
        fix_title_to_stream=False
        if (created := self.conversation is None) or self.conversation.title=='':
            if created:
                self.conversation = Conversation.objects.create(pk=conversation_id, title=title[:50], user=request.user, )
            fix_title_to_stream=True
            create_title_with_llm.delay(question,conversation_id)#异步生成标题
        # 创建用户消息
        ChatMessage(conversation=self.conversation, content=question, role='HUMAN',parts=[PartsHandler.create_text_part(question)] ).save()
        # 配置memory
        config = {"configurable": {"thread_id": f'common_chat:{request.user.id}:{self.conversation.id}'}}
        messages=self.build_history_message(agent,self.conversation,created, config)

        history_info={}
        #如果有文件问答历史，则接下来需要走文件问答
        if not created and self.conversation.files:
            history_files= list(self.conversation.files.values_list('id', flat=True))
            if history_files:
                history_info['file_query']=True
                history_info['file_id']=','.join(history_files)

        # 调用工作流

        params ={
            "messages": messages,
            "headers": os.environ.get("RAG_HEADERS", HEADERS),
            "config": config,
            "retrieve_base_url": os.environ.get('RAG_BASE_URL', BASE_URL),  # todo 同上
            "question": question,
            "dataset_ids": dataset_ids,
            "document_ids": document_ids,
            "file_path": file_path,
            "conversation_id": conversation_id,
            "history_info": history_info,
            "rewrite": True,
            "enable_thinking":enable_thinking,
            "model_name":model_name
        }
        model_streams = agent.graph.stream(params, config=config, stream_mode='messages') if open_stream else agent.graph.invoke(params, config=config, stream_mode='messages')

        # 保存助手的回复
        def create_ai_msg(ai_messages_chunk):
            last_type=None
            last_instance=None
            instances=[]
            for item in ai_messages_chunk:
                cur_type=item.get('type', None)
                if cur_type=='source' or not last_type ==cur_type:
                    last_type=cur_type
                    last_instance=item
                    instances.append(last_instance)
                else:
                    append_proto_instance(last_instance,item)

            ai_message = ''.join([get_proto_content(item) for item in instances])

            ai_msg = {'conversation_id': conversation_id,'content': ai_message,'role': 'Assistant','parts':instances}
            ChatMessage.objects.create(**ai_msg)
            return self.conversation.title if fix_title_to_stream else None

        # stream_processor = StreamProcessor(agent=agent,stream=model_streams,callback= create_ai_msg,enable_thinking=enable_thinking)
        # proto_stream_generator = stream_processor.process_with_thinking_mix()
        stream_processor = CatchableStreamProcessor(agent=agent, stream=model_streams, callback=create_ai_msg,
                                                    enable_thinking=enable_thinking,request=request)
        resp = InterruptibleStreamingHttpResponse(streaming_content=stream_processor,processor=stream_processor)
        # resp.headers['content-type'] = 'application/octet-stream; charset=utf-8'
        resp.headers['content-type'] = 'text/event-stream; charset=utf-8'
        resp.headers['x-vercel-ai-data-stream'] = 'v1'
        return resp
        # return self.wrap_response(resp)

    def wrap_response(self, response):
        response.headers['content-type'] = 'application/octet-stream; charset=utf-8'
        response.headers['x-vercel-ai-data-stream'] = 'v1'
        return response

    def build_history_message(self,agent,conversation,created,config):
        messages=[]
        if not created:
            try:
                if not agent.check_pointer.get_tuple(config):
                    raise Exception("no checkpoint")
            except Exception as e:
                history = ChatMessage.objects.filter(conversation=conversation).order_by('-create_at')[:20][::-1]
                for item in history:
                    if item.role.lower() == 'human':
                        messages.append(HumanMessage(item.content))
                    elif item.role.lower()=='assistant':
                        messages.append(AIMessage(item.content))
        return  messages

    @extend_schema(
        operation_id='view_chunk_detail',
        summary='查看文档块详情',
        description='根据文档ID、块ID和UUID获取存储在Redis中的文档块详细信息',
        parameters=[
            {
                'name': 'doc_id',
                'in': 'path',
                'required': True,
                'type': 'string',
                'description': '文档ID'
            },
            {
                'name': 'chunk_id',
                'in': 'path',
                'required': True,
                'type': 'string',
                'description': '文档块ID'
            },
            {
                'name': 'uuid',
                'in': 'path',
                'required': True,
                'type': 'string',
                'description': '唯一标识符'
            }
        ],
        responses={200: dict}  # 使用dict表示返回的JSON响应
    )
    @action(
        methods=['GET'],
        detail=False,
        url_path='view_chunk_detail/(?P<doc_id>[^/.]+)/(?P<chunk_id>[^/.]+)/(?P<uuid>[^/.]+)'
    )
    def view_chunk_detail(self, request: Request, *args, **kwargs):
        doc = kwargs['doc_id']
        chunk_id = kwargs['chunk_id']
        uuid=kwargs['uuid']
        conn = get_redis_connection(CHUNK_DETAIL)
        detail = conn.get(CHUNK_DETAIL_REDIS_KEY.format(doc, chunk_id,uuid))
        return SuccessResponse(detail)

    @extend_schema(summary="导出聊天记录，conversation_id：会话id",)
    @action(methods=['GET'], detail=False, url_path='export/(?P<conversation_id>[^/.]+)',url_name='export_excel')
    def export_execl(self, request,*args, **kwargs):
        field_data = ['序号', '内容', '类型', '创建时间']
        conversation_id=kwargs['conversation_id']
        queryset = ChatMessage.objects.filter(conversation__id=conversation_id)
        id_map={ obj.pk:idx+1 for idx,obj in enumerate(queryset)}
        data = ExportChatHistorySerializer(queryset, many=True,context={'id_map':id_map}).data
        title=Conversation.objects.filter(pk=conversation_id).values_list('title',flat=True)[0]
        response = export_excel(request, field_data, data, f'{title}.xls')
        return response

    @extend_schema(
        operation_id='upload_chat_file',
        summary='上传聊天文件',
        description='上传文件到聊天会话中，支持文件上传并进行处理，可用于文件问答',
        request={
            'multipart/form-data': {
                'type': 'object',
                'properties': {
                    'conversation_id': {
                        'type': 'string',
                        'description': '会话ID，如不提供则创建新会话'
                    },
                    'chunk_size': {
                        'type': 'integer',
                        'description': '文件分块大小，默认为2000'
                    },
                    'source_type': {
                        'type': 'string',
                        'description': '文件来源类型，默认为conversion,可选summary用于摘要提取'
                    },
                    'file': {
                        'type': 'array',
                        'items':{
                            'type': 'string',
                            'format': 'binary',
                        },
                        'description':'要上传的多个文件'
                    }
                },
                'required': ['file']
            }
        },
        responses={200: CommonFileSerializer(many=True)}
    )
    @action(methods=['POST'],detail=False,url_path='upload')
    @atomic
    def upload(self,request,*args,**kwargs):

        conversation_id=request.data.get('conversation_id',None)
        # conversation_id='Zxo07IGPwSdpQe9Z'#todo 测试数据
        chunk_size= request.data.get('chunk_size',2000)
        source_type=request.data.get('source_type',None)
        if not source_type:
            source_type='conversion'
        #todo 测试数据
        # conversation_id='6rpk7xoZHT3OzMz8'
        #todo 以上是测试数据
        conversation=None
        dir_path=f'temp/{request.user.id}/{conversation_id}/' if source_type=='conversion' else f'temp/{request.user.id}/{source_type}/'
        if source_type=='conversion':
            conversation = Conversation.objects.filter(pk=conversation_id).first()
            if conversation is None:
                conversation = Conversation.objects.create(pk=conversation_id, title='', user=request.user)

        result,file_infos=file_upload(request, dir_path)
        batch=[]
        for file_item in file_infos:
            file_path=file_item[0]
            file_name=file_item[1]
            file_origin_name=file_item[2]
            file_size=file_item[3]
            file_type=file_item[4]
            pk=str(uuid4())

            cf= CommonFile(pk=pk, conversation=conversation, file_name=file_name, file_origin_name=file_origin_name,
                           file_path=file_path, file_size=file_size, file_type=file_type, source_type=source_type)
            batch.append(cf)
        CommonFile.objects.bulk_create(batch)
        _ =[ChatDocumentSpliter(item).load() for item in batch]
        serializer=CommonFileSerializer(instance=batch, many=True)
        return SuccessResponse(serializer.data)



class KeywordRuleViewSet(CustomModelViewSet):
    """
    关键词
    keyword : icontains、exact
    """
    serializer_class = KeywordRuleSerializer
    queryset = KeywordRule.objects.all()
    permission_classes = [IsAdminRoleUser]
    filterset_fields = {
        'keyword':['exact', 'icontains'],
    }

    @extend_schema(
        summary="关键词列表",
        description="获取关键词的列表信息：keyword__icontains",
        responses={
            200: KeywordRuleSerializer(many=True),
        },
        tags=['消息围栏']
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @extend_schema(
        summary="创建关键词",
        description="创建多个新的关键词",
        request=KeywordRuleSerializer,
        responses={
            200: KeywordRuleSerializer,
        },
        tags=['消息围栏']
    )
    def create(self, request, *args, **kwargs):
        keyword= request.data['keyword']
        map = [ {"keyword":k.strip()} for k in keyword.split('\n') if k.strip() != '']
        arr =[item['keyword'] for item in map]
        serializer= KeywordRuleSerializer(data=map,many=True)
        valid = serializer.is_valid()
        if not valid:
            return ErrorResponse(msg='存在相同名称的关键字')
        instances= [KeywordRule(**item) for item in serializer.validated_data]
        KeywordRule.objects.bulk_create(instances)

        safety_filter.keyword_processor.add_keywords_from_list(arr)
        return SuccessResponse()

    @extend_schema(
        summary="删除关键词",
        description="删除指定的关键词",
        responses={
            200: {'description': '删除成功'},
        },
        tags=['消息围栏']
    )
    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        safety_filter.keyword_processor.remove_keyword(instance.keyword)
        return super().destroy(request, *args, **kwargs)

    @extend_schema(
        summary="批量删除关键词",
        description="批量删除指定的关键词, pks=[id2,id2]",
        tags=['消息围栏']
    )
    @action(methods=['delete'], detail=False)
    def multiple_delete(self, request, *args, **kwargs):
        return super().multiple_delete(request, *args, **kwargs)


class SummaryViewSet(APIView):
    """
    摘要提取
    """

    @extend_schema(
        operation_id='extract_summary',
        summary='摘要提取',
        description='根据文件路径提取文档摘要',
        request={
            'application/json': {
                'type': 'object',
                'properties': {
                    'file_path': {
                        'type': 'string',
                        'description': '文件路径'
                    }
                },
                'required': ['file_path']
            }
        },
        responses={200: dict}
    )
    def post(self, request, *args, **kwargs):
        file_path = request.data.get('file_path')
        original,summary= SummaryDocumentSpliter(file_path=file_path).load()
        return SuccessResponse({'original': original, 'summary': summary})
