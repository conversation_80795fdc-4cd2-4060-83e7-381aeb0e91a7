from django.db import models
from rest_framework import serializers
from rest_polymorphic.serializers import PolymorphicSerializer

from .models import *
from .signals import exam_submitted, exam_analysis
from .utils import create_question_version

class DynamicAnswerField(serializers.Field):
    """
    动态答案字段
    """
    def __init__(self, **kwargs):
        self.question_type = kwargs.pop('question_type', None)
        super().__init__(**kwargs)

    def get_question_type(self):
        """
        统一获取题型方法
        """
        # 从自身属性获取
        if self.question_type:
            return self.question_type
        # 从父序列化器的实力属性获取
        if hasattr(self.parent, 'initial_data'):
            return self.parent.initial_data.get('type')
        # 从上下文获取
        return self.context.get('question_type')
    
    def to_internal_value(self, data):
        """
        反序列化时根据题型验证答案类型
        """
        question_type = self.get_question_type()
        if not question_type:
            raise serializers.ValidationError("题型（question_type）未设置，无法校验答案格式！")
        if question_type in ['单选题', '判断题', '问答题']:
            if not isinstance(data, str):
                raise serializers.ValidationError('答案必须是字符串')
            return data
        elif question_type in ['多选题', '填空题']:
            if not isinstance(data, list):
                raise serializers.ValidationError('答案必须是列表')
            return data
        else:
            raise serializers.ValidationError(f'不支持的题型：{self.question_type}')
    
    def to_representation(self, value):
        """
        序列化时返回原始值
        """
        return value

class BaseQuestionSerializer(serializers.ModelSerializer):
    """
    基础题目序列化器
    """
    class Meta:
        model = BaseQuestion
        fields = [
            'id', 'author', 'stem', 'difficulty',
            'direction', 'knowledge', 'label_id',
            'explanation', 'status', 'visible', 'is_ai_generated',
            'extra', 'is_deleted', 'created_at', 'deleted_at',
            'updated_at'
        ]

# class AIQuestionSerializer(serializers.ModelSerializer):
#     """
#     AI生成题目序列化器
#     """
#     aiid = serializers.CharField(required=False)
#     knowledge_point_ids = serializers.PrimaryKeyRelatedField(
#         queryset=KnowledgePoint.objects.all(),
#         source='knowledge_points',
#         many=True,
#         required=False,
#         allow_null=True,
#         write_only=True,
#         default=[]
#     ) # 试题知识点表的知识点id
#     type = serializers.CharField(write_only=True, required=False)
#     options = serializers.ListField(required=False) # 单选题/多选题/判断题使用
#     answer = DynamicAnswerField() # 使用自定义字段处理动态类型
#     resourcetype = serializers.CharField(required=False)


#     class Meta:
#         model = BaseQuestion
#         fields = [
#             'aiid', 'stem', 'type', 'options', 'answer',
#             'knowledge_point_ids', 'resourcetype', 'difficulty',
#             'direction', 'explanation', 'is_ai_generated'
#         ]


class SingleChoiceQuestionSerializer(BaseQuestionSerializer):
    """
    单选题序列化器
    """
    type = serializers.CharField(read_only=True)
    options = serializers.JSONField()
    answer = serializers.CharField()

    class Meta(BaseQuestionSerializer.Meta):
        model = SingleChoiceQuestion
        fields = BaseQuestionSerializer.Meta.fields + ['type', 'options', 'answer']


class MultipleChoiceQuestionSerializer(serializers.ModelSerializer):
    """
    多选题序列化器
    """
    type = serializers.CharField(read_only=True)
    options = serializers.JSONField()
    answer = serializers.ListField()

    class Meta(BaseQuestionSerializer.Meta):
        model = MultipleChoiceQuestion
        fields = BaseQuestionSerializer.Meta.fields + ['type', 'options', 'answer']


class TrueOrFalseQuestionSerializer(serializers.ModelSerializer):
    """
    判断题序列化器
    """
    type = serializers.CharField(read_only=True)
    options = serializers.JSONField()
    answer = serializers.CharField()

    class Meta(BaseQuestionSerializer.Meta):
        model = TrueOrFalseQuestion
        fields = BaseQuestionSerializer.Meta.fields + ['type', 'options', 'answer']

class FillInBlankQuestionSerializer(serializers.ModelSerializer):
    """
    填空题序列化器
    """
    type = serializers.CharField(read_only=True)
    answer = serializers.JSONField()

    class Meta(BaseQuestionSerializer.Meta):
        model = FillInBlankQuestion
        fields = BaseQuestionSerializer.Meta.fields + ['type', 'answer']

        

class QuestionAndAnswerQuestionSerializer(serializers.ModelSerializer):
    """
    问答题序列化器
    """
    type = serializers.CharField(read_only=True)
    answer = serializers.CharField()

    class Meta(BaseQuestionSerializer.Meta):
        model = QuestionAndAnswerQuestion
        fields = BaseQuestionSerializer.Meta.fields + ['type', 'answer']

class QuestionPolymorphicSerializer(PolymorphicSerializer):
    model_serializer_mapping = {
        BaseQuestion: BaseQuestionSerializer,
        SingleChoiceQuestion: SingleChoiceQuestionSerializer,
        MultipleChoiceQuestion: MultipleChoiceQuestionSerializer,
        TrueOrFalseQuestion: TrueOrFalseQuestionSerializer,
        FillInBlankQuestion: FillInBlankQuestionSerializer,
        QuestionAndAnswerQuestion: QuestionAndAnswerQuestionSerializer,
    }

class QuestionVersionSerializer(serializers.ModelSerializer):
    """
    题目版本序列化器
    """
    question = QuestionPolymorphicSerializer(read_only=True)
    content = serializers.JSONField()
    version = serializers.IntegerField(read_only=True)
    created_at = serializers.DateTimeField(required=False)
    class Meta:
        model = QuestionVersion
        fields = '__all__'

class QuestionCreateUpdateSerializer(serializers.ModelSerializer):
    """
    题目序列化器-新增和更新
    """
    knowledge_points = serializers.SerializerMethodField(read_only=True)
    knowledge_point_ids = serializers.PrimaryKeyRelatedField(
        queryset=KnowledgePoint.objects.all(),
        source='knowledge_points',
        many=True,
        required=False,
        allow_null=True,
        write_only=True,
        default=[]
    ) # 试题知识点表的知识点id
    relevance = serializers.FloatField(required=False)
    is_primary = serializers.BooleanField(required=False)
    type = serializers.CharField(write_only=True, required=False)
    options = serializers.ListField(required=False) # 单选题/多选题/判断题使用
    answer = DynamicAnswerField() # 使用自定义字段处理动态类型
    class Meta:
        model = BaseQuestion
        fields = [
            'id', 'author', 'stem', 'difficulty',
            'direction', 'knowledge', 'label_id',
            'explanation', 'status', 'visible', 'is_ai_generated',
            'extra', 'is_deleted', 'created_at', 'deleted_at',
            'updated_at', 'type', 'options', 'answer',
            'relevance', 'is_primary',
            'knowledge_points', 'knowledge_point_ids',
        ]
    
    MODEL_MAPPING = {
        '单选题': SingleChoiceQuestion,
        '多选题': MultipleChoiceQuestion,
        '判断题': TrueOrFalseQuestion,
        '填空题': FillInBlankQuestion,
        '问答题': QuestionAndAnswerQuestion
    }

    def get_knowledge_points(self, obj):
        """自定义知识点ID列表的返回格式"""
        return list(obj.knowledge_point.values_list('id', flat=True))

    def validate(self, attrs):
        """
        统一校验逻辑
        """
        # print("进入 validate 方法")
        question_type = attrs.get('type')
        if not question_type:
            raise serializers.ValidationError('题型不能为空')

        
        # print(f"准备传递的 question_type: {question_type}")
        self.fields['answer'].context['question_type'] = question_type  # 手动设置answer字段的上下文
        # print(f"上下文设置后: {self.context}")
        if not attrs.get('stem'):
            raise serializers.ValidationError('题干不能为空')
        return attrs
        
    def create(self, validated_data):
        """
        创建题目
        :param validated_data:
        :return:
        """
        knowledge_points_data = validated_data.pop('knowledge_points', [])
        # relevance = validated_data.pop('relevance', 0.5)
        # is_primary = validated_data.pop('is_primary', False)
        # 确定具体的题型类
        question_type = validated_data.pop('type', '单选题')  # 默认单选题
        
        model_class = self._get_model_class(question_type)
        subclass_fields = self._get_subclass_fields(validated_data, question_type)

        with transaction.atomic():
            # 创建具体题型的实例
            question = model_class.objects.create(**validated_data, **subclass_fields)
            
            # 更新试题知识点中间表
            if knowledge_points_data:
                self._update_knowledge_points(question, knowledge_points_data)

            # 创建试题版本表数据
            create_question_version(question)
            return question

    def update(self, instance, validated_data):
        """
        更新题目
        :param instance:
        :param validated_data:
        :return:
        """
        knowledge_points_data = validated_data.pop('knowledge_points', [])
        # relevance = validated_data.pop('relevance', None)
        # is_primary = validated_data.pop('is_primary', False)

        question_type = validated_data.pop('type', None) or instance.type

        subclass_fields = self._get_subclass_fields(validated_data, question_type)

        with transaction.atomic():
            # 更新基础字段
            for attr, value in validated_data.items():
                setattr(instance, attr, value)
            # 更新子类特有字段
            for attr, value in subclass_fields.items():
                setattr(instance, attr, value)
            instance.save()

            # 更新试题知识点关联关系
            if knowledge_points_data is not None:
                self._update_knowledge_points(instance, knowledge_points_data)
                
            create_question_version(instance)  # 创建试题版本表数据
            return instance
    
    def _update_knowledge_points(self, question, knowledge_points_data, default_relevance=0.5, default_is_primary=False):

        """
        更新试题与知识点关联
        """
        if not knowledge_points_data:
            return 

        relations_to_create = []
        for idx, kp in enumerate(knowledge_points_data):
            relations_to_create.append(
                QuestionKnowledgePoint(
                    question=question,
                    knowledge_point=kp,
                    relevance=default_relevance,
                    is_primary=default_is_primary if idx == 0 else False,
                )
            )
        
        # 删除旧的关联
        QuestionKnowledgePoint.objects.filter(question=question).delete()
        # 批量插入新关联
        QuestionKnowledgePoint.objects.bulk_create(relations_to_create)  
            

    def _get_model_class(self, question_type):
        """
        根据题型获取模型类
        """
        model_class = self.MODEL_MAPPING.get(question_type, BaseQuestion)
        if model_class == BaseQuestion:
            raise serializers.ValidationError('无效的题型')
        return model_class


    def _get_subclass_fields(self, validated_data, question_type):
        """
        提取子类特有字段
        """
        subclass_fields = {}

        # 单选题特有字段
        if question_type == '单选题':
            subclass_fields['type'] = question_type
            subclass_fields['options'] = validated_data.pop('options', [])
            subclass_fields['answer'] = validated_data.pop('answer', '')
        # 多选题特有字段
        elif question_type == '多选题':
            subclass_fields['type'] = question_type
            subclass_fields['options'] = validated_data.pop('options', [])
            subclass_fields['answer'] = validated_data.pop('answer', [])
        # 判断题特有字段
        elif question_type == '判断题':
            subclass_fields['type'] = question_type
            subclass_fields['options'] = validated_data.pop('options', [])
            subclass_fields['answer'] = validated_data.pop('answer', '')
        # 填空题特有字段
        elif question_type == '填空题':
            subclass_fields['type'] = question_type
            subclass_fields['answer'] = validated_data.pop('answer', [])
        # 问答题特有字段
        elif question_type == '问答题':
            subclass_fields['type'] = question_type
            subclass_fields['answer'] = validated_data.pop('answer', '')
        else:
            raise serializers.ValidationError('无效的题型')
        
        return subclass_fields


class QuestionKnowledgePointBulkSerializer(serializers.ModelSerializer):
    """
    试题与知识点关联序列化器-批量修改关联知识点
    """
    question_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )
    knowledge_point_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )

    class Meta:
        model = QuestionKnowledgePoint
        fields = [
            'question_ids', 'knowledge_point_ids'
        ]

    def validate_question_ids(self, value):
        # 验证题目ID是否存在
        existing_ids = set(BaseQuestion.objects.filter(
            id__in=value
        ).values_list('id', flat=True))
        missing_ids = set(value) - existing_ids
        if missing_ids:
            raise serializers.ValidationError(
                f"以下题目ID不存在: {missing_ids}"
            )
        return value

    def validate_knowledge_point_ids(self, value):
        # 验证知识点ID是否存在
        existing_ids = set(KnowledgePoint.objects.filter(
            id__in=value
        ).values_list('id', flat=True))
        missing_ids = set(value) - existing_ids
        if missing_ids:
            raise serializers.ValidationError(
                f"以下知识点ID不存在: {missing_ids}"
            )
        return value



class PaperSerializer(serializers.ModelSerializer):
    """
    试卷序列化器
    """
    class Meta:
        model = Paper
        fields = '__all__'



class PaperCreateUpdateSerializer(serializers.ModelSerializer):
    """
    试卷序列化器-新增和修改
    """
    author = serializers.PrimaryKeyRelatedField(
        queryset=UserInfo.objects.all(),
        write_only=True,
        required=False
    )  # 关联到用户表的外键id
    direction = serializers.CharField(required=False)
    difficulty = serializers.CharField(required=False)

    exam_id = serializers.PrimaryKeyRelatedField(
        queryset=KnowledgeExam.objects.all(),
        write_only=True,
        required=False
    )  # 关联到考试表的外键id

    question_version = serializers.PrimaryKeyRelatedField(
        queryset=QuestionVersion.objects.all(),
        write_only=True,
        required=False
    )
    index = serializers.IntegerField(required=False)
    attached = serializers.BooleanField(required=False)
    
    user = serializers.PrimaryKeyRelatedField(
        queryset=UserInfo.objects.all(),
        write_only=True,
        required=False
    )
    behavior = serializers.CharField(required=False)
    time = serializers.DateTimeField(required=False)
    result = serializers.CharField(required=False)
    extra = serializers.CharField(required=False, allow_null=True)
    questions = serializers.ListField(required=False)

    class Meta:
        model = Paper
        fields = [
            'id', 'name', 'direction', 'author', 'difficulty', 'status',
            'visible', 'disrupt', 'extra', 'is_deleted', 'deleted_at', 'created_at',
            'updated_at', 'exam_id', 'question_version', 'index', 'attached',
            'user', 'behavior', 'time', 'result', 'extra', 'questions'
        ]

    
    def validate(self, attrs):
        """
        统一校验逻辑
        """
        exam_id = attrs.get('exam_id')
        if not exam_id:
            raise serializers.ValidationError('考试ID不能为空')
        user = attrs.get('user')
        if not user:
            raise serializers.ValidationError('用户不能为空')
        
        return attrs

    
    def create(self, validated_data):
        """
        试卷新增
        1. 试卷表新增记录
        2. 考试表中更新记录，将外键试卷ID写入
        3. 试卷试题版本关联表中新增记录
        4. 试卷管理日志表中新增记录
        """
        # 提取关联对象
        exam_id = validated_data.pop('exam_id')
        # question_version = validated_data.pop('question_version')
        user = validated_data.pop('user', None)
        print(f"user:{type(user)}")

        # 提取可选字段
        index = validated_data.pop('index', 0)
        attached = validated_data.pop('attached', False)
        behavior = validated_data.pop('behavior', '创建')
        time = validated_data.pop('time', timezone.now())
        result = validated_data.pop('result', None)
        extra = validated_data.pop('extra', None)
        questions = validated_data.pop('questions', [])

        with transaction.atomic():
            # 1. 试卷表新增记录
            paper = Paper.objects.create(**validated_data, author=user)
            # 2. 更新考试表
            if exam_id:
                KnowledgeExam.objects.filter(id=exam_id.id).update(paper_id=paper.id)
            # 3. 试卷试题版本表新增记录
            if questions is not None:
                question_ids = [q['id'] for q in questions]
                # 查询这些试题的最新版本
                latest_version = (
                    QuestionVersion.objects
                    .filter(question_id__in=question_ids)
                    .order_by('question_id', '-version')
                    .distinct('question_id')  # 按question_id去重，保留question_version最大的
                )
                # 创建版本关联
                version_ids = [version.id for version in latest_version]

                # 创建映射字典
                version_map = {qv.question_id: qv for qv in latest_version}
                score_map = {q['id']: q['score'] for q in questions}

                # 批量创建数据
                paper_question_versions = []
                for qid in question_ids:
                    paper_question_versions.append(
                        PaperQuestionVersion(
                            paper = paper,
                            question_version_id = version_map[qid].id,
                            score = score_map[qid],
                            index = index,
                            attached = attached
                        )
                    )
                if paper_question_versions:
                    PaperQuestionVersion.objects.bulk_create(paper_question_versions, batch_size=100) # 批量创建试卷-试题版本关联记录
                # print(f"paper_question_versions:{paper_question_versions}")
            # 4. 试卷管理日志表新增记录
            PaperManager.objects.create(
                paper_id=paper.id,
                user_id=user.id,
                behavior=behavior,
                time=time,
                result=result,
                extra=extra
            )
            return paper
        
        
    def update(self, instance, validated_data):
        """
        试卷修改
        1. 更新试卷表
        2. 更新试卷试题版本关联表中记录
        """

        # 提取可选字段
        exam_id = validated_data.pop('exam_id', None)
        questions = validated_data.pop('questions', [])
        question_version_id = validated_data.pop('question_version_id', None)
        user = validated_data.pop('user', None)


        with transaction.atomic():
            # 1. 更新试卷表
            Paper.objects.filter(id=instance.id).update(**validated_data)

            # 2. 处理题目更新
            if questions is not None:
                # 获取现有的试卷题目版本关联
                existing_versions = instance.paper_qv_relations.all()
                existing_version_ids = set(pqv.question_version_id for pqv in existing_versions)
                # print(f"existing_version:{existing_versions}")
                # 处理传入的题目列表
                new_version_ids = set()
                for question in questions:
                    question_id = question.get('id')
                    question_version_id = question.get('question_version_id')
                    # print(f"question_id: {question_id}")
                    # print(f"question_version_id:{question_version_id}")

                    # 如果没有提供版本ID，使用该题目的最新版本
                    if not question_version_id and question_id:
                        latest_version = QuestionVersion.objects.filter(
                            question_id=question_id
                        ).order_by('-version').first()
                        if latest_version:
                            question_version_id = latest_version.id
                    
                    if question_version_id:
                        new_version_ids.add(question_version_id)
                        defaults = {
                            'index':question.get('index', 0),
                            'score':question.get('score', 10),
                            'attached':question.get('attached', False)
                        }
                        # 更新或创建关联记录
                        PaperQuestionVersion.objects.update_or_create(
                            paper_id=instance.id,
                            question_version_id=question_version_id,
                            defaults=defaults
                        )
                
                # 删除不在新列表中的题目关联
                to_delete_ids = existing_version_ids - new_version_ids
                if to_delete_ids:
                    PaperQuestionVersion.objects.filter(
                        question_version_id__in=to_delete_ids
                    ).delete()

        return instance
    

    def to_representation(self, instance):
        """"
        自定义创建成功后的返回格式
        """

        # 添加考试相关的信息
        if hasattr(instance, 'knowledgeexam_set'):
            exam_id = instance.knowledgeexam_set.first().id if instance.knowledgeexam_set.exists() else None
        
        ret = {
            'paper_id': instance.id,
            'exam_id': exam_id
        }

            
        return ret

class PaperQuestionVersionSerializer(serializers.ModelSerializer):
    """
    试卷试题版本关联序列化器
    """
    class Meta:
        models = PaperQuestionVersion
        fields = '__all__'


class PaperQuestionVersionDetailSerializer(serializers.ModelSerializer):
    """
    试卷试题版本关联序列化器-详情（用于获取试卷详情的嵌套序列化使用）
    """
    question_id = serializers.IntegerField(
        read_only=True,
        source='question_version.question_id'
    )
    content = serializers.JSONField(
        read_only=True,
        source='question_version.content'
    )
    class Meta:
        model = PaperQuestionVersion
        fields = [
            'id',  # 试卷试题版本id 
            'paper',  # 试卷id
            'question_version',  # 试题版本id
            'question_id',  # 试题id
            'content',  # 试题内容详情
            'score'
        ]

class PaperDetailSerializer(serializers.ModelSerializer):
    """
    试卷序列化器-详情
    """
    questions = PaperQuestionVersionDetailSerializer(
        many=True,
        read_only=True,
        source='paper_qv_relations'
    )
    exam_id = serializers.SerializerMethodField()
    class Meta:
        model = Paper
        fields = [
            'name', 'direction', 'author', 'difficulty', 'status',
            'visible', 'disrupt', 'extra', 'exam_id', 'questions'
        ]

    def get_exam_id(self, obj):
        """
        获取考试id
        """
        exam = obj.knowledgeexam_set.first()
        return exam.id if exam else None

class KnowledgeExamSerializer(serializers.ModelSerializer):
    """
    考试/作业序列化器
    """
    class Meta:
        model = KnowledgeExam
        fields = '__all__'

class KnowledgeExamCreateUpdateSerializer(serializers.ModelSerializer):
    """
    考试序列化器-新增和修改
    """
    author = serializers.PrimaryKeyRelatedField(
        queryset=UserInfo.objects.all(),
        required=False
    )  # 关联到用户表的外键id
    class_ids = serializers.ListField(
        child = serializers.IntegerField(),
        write_only=True,
        required=False
    )
    course_id = serializers.PrimaryKeyRelatedField(
        queryset=Course.objects.all(),
        write_only=True,
    ) # 课程考试关联表的课程id
    semester_id = serializers.PrimaryKeyRelatedField(
        queryset=Semester.objects.all(),
        write_only=True,
    ) # 课程考试关联表的学期id
    course_semester = serializers.PrimaryKeyRelatedField(
        queryset=CourseSemester.objects.all(),
        write_only=True,
        required = False
    ) # 课程考试关联表的课程id
    added_at = serializers.DateTimeField(required=False)  # 课程考试关联表中的添加时间字段
    usage_type = serializers.CharField(required=False)  # 课程考试关联表中的用途类型字段
    order = serializers.IntegerField(required=False)  # 课程考试关联表中的排序字段
    notes = serializers.CharField(required=False)  # 课程考试关联表中的教师备注字段
    pass_rate = serializers.CharField(
        required=False,
        allow_blank=True,
        allow_null=True,
        default=None,
        trim_whitespace=True # 去除前后空格
    )  # 及格率
    # status = serializers.CharField(required=False, allow_null=True)  # 考试状态

    class Meta:
        model = KnowledgeExam
        fields = [
            'id', 'name', 'author', 'paper', 'duration',
            'start_time', 'end_time', 'pass_rate', 'status', 'introduction',
            'is_deleted', 'deleted_at', 'created_at', 'updated_at', 'course_id',
            'added_at', 'usage_type', 'order', 'notes',
            'class_ids', 'semester_id', 'course_semester'
        ]
    
    def validate_pass_rate(self, value):
        """处理空字符串，并确保返回 float 或 None"""
        if value == "":
            return None  # 或 return 60.0（如果希望空字符串默认 60.0）
        if value is None:
            return None
        try:
            return float(value)  # 确保返回 float
        except (ValueError, TypeError):
            raise serializers.ValidationError("必须为数字或空字符串")


    def validate(self, attrs):
        """
        统一校验逻辑
        1. 验证考试时间有效性
        2. 验证用途类型
        """
        # 1. 校验考试结束时间晚于开始时间
        start_time = attrs.get('start_time')
        end_time = attrs.get('end_time')
        if start_time and end_time:
            if end_time <= start_time:
                raise serializers.ValidationError(
                    {'end_time': '结束时间必须晚于开始时间'}
                )
            # 校验时间是否在合理范围内（不超过1年）
            if (end_time - start_time).days > 365:
                raise serializers.ValidationError(
                    {'end_time': '考试时长不超过1年'}
                )
            
        # 2. 校验用途类型
        usage_type = attrs.get('usage_type')
        if usage_type:
            valid_types = ['homework', 'exam']
            if usage_type not in valid_types:
                raise serializers.ValidationError(
                    {'usage_type': f'用途类型必须是{valid_types}'}
                )
        
        # 3. 处理 pass_rate（确保在全局校验中也处理）
        if 'pass_rate' in attrs:
            if attrs['pass_rate'] == "":
                attrs['pass_rate'] = None  # 或者 60.0
            elif attrs['pass_rate'] is not None:
                try:
                    attrs['pass_rate'] = float(attrs['pass_rate'])
                except (ValueError, TypeError):
                    raise serializers.ValidationError({'pass_rate': '请提供有效的数字'})

        # 4. 验证课程和学期
        course_id = attrs.get('course_id')
        semester_id = attrs.get('semester_id')
        user_id = attrs.get('author', None)
        if course_id and semester_id:
            try:
                course_semester = CourseSemester.objects.get(
                    course = course_id,
                    semester = semester_id,
                    user_id = user_id
                )
            except CourseSemester.DoesNotExist:
                raise serializers.ValidationError({'course_semester': '课程和学期不匹配'})
            attrs['course_semester'] = course_semester

        
        return attrs
        
    def create(self, validated_data):
        """
        创建考试及其关联关系
        1. 创建考试记录
        2. 创建课程学期考试关联
        3. 创建班级考试关联 
        """
        # 提取关联对象
        class_ids = validated_data.pop('class_ids', [])  # 提取班级id列表
        course_id = validated_data.pop('course_id', None)
        semester_id = validated_data.pop('semester_id', None)
        course_semester_obj = validated_data.pop('course_semester')
        # 提取可选字段
        usage_type = validated_data.pop('usage_type', 'homework')  # 提取用途类型
        order = validated_data.pop('order', 0)  # 提取排序字段
        notes = validated_data.pop('notes', '')  # 提取教师备注字
        author = validated_data.pop('author', None)
        print(f"user:{author}")

        with transaction.atomic():
            # 1. 创建考试记录
            exam = KnowledgeExam.objects.create(**validated_data, author=author)
            # 2. 创建班级关联
            if class_ids:
                ClassKnowledgeExam.objects.bulk_create(
                    ClassKnowledgeExam(
                        exam_id_id=exam.id,
                        class_id_id=class_id
                    )
                    for class_id in class_ids
                    if Class.objects.filter(id=class_id).exists() # 确保class_id存在
                )
            # 3. 创建课程关联
            if course_semester_obj:

                CourseSemesterExam.objects.create(
                    course_semester = course_semester_obj,
                    exam_id = exam,
                    added_by = author or exam.author,
                    usage_type = usage_type,
                    order = order,
                    notes = notes
                )

        return exam

    def update(self, instance, validated_data):
        """
        更新考试/作业
        1. 更新考试表记录
        2. 更新班级考试关联表记录
        3. 更新课程学期考试关联表记录
        """
        # 提取可选字段
        author = validated_data.pop('author', None)
        
        class_ids = validated_data.pop('class_ids', None)
        pass_rate = validated_data.pop('pass_rate', instance.pass_rate)
        status = validated_data.pop('status', instance.status)
        course_id = validated_data.pop('course_id', None)
        semester_id = validated_data.pop('semester_id', None)
        course_semester_obj = validated_data.pop('course_semester', None)  
        usage_type = validated_data.pop('usage_type', 'homework')  # 提取用途类型
        order = validated_data.pop('order', 0)  # 提取排序字段
        notes = validated_data.pop('notes', '')  # 提取教师备注字
        start_time = validated_data.get('start_time', None)
        end_time = validated_data.get('end_time', None)
        

        with transaction.atomic():
            # 1. 更新考试表记录
            exam_update_data = {}
            if 'start_time' in validated_data:
                exam_update_data['start_time'] = start_time
            if 'end_time' in validated_data:
                exam_update_data['end_time'] = end_time
            
            if instance and exam_update_data:
                KnowledgeExam.objects.filter(id=instance.id).update(
                    **validated_data,
                    author = author,
                    pass_rate = pass_rate,
                    status = status
                )

            # 2. 更新班级考试关联表记录
            if class_ids is not None:
                # 获取现有关联班级ID
                existing_class_ids = set(instance.cls_exam_relations.values_list('class_id', flat=True))
                new_class_ids = set(class_ids)

                # 删除旧关联
                to_delete_ids = existing_class_ids - new_class_ids
                if to_delete_ids:
                    instance.cls_exam_relations.filter(class_id__in=to_delete_ids).delete()
                # 创建新关联
                to_create = [
                    ClassKnowledgeExam(
                        exam_id_id=instance.id,
                        class_id_id=class_id
                    )
                    for class_id in (new_class_ids - existing_class_ids)
                    if Class.objects.filter(id=class_id).exists() # 确保class_id存在
                ]
                if to_create:
                    ClassKnowledgeExam.objects.bulk_create(to_create)
            # 3. 更新课程考试关联表记录
            if course_semester_obj is not None:

                    CourseSemesterExam.objects.update_or_create(
                        exam_id=instance,
                        defaults = {
                            'course_semester': course_semester_obj,
                            'added_by': author,
                            'usage_type': usage_type,
                        'order': order,
                        'notes': notes
                        }
                    )
            
            return instance

    def to_representation(self, instance):
        """
        序列化输出格式
        """
        # 先获取默认的序列化结果
        ret = super().to_representation(instance)
        course_relation = instance.cs_exam_relations.select_related(
            'course_semester__course',
        ).first()
        if course_relation:
            # 添加课程相关信息
            ret['course'] = {
                'course_id': course_relation.course_semester.course_id if course_relation else None,
                'course_title': course_relation.course_semester.course.title if course_relation else None,
                'semester_id': course_relation.course_semester.semester_id if course_relation else None,
                'course_semester': course_relation.course_semester_id if course_relation else None,
            }
        # 添加班级相关信息 
        ret['classes'] = [
            {
                'class_id': class_exam.class_id.id,
                'class_name': class_exam.class_id.name,
            }
            for class_exam in instance.cls_exam_relations.all()
        ]

        return ret
    
class KnowledgeExamPublishSerializer(serializers.ModelSerializer):
    """考试/作业 - 发布"""
    author = serializers.PrimaryKeyRelatedField(
        queryset=UserInfo.objects.all(),
        required=False
    )  # 关联到用户表的外键id
    class_ids = serializers.ListField(
        child = serializers.IntegerField(),
        write_only=True,
        required=False
    )
    pass_rate = serializers.CharField(
        required=False,
        allow_blank=True,
        allow_null=True,
        default=None,
        trim_whitespace=True # 去除前后空格
    )  # 及格率
    status = serializers.CharField(required=False, allow_null=True, allow_blank=True)  # 考试状态

    class Meta:
        model = KnowledgeExam
        fields = [
            'id', 'name', 'author', 'paper', 'duration',
            'start_time', 'end_time', 'pass_rate', 'status', 'introduction',
            'is_deleted', 'deleted_at', 'created_at', 'updated_at', 'class_ids'
        ]
    
    def validate_pass_rate(self, value):
        """处理空字符串，并确保返回 float 或 None"""
        if value == "":
            return None  # 或 return 60.0（如果希望空字符串默认 60.0）
        if value is None:
            return None
        try:
            return float(value)  # 确保返回 float
        except (ValueError, TypeError):
            raise serializers.ValidationError("必须为数字或空字符串")
    
    def validate(self, attrs):
        """
        统一校验逻辑
        1. 验证考试时间有效性
        2. 验证用途类型
        """
        # 1. 校验考试结束时间晚于开始时间
        start_time = attrs.get('start_time')
        end_time = attrs.get('end_time')
        if start_time and end_time:
            if end_time <= start_time:
                raise serializers.ValidationError(
                    {'end_time': '结束时间必须晚于开始时间'}
                )
            # 校验时间是否在合理范围内（不超过1年）
            if (end_time - start_time).days > 365:
                raise serializers.ValidationError(
                    {'end_time': '考试时长不超过1年'}
                )
            
        # 2. 校验用途类型
        usage_type = attrs.get('usage_type')
        if usage_type:
            valid_types = ['homework','exam']
            if usage_type not in valid_types:
                raise serializers.ValidationError(
                    {'usage_type': f'用途类型必须是{valid_types}'}
                )
        
        # 3. 处理 pass_rate（确保在全局校验中也处理）
        if 'pass_rate' in attrs:
            if attrs['pass_rate'] == "":
                attrs['pass_rate'] = None  # 或者 60.0
            elif attrs['pass_rate'] is not None:
                try:
                    attrs['pass_rate'] = float(attrs['pass_rate'])
                except (ValueError, TypeError):
                    raise serializers.ValidationError({'pass_rate': '请提供有效的数字'})
                
        
        return attrs
        
    def update(self, instance, validated_data):
        """
        保存和发布考试/作业
        1. 更新考试表记录
        2. 更新班级考试关联表记录
        3. 更新用户参加考试记录表
        """
        # 提取可选字段
        class_ids = validated_data.pop('class_ids', [])
        pass_rate = validated_data.pop('pass_rate', instance.pass_rate)
        status = validated_data.pop('status', instance.status)
        start_time = validated_data.get('start_time', None)
        end_time = validated_data.get('end_time', None)
        print(f"validated_data发布：{validated_data}")

        with transaction.atomic():
                 
                # 1. 更新考试表
                exam_update_data = {}
                if 'start_time' in validated_data:
                    exam_update_data['start_time'] = start_time
                if 'end_time' in validated_data:
                    exam_update_data['end_time'] = end_time
                
                if instance and exam_update_data:
                    KnowledgeExam.objects.filter(id=instance.id).update(
                        **validated_data,
                        pass_rate = pass_rate,
                        status = status
                    )

                # 2. 更新班级考试关联表
                exist_class_exam = ClassKnowledgeExam.objects.filter(exam_id = instance)  # 得到已存在的班级id列表
                exist_class_ids = set(exist_class_exam.values_list('class_id', flat=True))  # 将班级列表转换为集合类型
                new_class_ids = set(class_ids)  # 传递的班级列表转换为集合类型
                to_delete_ids = exist_class_ids - new_class_ids  # 集合差集操作，得到需要删除的班级id列表
                if to_delete_ids:
                    ClassKnowledgeExam.objects.filter(
                        exam_id = instance,
                        class_id__in = to_delete_ids
                    ).delete() # 删除旧的班级考试关联
                to_add_ids = new_class_ids - exist_class_ids  # 集合差集操作，得到需要新增的班级id列表
                if to_add_ids:
                    classes = Class.objects.filter(id__in=to_add_ids)
                    class_map = {cls.id: cls for cls in classes}  # 建立ID到实例的映射
                    ClassKnowledgeExam.objects.bulk_create(
                        [
                            ClassKnowledgeExam(
                                exam_id = instance,
                                class_id = class_map[class_id]
                            )
                            for class_id in to_add_ids
                            if class_id in class_map
                        ]
                    )  # 同步新的班级考试关联

                if class_ids:
                    related_class_ids = class_ids
                else:
                    related_class_ids = list(exist_class_exam.values_list('class_id', flat=True))  # 获取所有关联的class_id列表
                if not related_class_ids:
                    raise serializers.ValidationError("没有关联任何班级，无法保存或发布")
                
                # 如果status="已发布"，则创建参加记录
                if status == '已发布':
                
                    # 查询class_id列表下的所有学生
                    student_ids = ClassMember.objects.filter(
                        class_obj__in=related_class_ids,
                        student__isnull=False
                    ).values_list('student_id', flat=True).distinct()

                    print(f"class_exams:{class_ids}")
                    print(f"student_ids:{student_ids}")

                    # 3. 批量创建参加记录
                    records = [
                        UserTakePartExam(
                            user_id=student_id,
                            exam_id=instance,
                            take_part_status='未提交'
                        )
                        for student_id in student_ids
                    ]
                    print(f"records:{records}")
                    results1 = UserTakePartExam.objects.bulk_create(records)
                    print(f"results1:{results1}")
                
            
        return instance
        
    def create(self, validate_data):
        raise serializers.ValidationError("不允许创建新记录")
        


class KnowledgeExamListSerializer(serializers.ModelSerializer):
    """
    考试/作业序列化器-列表详情
    1. 获取考试/作业内容
    2. 获取考试/作业对应的班级id
    """
    class_names = serializers.SerializerMethodField()
    submitted_count = serializers.IntegerField()  # 参加某次考试已提交试卷的学生数量
    total_take_count = serializers.IntegerField()  # 参加某次考试的所有学生数量


    class Meta:
        model = KnowledgeExam
        fields = [
            'id', 'name', 'paper', 'start_time',
            'end_time', 'class_names', 'duration',
            'introduction','submitted_count', 'total_take_count',
        ]

    def get_class_names(self, obj):
        """
        获取考试关联的班级名称列表
        """
        relations = getattr(obj, 'prefetched_cls_relations', [])  # 使用预取数据
        if not relations:
            relations = obj.cls_exam_relations.select_related('class_id').all()

        class_names= set()
        for relation in relations:
            if relation.class_id and relation.class_id.name:
                class_names.add(relation.class_id.name)
        return list(class_names)
    
    
class KnowledgeExamStudentListSerializer(serializers.ModelSerializer):
    """
    获取考试/作业列表-学生视角
    """
    total_score = serializers.SerializerMethodField()
    take_part_status = serializers.SerializerMethodField()

    class Meta:
        model = KnowledgeExam
        fields = [
            'id', 'name', 'start_time', 'end_time', 'duration',
            'total_score', 'take_part_status'

        ]

    def get_user_exam(self, obj):
        # 使用预取数据，避免额外查询
        return getattr(obj, 'user_take_part_exam', [None])[0]

    def get_total_score(self, obj):
        user_exam = self.get_user_exam(obj)
        return user_exam.total_score if user_exam else None
    
    def get_take_part_status(self, obj):
        user_exam = self.get_user_exam(obj)
        return user_exam.take_part_status if user_exam else None
    
class KnowledgeExamDetailSerializer(serializers.ModelSerializer):
    """
    考试/作业序列化器-详情
    """
    class_names = serializers.SerializerMethodField()
    class Meta:
        model = KnowledgeExam
        fields = [
            'id', 'name', 'start_time',
            'end_time', 'status', 'duration','introduction',
            'class_names', 'author'
        ]
    def get_class_names(self, obj):
        return list(
            obj.class_exams
            .select_related('class_id')  # 优化查询性能
            .values_list('class_id__name', flat=True)
            .distinct()
        )

class UserTakePartExamSerializer(serializers.ModelSerializer):
    """
    用户参加考试序列化器
    """
    class Meta:
        model = UserTakePartExam
        fields = '__all__'


class UserTakePartExamDetailSerializer(serializers.ModelSerializer):
    """
    用户参加考试序列化器-详情
    """
    exam_info = serializers.SerializerMethodField()
    user_info = serializers.SerializerMethodField()
    questions = serializers.SerializerMethodField()
    user_take_exam = serializers.IntegerField(source='pk')

    class Meta:
        model = UserTakePartExam
        fields = [
            'exam_info', 'user_info', 'questions',
            'end_time', 'take_part_status', 'total_score',
            'user_take_exam'
        ]

    def get_exam_info(self, obj):
        exam = obj.exam_id
        return {
            'exam_id': exam.id,
            'exam_name': exam.name,
            'duration': exam.duration,
        }
    
    def get_user_info(self, obj):
        user = obj.user
        if user.student_number:
            student_number = user.student_number
        else:
            student_number = user.username
        
        return {
            'student_id': user.id,
            'student_name': user.first_name,
            'student_number': student_number,
        }
    
    def get_questions(self, obj):
        # 获取该考试的所有题目以及用户作答情况
        questions = []
        # 1. 获取考试关联的试卷题目
        if obj.exam_id.paper:
            paper_questions = PaperQuestionVersion.objects.filter(
                paper=obj.exam_id.paper
            ).select_related('question_version')

            # 获取用户对题目的作答
            user_responses = UserExamQuestionFeedback.objects.filter(
                exam_id = obj.exam_id,
                user = obj.user
            ).select_related('question_version')

            response_dict = {r.question_version_id: r for r in user_responses}

            for pq in paper_questions:
                score = PaperQuestionVersion.objects.filter(
                    paper = obj.exam_id.paper,
                    question_version=pq.question_version
                ).values_list('score', flat=True).first()
                score = float(score) if score else 0

                response = response_dict.get(pq.question_version_id)
                questions.append({
                    'question_version_id': pq.question_version_id,
                    'content': pq.question_version.content,
                    'score': score,
                    'feedback': response.feedback if response else None,
                    'get_score': response.get_score if response else None,
                    'justification': response.justification if response else None
                })
            
        return questions


class UserTakePartExamStudentDetailSerializer(serializers.ModelSerializer):
    """
    用户参加考试序列化器-详情(学生答卷视角)
    """
    exam_info = serializers.SerializerMethodField()
    user_info = serializers.SerializerMethodField()
    questions = serializers.SerializerMethodField()
    user_take_exam = serializers.IntegerField(source='pk')

    class Meta:
        model = UserTakePartExam
        fields = [
            'exam_info', 'user_info', 'questions',
            'end_time', 'take_part_status', 'user_take_exam'
        ]

    def get_exam_info(self, obj):
        exam = obj.exam_id
        return {
            'exam_id': exam.id,
            'exam_name': exam.name,
            'duration': exam.duration,
        }
    
    def get_user_info(self, obj):
        user = obj.user
        # if user.role == 1:
        #     last_number = user.teacher_number
        # elif user.role == 2:
        #     last_number = user.student_number
        # else:
        #     last_number = user.username
        
        return {
            'student_id': user.id,
            'student_name': user.first_name,
            'student_number': user.student_number,
        }
    
    def get_questions(self, obj):
        # 获取该考试的所有题目以及用户作答情况
        questions = []
        # 1. 获取考试关联的试卷题目
        if obj.exam_id.paper:
            paper_questions = PaperQuestionVersion.objects.filter(
                paper=obj.exam_id.paper
            ).select_related('question_version')

            # 获取用户对题目的作答
            user_responses = UserExamQuestionFeedback.objects.filter(
                exam_id = obj.exam_id,
                user = obj.user
            ).select_related('question_version')
            

            response_dict = {r.question_version_id: r for r in user_responses}

            for pq in paper_questions:
                score = PaperQuestionVersion.objects.filter(
                    paper = obj.exam_id.paper,
                    question_version=pq.question_version
                ).values_list('score', flat=True).first()
                score = float(score) if score else 0

                response = response_dict.get(pq.question_version_id)
                questions.append({
                    'question_version_id': pq.question_version_id,
                    'content': pq.question_version.content,
                    'score': score,
                    'feedback': response.feedback if response else None,
                    'get_score': response.get_score if response else 0,
                    'justification': response.justification if response else None
                })
            
        return questions


class UserTakePartExamOverviewSerializer(serializers.ModelSerializer):
    """
    用户参加考试序列化器-总览
    """
    student_id = serializers.PrimaryKeyRelatedField(
        queryset=UserInfo.objects.all(),
        required=False,
        source='user_id'
    )
    student_number = serializers.SerializerMethodField()
    student_name = serializers.SerializerMethodField()
    # user_take_exam = serializers.IntegerField(source='pk')

    class Meta:
        model = UserTakePartExam
        fields = [
            'id', 'exam_id', 'end_time',
            'take_part_status', 'total_score',
            'student_id', 'student_number', 'student_name',
        ]

    def get_student_name(self, obj):
        return obj.user.first_name if obj.user.first_name else ""

    def get_student_number(self, obj):
        if obj.user.student_number:
            return obj.user.student_number
        elif obj.user.teacher_number:
            return obj.user.teacher_number
        else:
            return obj.user.username

class UserExamQuestionFeedbackSerializer(serializers.ModelSerializer):
    """
    用户考试答题反馈序列化器
    """
    class Meta:
        model = UserExamQuestionFeedback
        fields = '__all__'

class UserExamQuestionFeedbackSubmitSerializer(serializers.ModelSerializer):
    """
    用户考试反馈序列化器-提交
    """
    user = serializers.PrimaryKeyRelatedField(
        queryset=UserInfo.objects.all(),
        required=True
    )
    exam_id = serializers.PrimaryKeyRelatedField(
        queryset=KnowledgeExam.objects.all(),
        required=True
    )
    user_take_exam = serializers.PrimaryKeyRelatedField(
        queryset=UserTakePartExam.objects.all(),
        required=True
    )
    questions = serializers.ListField(
        child=serializers.DictField(),
        required=False
    )
    take_part_status = serializers.CharField(required = False)
    start_time = serializers.DateTimeField(required = False)
    end_time = serializers.DateTimeField(required = False)


    class Meta:
        model = UserExamQuestionFeedback
        fields = [
            'id', 'user', 'exam_id', 'questions', 'user_take_exam',
            'feedback', 'start_time', 'end_time', 'take_part_status'
        ]

    def validate_questions(self, value):
        """确保questions列表中的每个元素格式正确"""
        validated_questions = []
        for question in value:
            if not isinstance(question, dict):
                raise serializers.ValidationError("每个问题必须是字典格式")
            
            # 转换question_version_id为整数
            try:
                version_id = int(question.get('question_version_id'))
            except (TypeError, ValueError):
                raise serializers.ValidationError("question_version_id必须是整数")
            
            validated_questions.append({
                'question_version_id': version_id,
                'feedback': str(question.get('feedback', ''))
            })
        
        return validated_questions

    
    def create(self, validated_data):
        """
        创建用户考试反馈记录
        1. 根据考试id查询到对应的试题版本id
        2. 新增用户考试答题反馈表中的用户id、考试id、试题版本、答题内容、用户参加考试记录id
        3. 修改更新用户参加考试记录表的开始作答时间和提交时间
        """
        questions = validated_data.pop('questions', [])
        user = validated_data['user']
        exam_id = validated_data['exam_id']
        take_part_status = validated_data.get('take_part_status', '')
        end_time = validated_data.get('end_time', None)
        user_take_exam = validated_data['user_take_exam']

        with transaction.atomic():
            # 1. 加载所需的试题版本
            version_ids = [q['question_version_id'] for q in questions]
            versions = {v.id: v for v in QuestionVersion.objects.filter(id__in=version_ids)}
            # 2. 创建用户答题记录
            feedback_objects = []
            for q in questions:
                version = versions.get(q['question_version_id'])
                if not version:
                    raise serializers.ValidationError(f"试题版本{q['question_version_id']}不存在")
                feedback_objects.append(
                    UserExamQuestionFeedback(
                        user=user,
                        exam_id=exam_id,
                        user_take_exam=user_take_exam,
                        question_version=version,
                        feedback=q.get('feedback', '')
                    )
                )
            
            # 3. 批量创建
            try:
                created_feedbacks = UserExamQuestionFeedback.objects.bulk_create(feedback_objects)
                print(f"成功创建 {len(created_feedbacks)} 条反馈记录")
                # 获取实际ID
                first_id = UserExamQuestionFeedback.objects.filter(
                    user = user,
                    exam_id = exam_id,
                    user_take_exam = user_take_exam
                ).order_by('-id').first().id
            except Exception as e:
                print(f"创建记录失败：{str(e)}")
                raise

            # 4. 更新用户参加考试记录表
            user_exam_record, _ = UserTakePartExam.objects.update_or_create(
                exam_id = exam_id,
                user = user,
                defaults={
                    'end_time':end_time,
                    'take_part_status': '阅卷中'
                }
            )

            # 5. 发送信号
            print(f"准备发送信号，exam_id={exam_id}, user_id={user.id}, 首个反馈ID={feedback_objects[0].id}")
            exam_submitted.send(
                sender=self.__class__,
                exam_id = exam_id,
                user = user,
                feedback_ids = [f.id for f in created_feedbacks if f.id]  # 传递所有新创建的ID
            )
            
            return {'success':True, 'count': len(created_feedbacks), 'first_feedback_id':first_id}

        
class UserExamQuestionFeedbackPublishSerializer(serializers.ModelSerializer):
    """
    用户考试反馈序列化器-发布阅卷结果
    """
    user = serializers.PrimaryKeyRelatedField(
        queryset=UserInfo.objects.all(),
        required=True
    )
    exam_id = serializers.PrimaryKeyRelatedField(
        queryset=KnowledgeExam.objects.all(),
        required=True
    )
    user_take_exam = serializers.PrimaryKeyRelatedField(
        queryset=UserTakePartExam.objects.all(),
        required=True
    )
    course = serializers.PrimaryKeyRelatedField(
        queryset=Course.objects.all(),
        required=True
    )
    semester = serializers.PrimaryKeyRelatedField(
        queryset=Semester.objects.all(),
        required=True
    )
    course_semester = serializers.PrimaryKeyRelatedField(
        queryset=CourseSemester.objects.all(),
        write_only=True,
        required = False
    )  # 课程考试关联表的课程id
    questions = serializers.ListField(
        child=serializers.DictField(),
        write_only=True,
        required=True
    )  # 仅用于输入不保存到模型
    take_part_status = serializers.CharField(write_only=True, required = False)
    end_time = serializers.DateTimeField(write_only=True, required = False)
    total_score = serializers.FloatField(write_only=True, required = False)
    next_publish_id = serializers.IntegerField(read_only=True, required = False)


    class Meta:
        model = UserExamQuestionFeedback
        fields = [
            'id', 'user', 'exam_id', 'questions',
            'feedback', 'take_part_status', 'end_time',
            'get_score', 'justification', 'total_score',
            'course', 'semester', 'course_semester','user_take_exam',
            'next_publish_id'
        ]

    def validate_questions(self, value):
        """确保questions列表中的每个元素格式正确"""
        if not isinstance(value, list):
            raise serializers.ValidationError("questions必须是列表")

        validated_questions = []
        for idx, question in enumerate(value, 1):
            if not isinstance(question, dict):
                raise serializers.ValidationError("每个问题必须是字典格式")
            
            # 检查所有必需字段
            required_fields = ['question_version_id', 'get_score', 'justification']
            missing_fields = [field for field in required_fields if field not in question]
            
            if missing_fields:
                raise serializers.ValidationError(
                    f"第{idx}个问题缺少以下字段: {', '.join(missing_fields)}"
                )
            
            # 验证字段类型
            try:
                validated_questions.append({
                    'question_version_id': int(question['question_version_id']),
                    'get_score': float(question['get_score']),
                    'justification': str(question['justification'])
                })
            except (ValueError, TypeError) as e:
                raise serializers.ValidationError(f"第{idx}个问题字段格式错误: {str(e)}")
        
        return validated_questions
        

    
    def create(self, validated_data):
        """
        更新用户考试反馈记录
        1. 根据考试id查询到对应的试题版本id
        2. 更新用户考试答题反馈表中的得分，评语
        3. 修改更新用户参加考试记录表的总分数
        4. 获取该考试下的下一个发布ID
        5. 发送学情分析信号
        6. 返回用户参加考试记录和下一个ID
        """
        questions = validated_data.pop('questions', [])
        user = validated_data['user']
        exam_id = validated_data['exam_id']
        total_score = validated_data.pop('total_score', 0)
        course_semester = validated_data.pop('course_semester', None)
        semester = validated_data.pop('semester', None)
        course = validated_data.pop('course', None)
        user_take_exam = validated_data['user_take_exam']


        with transaction.atomic():
            # 1. 加载所需的试题版本
            version_ids = [q['question_version_id'] for q in questions]
            versions = {v.id: v for v in QuestionVersion.objects.filter(id__in=version_ids)}
            # 2. 更新用户答题记录
            for q in questions:
                version = versions.get(q['question_version_id'])
                if not version:
                    raise serializers.ValidationError(f"试题版本{q['question_version_id']}不存在")
                
                # 更新记录
                UserExamQuestionFeedback.objects.filter(
                    user = user,
                    exam_id = exam_id,
                    question_version = version,
                    user_take_exam = user_take_exam
                ).update(
                    get_score=q['get_score'],
                    justification=q['justification']
                )

            # 3. 更新用户参加考试记录表
            user_exam_record, _ = UserTakePartExam.objects.update_or_create(
                exam_id = exam_id,
                user = user,
                defaults = {
                    'total_score': total_score,
                    'take_part_status': '已发布'
                }
            )

            usage_type = CourseSemesterExam.objects.filter(
                exam_id = exam_id,
                course_semester = course_semester
            ).values_list('usage_type', flat=True).first() or "exam"
            print(f"usage_type:{usage_type}")

            # 4. 获取下一个发布ID，查询usertakepartexam表中未发布的条目，usertakepartexam表中的主键id
            next_publish_id = UserTakePartExam.objects.filter(
                exam_id = exam_id,
                take_part_status = '已阅卷'
            ).order_by('id').values_list('id', flat=True).first()


            # 5. 发送学情分析信号
            print(f"成绩发布成功，准备发送学情分析信号，course={course}, semester={semester}, exam_id={exam_id}, user_id={user.id}, usage_type={usage_type}")
            exam_analysis.send(
                sender=self.__class__,
                course_semester = course_semester,
                semester = semester,
                course = course,
                exam_id = exam_id,
                user = user,
                total_score = total_score,
                usage_type = usage_type
            )

            # 6. 返回用户参加考试记录及下一个发布id
            result = {
                'user_exam_record': user_exam_record,
                'next_publish_id': next_publish_id
            }

            # 将next_publish_id 添加到序列化器实例
            self.next_publish_id = next_publish_id

            return result
        
class UserInfoListSerializer(serializers.ModelSerializer):
    """
    课程归属人列表序列化器
    """
    class Meta:
        model = UserInfo
        fields = ['id', 'username']
