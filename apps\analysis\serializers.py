from rest_framework import serializers
from .models import ExamAnalysis, CourseAnalysis
# from exam.models import ClassKnowledgeExam, CourseKnowledgeExam
from course.models import Class, Course, Semester
from exam.models import ClassKnowledgeExam, CourseSemesterExam


class CourseAnalysisSerializers(serializers.ModelSerializer):
    
    class Meta:
        model = CourseAnalysis
        fields = '__all__'


class ExamAnalysisSerializers(serializers.ModelSerializer):
    
    class Meta:
        model = ExamAnalysis
        fields = '__all__'


class ExamAnalysisListSerializers(serializers.ModelSerializer):
    """考试/作业学情分析序列化器-列表"""
    student_name = serializers.CharField(source='user.username', read_only=True)
    # analysis_exams = serializers.SerializerMethodField()

    class Meta:
        model = ExamAnalysis
        fields = [
            'id', 'student_name', 'analysis_score', 'analysis_exam'
        ]
    
    # def get_analysis_exams(self, obj):
    #     """获取分析的考试数据"""
    #     exam = obj.exam_id
    #     try:
    #         # 获取该考试对应的所有ClassKnowledgeExam记录
    #         class_knowledge_exam_qs = ClassKnowledgeExam.objects.filter(exam_id=exam)
    #         if class_knowledge_exam_qs.exists():
    #             return class_knowledge_exam_qs.first().analysis_exams
    #     except ClassKnowledgeExam.DoesNotExist:
    #         return None
        
    
class ExamAnalysisWholeClassSerializers(serializers.ModelSerializer):
    """考试/作业学情分析序列化器-班级整体"""
    class Meta:
        model = ClassKnowledgeExam
        fields = [
            'analysis_exams', 'exam_id'
        ]

class ExamAnalysisWholeCourseSerializers(serializers.ModelSerializer):
    """考试/作业学情分析序列化器-课程整体"""

    class Meta:
        model = CourseSemesterExam
        fields = [
            'analysis_course_exams', 'exam_id'
        ]

class ExamAnalysisClassListSerializers(serializers.ModelSerializer):
    """考试关联班级序列化器"""
    class Meta:
        model = Class
        fields = [
            'id', 'name'
        ]
        
class CourseAnalysisListSerializers(serializers.ModelSerializer):
    """课程学情分析序列化器-列表"""
    student_name = serializers.CharField(source='user.username', read_only=True)
    student_number = serializers.CharField(source='user.id_number', read_only=True)


    class Meta:
        model = CourseAnalysis
        fields = [
            'id', 'student_name', 'student_number', 'analysis_status'
        ]

class CourseAnalysisDetailSerializers(serializers.ModelSerializer):
    """课程学情分析序列化器-详情"""
    student_name = serializers.CharField(source='user.username', read_only=True)
    student_number = serializers.CharField(source='user.id_number', read_only=True)


    class Meta:
        model = CourseAnalysis
        fields = [
            'id', 'student_name', 'student_number',
            'learn_progress', 'homework_situation', 'exam_grade', 'course_comment'
        ]


class CourseAnalysisClassListSerializers(serializers.ModelSerializer):
    """课程关联班级序列化器"""
    class Meta:
        model = Class
        fields = [
            'id', 'name'
        ]