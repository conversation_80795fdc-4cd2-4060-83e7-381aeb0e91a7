from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
import requests
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ars<PERSON>, JSONParser
from rest_framework.decorators import api_view, parser_classes, permission_classes
import os

import re
import urllib.parse
from django.http import StreamingHttpResponse, HttpResponse
from django.utils.encoding import escape_uri_path
import sys
import json
from django.db import transaction
from datetime import datetime
from django.conf import settings
import traceback

from core.utils import custom_api_view
from .models import ChunkQA
# sys.path.insert(0, os.path.abspath("/root/aieducation_backend/knowledge"))
# from pgdatabase import *
from .pgdatabase import (
    get_knowledge_base_by_id,
    #get_user_kb_links,
    get_kb_course_links,
    get_course_by_id,
    get_kb_courses_batch,
    add_knowledge_base,
    #add_user_kb_link,
    add_course_kb_link,
    update_knowledge_base,
    delete_knowledge_base,
    get_all_knowledge_bases,
    #link_user_kb,

    #delete_all_kb_links,
    delete_all_kb_course_links,
    #delete_user_kb_link,
    delete_course_kb_link,
    delete_knowledge_base_cascade,
    get_all_active_knowledge_bases,
    batch_delete_knowledge_bases_cascade,
    get_user_knowledge_bases,
    get_user_kb_ids,
    get_course_kb_links
)
from .chunk_qa_utils import *
# BASE_URL = "http://192.168.5.249:30847"
# HEADERS = {
#     "Content-Type": "application/json",
#     "Authorization": "Bearer tianhe1-Y5YmFkNjhjNGZmNjExZjA4MDNiNGFkNW"
# }
from django.conf import settings
from course.models import CourseSemester
from course.serializers import CourseSemesterOwnerSerializer
from course.utils import api_response
# 安全获取配置
def get_knowledge_config():
    """安全地获取知识库配置"""
    try:
        return settings.KNOWLEDGE_CONFIG
    except AttributeError:
        # 如果没有配置，使用默认值
        print("[WARN] 未找到知识库配置，使用默认值")
        return {
            'BASE_URL': os.environ.get('KNOWLEDGE_BASE_URL', 'http://192.168.5.249:30847'),
            'API_TOKEN': os.environ.get('KNOWLEDGE_API_TOKEN', 'tianhe1-Y5YmFkNjhjNGZmNjExZjA4MDNiNGFkNW'),
            'TIMEOUT': int(os.environ.get('KNOWLEDGE_TIMEOUT', '30')),
        }

# 获取配置
_config = get_knowledge_config()
BASE_URL = _config['BASE_URL']
HEADERS = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {_config['API_TOKEN']}"
}



# 返回格式规范
def sucess_api_response(data=None, status=200):
    return Response({"data": data, }, status=status)

def fail_api_response(error=None, status=500):
    '''
    {} 里的内容是返回给前端的实际数据。
    status= 是告诉浏览器/客户端本次请求的 HTTP 状态码。
    '''
    return Response({"error": error, }, status=status)
'''------------登录接口---------'''
from django.contrib.auth import authenticate, login
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

class FakeGroups:
    def all(self):
        return []

class FakeUser:
    def __init__(self, user_id=1, username="test_user"):
        self.id = user_id
        self.username = username
        self.is_authenticated = True
        self.is_anonymous = False
        self.is_active = True
        self.role = 1
        self.groups = FakeGroups()  # 关键：加上groups属性

    def has_perm(self, perm, obj=None):
        return True

def get_test_user(request):
    """获取测试用户，优先使用真实用户，否则返回假用户"""
    # 如果有真实认证用户，使用真实用户
    if hasattr(request.user, 'is_authenticated') and request.user.is_authenticated:
        return request.user

    # 否则返回假用户进行测试
    fake_user = FakeUser(user_id=1, username="test_user")
    print(f"[DEBUG] 使用假用户进行测试: ID={fake_user.id}, 用户名={fake_user.username}")
    return fake_user

@csrf_exempt
@api_view(['POST'])
def user_login(request):
    """用户登录接口"""
    username = request.data.get('username')
    password = request.data.get('password')

    if not username or not password:
        return fail_api_response("用户名和密码不能为空", status=400)

    user = authenticate(username=username, password=password)
    if user:
        login(request, user)
        return sucess_api_response({
            "message": "登录成功",
            "user_id": user.id,
            "username": user.username
        })
    else:
        return fail_api_response("用户名或密码错误", status=401)
'''------------登录接口结束---------'''

'''------------知识库管理接口---------'''
def ensure_bge_m3_in_gpustack():
    """
    检查GPUStack中的embedding模型是否有bge-m3，没有则自动添加。
    返回 (True, 提示) 或 (False, 错误提示)
    """
    BASE_URL = "http://192.168.5.249:30847"
    HEADERS = {
        "Content-Type": "application/json",
        "Authorization": "Bearer tianhe1-Y5YmFkNjhjNGZmNjExZjA4MDNiNGFkNW"
    }
    # 1. 查询模型列表
    try:
        resp = requests.get(f"{BASE_URL}/api/v1/models_token/", headers=HEADERS, timeout=10)
        if resp.status_code != 200 and resp.json().get("code") == 0:
            return False, f"查询模型列表失败，HTTP状态码: {resp.status_code}"
        data = resp.json()
        models = data.get("data", {}).get("GPUStack", {}).get("models", [])
        for m in models:
            if m.get("name") == "bge-m3" and m.get("type") == "embedding":
                return True, "bge-m3 embedding模型已存在，无需添加"
    except Exception as e:
        return False, f"查询模型列表异常: {str(e)}"

    # 2. 没有则添加
    add_body = {
        "llm_factory": "GPUStack",
        "llm_name": "bge-m3",
        "model_type": "embedding",
        "api_key": "model-manager_b71362a893dc0e82_a509538b8a12e1e37e5b6da5bbb4f245c",
        "api_base": "http://192.168.5.249:32450",
        "max_tokens": 8192
    }
    try:
        resp_add = requests.post(f"{BASE_URL}/api/v1/models_token", headers=HEADERS, json=add_body, timeout=10)
        if resp_add.status_code == 200 and resp_add.json().get("code") == 0:
            return True, "bge-m3 embedding模型不存在，已成功添加"
        else:
            return False, f"添加bge-m3模型失败: {resp_add.text}"
    except Exception as e:
        return False, f"添加bge-m3模型异常: {str(e)}"
def _get_user_courses_and_semesters(user):
    """
    获取当前用户关联的课程和学期信息
    """
    """
        获取用户可以看到的课程及学期列表（非接口形式）
        :param user: User对象
        :return: list 序列化后的课程学期数据
        """
    user_id = user.id
    queryset = CourseSemester.objects.filter(user_id=user_id)
    serializer = CourseSemesterOwnerSerializer(queryset, many=True)
    return serializer.data

def _merge_course_semester_info(course_ids, user_course_semesters):
    """根据课程ID列表，合并课程title和学期信息"""
    result = []
    for course_id in course_ids:
        for cs in user_course_semesters:
            if cs["course"]["id"] == course_id:
                result.append({
                    "course_id": course_id,
                    "course_title": cs["course"]["title"],
                    "semester": cs["semester"]
                })
                break
    return result


class DatasetListCreateView(APIView):
    permission_classes = [AllowAny]
    permission_map={
        'get': ['knowledge_base.view_knowledge_bases'],
        'post': ['knowledge_base.add_knowledge_bases']
    }
    def get(self, request):
        """获取当前用户有权限的知识库列表，支持通过source参数选择数据源"""
        # 从认证的用户对象获取用户信息
        user = get_test_user(request)
        user_id = user.id

        # 获取数据源参数，默认为rag
        data_source = request.GET.get('source', 'external').lower()

        if data_source == 'local':
            return self._get_datasets_from_local(request, user)
        else:
            return self._get_datasets_from_rag(request, user)


    def _get_datasets_from_rag(self, request, user):
        """
        从RAG获取知识库列表，并过滤用户权限
        支持所有RAG原生参数：page, page_size, orderby, desc, name, id
        """
        user_id = user.id
        print(f"[INFO] 用户(ID:{user_id}, 用户名:{user.username})调用RAG List datasets接口")

        try:
            '''---权限验证---'''

            user_kbs = get_user_knowledge_bases(user)
            user_kb_ids = set(str(kb.id) for kb in user_kbs)
            '''---权限验证结束---'''


            print(f"[DEBUG] 用户有权限的知识库ID列表: {list(user_kb_ids)}")

            if not user_kb_ids:
                return sucess_api_response({
                    "code": 0,
                    "data": [],
                    "debug_info": {
                        "message": "用户没有关联的知识库",
                        "user_id": user_id,
                        "timestamp": datetime.now().isoformat(),
                        "source": "rag_filtered"
                    }
                })

            # 获取所有查询参数
            params = {}
            rag_params = ['page', 'page_size', 'orderby', 'desc', 'name', 'id']

            for param in rag_params:
                value = request.GET.get(param)
                if value is not None:
                    params[param] = value

            # 如果指定了特定的知识库ID，验证用户是否有权限
            #验证老师，学生、创建者
            requested_id = params.get('id')
            if requested_id:
                if str(requested_id) not in user_kb_ids:
                    print(f"[WARN] 用户(ID:{user_id})无权限访问知识库(ID:{requested_id})")
                    return fail_api_response(
                        {
                            "code": 403,
                            "message": "Access denied: You don't have permission to access this knowledge base",
                            "dataset_id": requested_id
                        },
                        status=status.HTTP_403_FORBIDDEN
                    )


            # 设置默认值（按照RAG文档）
            if 'page' not in params:
                params['page'] = 1
            if 'page_size' not in params:
                params['page_size'] = 30
            if 'orderby' not in params:
                params['orderby'] = 'create_time'
            if 'desc' not in params:
                params['desc'] = 'true'

            print(f"[DEBUG] 转发到RAG的参数: {params}")

            # 调用RAG API
            resp = requests.get(
                f"{BASE_URL}/api/v1/datasets",
                headers=HEADERS,
                params=params,
                timeout=30
            )

            print(f"[DEBUG] RAG API响应状态码: {resp.status_code}")

            # 首先检查HTTP状态码
            if resp.status_code == 200:
                print(f"[DEBUG] RAG API响应内容: {resp.text}")

                try:
                    rag_data = resp.json()
                    business_code = rag_data.get("code", 0)
                    business_message = rag_data.get("message", "")

                    print(f"[DEBUG] RAG业务状态码: {business_code}")
                    print(f"[DEBUG] RAG业务消息: {business_message}")

                    # 检查业务状态码
                    if business_code == 0:
                        # 业务成功，继续原有的处理逻辑
                        original_datasets = rag_data.get('data', [])
                        # 获取用户有权限的知识库详细信息（从PG获取）
                        kb_dict = {kb.id: kb for kb in user_kbs}
                        print(f'用户有权限的知识库详细信息: {kb_dict}')

                        # 过滤用户有权限的知识库
                        filtered_datasets = []
                        user_course_semesters = _get_user_courses_and_semesters(user)
                        for dataset in original_datasets:
                            dataset_id = str(dataset.get('id', ''))
                            if dataset_id in user_kb_ids:
                                # 获取对应的PG知识库信息
                                kb = kb_dict.get(dataset_id)
                                # 处理embedding_model字段，去掉@及其后面的内容
                                if 'embedding_model' in dataset and dataset['embedding_model']:
                                    original_embedding_model = dataset['embedding_model']
                                    # 查找@符号的位置
                                    at_index = original_embedding_model.find('@')
                                    if at_index != -1:
                                        # 如果找到@符号，只保留@之前的部分
                                        dataset['embedding_model'] = original_embedding_model[:at_index]
                                        print(
                                            f"[DEBUG] 处理embedding_model: '{original_embedding_model}' -> '{dataset['embedding_model']}'")
                                    else:
                                        # 如果没有@符号，保持原值
                                        print(f"[DEBUG] embedding_model无需处理: '{original_embedding_model}'")

                                # 处理name字段，去掉最后一个下划线及其后面的内容
                                if 'name' in dataset and dataset['name']:
                                    original_name = dataset['name']
                                    # 查找最后一个下划线的位置
                                    last_underscore_index = original_name.rfind('_')
                                    if last_underscore_index != -1:
                                        # 如果找到下划线，只保留下划线之前的部分
                                        dataset['name'] = original_name[:last_underscore_index]
                                        print(
                                            f"[DEBUG] 处理name: '{original_name}' -> '{dataset['name']}'")
                                    else:
                                        # 如果没有下划线，保持原值
                                        print(f"[DEBUG] name无需处理: '{original_name}'")

                                # 添加数据源标识和用户信息
                                dataset["can_operate"] = (hasattr(user, 'role') and user.role == 1) or (kb.user_id == user_id)
                                dataset["data_source"] = "rag_filtered"
                                dataset["sync_time"] = datetime.now().isoformat()
                                dataset["user_id"] = user_id
                                dataset["user_name"] = user.username
                                dataset["course_count"] = kb.get_course_count()
                                dataset["course_names"] =  kb.get_course_names()
                                course_ids = kb.get_course_ids()
                                course_semesters = _merge_course_semester_info(course_ids, user_course_semesters)
                                dataset["course_semesters"] = course_semesters
                                filtered_datasets.append(dataset)
                            else:
                                print(f"[DEBUG] 过滤掉无权限的知识库: {dataset_id}")

                        # 更新返回数据
                        rag_data['data'] = filtered_datasets



                        # 添加调试信息到响应
                        rag_data["debug_info"] = {
                            "request_params": params,
                            "timestamp": datetime.now().isoformat(),
                            "user_id": user_id,
                            "user_name": user.username,
                            #'can_operate':(hasattr(user, 'role') and user.role == 1) or (kb.user_id == user_id),
                            "original_count": len(original_datasets),
                            "filtered_count": len(filtered_datasets),
                            "user_kb_ids": list(user_kb_ids),
                            "source": "rag_filtered",

                        }

                        return sucess_api_response(rag_data)

                    else:
                        # 业务失败，返回RAG的错误信息
                        print(f"[ERROR] RAG业务错误: 代码={business_code}, 消息={business_message}")

                        # 根据错误码返回相应的HTTP状态码
                        http_status = 400
                        if business_code == 102:
                            # embedding模型不存在的错误
                            http_status = 400
                            error_message = f"配置错误: {business_message}"
                        elif business_code in [401, 403]:
                            http_status = business_code
                            error_message = f"认证或权限错误: {business_message}"
                        elif business_code == 404:
                            http_status = 404
                            error_message = f"资源不存在: {business_message}"
                        elif business_code >= 500:
                            http_status = 500
                            error_message = f"服务器错误: {business_message}"
                        else:
                            error_message = f"获取知识库列表失败: {business_message}"

                        return fail_api_response({
                            "code": business_code,
                            "message": error_message,
                            "details": business_message,
                            "user_id": user_id,
                            "request_params": params
                        }, status=http_status)

                except json.JSONDecodeError as e:
                    print(f"[ERROR] 解析RAG响应JSON失败: {e}")
                    return fail_api_response({
                        "code": 500,
                        "message": "RAG服务响应格式错误",
                        "response_text": resp.text
                    }, status=500)
            else:
                # HTTP层面的错误
                print(f"[ERROR] RAG API HTTP请求失败: {resp.status_code}")
                print(f"[ERROR] 响应内容: {resp.text}")

                try:
                    error_data = resp.json()
                    return fail_api_response(error_data, status=resp.status_code)
                except json.JSONDecodeError:
                    return fail_api_response({
                        "code": resp.status_code,
                        "message": f"RAG API调用失败，状态码: {resp.status_code}",
                        "details": resp.text
                    }, status=resp.status_code)

        except requests.exceptions.Timeout:
            print(f"[ERROR] RAG API调用超时")
            return fail_api_response(
                {"code": 504, "message": "RAG API调用超时"},
                status=status.HTTP_504_GATEWAY_TIMEOUT
            )
        except requests.exceptions.ConnectionError:
            print(f"[ERROR] 无法连接到RAG API")
            return fail_api_response(
                {"code": 503, "message": "无法连接到RAG服务"},
                status=status.HTTP_503_SERVICE_UNAVAILABLE
            )
        except requests.exceptions.RequestException as e:
            print(f"[ERROR] RAG API网络请求失败: {e}")
            return fail_api_response(
                {"code": 500, "message": "RAG API网络请求失败", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        except Exception as e:
            print(f"[ERROR] 调用RAG List datasets接口失败: {e}")
            traceback.print_exc()
            return fail_api_response(
                {"code": 500, "message": "内部服务器错误", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


    # 在 _get_datasets_from_local 方法中简化数据结构
    def _get_datasets_from_local(self, request, user):
        user_id = user.id
        print(f"[INFO] 从本地数据库获取用户(ID:{user_id}, 用户名:{user.username})的知识库列表")

        try:
            # 获取查询参数
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 30))
            orderby = request.GET.get('orderby', 'created_at')
            desc = request.GET.get('desc', 'true').lower() == 'true'
            name_filter = request.GET.get('name', '')

            # 修改：直接从知识库表获取用户的知识库
            user_kbs = get_user_knowledge_bases(user)

            if not user_kbs:
                return sucess_api_response({
                    "code": 0,
                    "data": [],
                    "debug_info": {
                        "message": "用户没有关联的知识库",
                        "user_id": user_id,
                        "timestamp": datetime.now().isoformat(),
                        "source": "pg_filtered"
                    }
                })

            # 获取知识库详细信息
            all_datasets = []
            for kb in user_kbs:
                try:
                    # 应用名称过滤
                    if name_filter and name_filter.lower() not in kb.name.lower():
                        continue

                    # 构建数据
                    dataset_info = {
                        "id": kb.id,
                        "name": kb.name,
                        "description": kb.description,
                        "document_count": kb.document_count,
                        "course_count": kb.get_course_count(),
                        "course_names": kb.get_course_names(),
                        "course_names_text": kb.get_course_names_text(),
                        "created_at": kb.created_at.isoformat() if kb.created_at else None,
                        "updated_at": kb.updated_at.isoformat() if kb.updated_at else None,
                        "is_active": kb.is_active,
                        "creator_id": kb.user.id if kb.user else None,
                        "creator_name": kb.user.username if kb.user else "未知用户",
                        "data_source": "local_database",
                        "sync_time": datetime.now().isoformat(),
                        "can_operate":True if user.role==1 or user.role==0 else kb.user_id == user_id # 创建者可以操作知识库，老师也能操作自己的知识库
                    }

                    all_datasets.append(dataset_info)

                except Exception as e:
                    print(f"[ERROR] 获取知识库信息失败，ID:{kb.id}, 错误:{e}")
                    continue

            # 排序处理保持不变...
            if orderby == 'created_at':
                all_datasets.sort(key=lambda x: x.get('created_at', ''), reverse=desc)
            elif orderby == 'updated_at':
                all_datasets.sort(key=lambda x: x.get('updated_at', ''), reverse=desc)
            elif orderby == 'name':
                all_datasets.sort(key=lambda x: x.get('name', ''), reverse=desc)
            elif orderby == 'course_count':
                all_datasets.sort(key=lambda x: x.get('course_count', 0), reverse=desc)
            elif orderby == 'document_count':
                all_datasets.sort(key=lambda x: x.get('document_count', 0), reverse=desc)

            # 分页
            total_count = len(all_datasets)
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            paginated_datasets = all_datasets[start_idx:end_idx]

            # 构建 debug_info
            debug_info = {
                "request_params": {
                    "page": page,
                    "page_size": page_size,
                    "orderby": orderby,
                    "desc": desc,
                },
                "timestamp": datetime.now().isoformat(),
                "user_id": user_id,
                "user_name": user.username,
                "original_count": total_count,
                "filtered_count": len(paginated_datasets),
                "user_kb_ids": [str(ds["id"]) for ds in all_datasets],
                "source": "local_database"
            }
            # result = {
            #     "datasets": paginated_datasets,
            #     "total_count": total_count,
            #     "page": page,
            #     "page_size": page_size,
            #     "total_pages": (total_count + page_size - 1) // page_size,
            #     "source": "local_database",
            #     "timestamp": datetime.now().isoformat(),
            #     "statistics": {
            #         "total_knowledge_bases": total_count,
            #         "total_courses": sum(dataset.get('course_count', 0) for dataset in all_datasets),
            #         "total_documents": sum(dataset.get('document_count', 0) for dataset in all_datasets)
            #     }
            # }

            # 构建与RAG一致的返回结构
            result = {
                "code": 0,
                "data": paginated_datasets,
                "total_count": total_count,
                "page": page,
                "page_size": page_size,
                "total_pages": (total_count + page_size - 1) // page_size,
                "source": "local_database",
                "timestamp": datetime.now().isoformat(),
                "statistics": {
                    "total_knowledge_bases": total_count,
                    "total_courses": sum(dataset.get('course_count', 0) for dataset in all_datasets),
                    "total_documents": sum(dataset.get('document_count', 0) for dataset in all_datasets)
                },
                "debug_info": debug_info
            }

            # 外层包一层data，和RAG一致
            return sucess_api_response({"code": 0, "data": paginated_datasets, "debug_info": debug_info})

            return sucess_api_response(result)

        except Exception as e:
            print(f"[ERROR] 从本地数据库获取知识库列表失败: {e}")

            traceback.print_exc()
            return fail_api_response(
                {"error": "Failed to get datasets from local database", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @transaction.atomic()
    def post(self, request):
        """创建知识库时，创建成功的话，同时把'id，名称，描述，文档数'信息存到知识库表里"""
        try:
            # 从认证的用户对象获取用户信息
            user = get_test_user(request)
            user_id = user.id
            print(f"[INFO] 用户(ID:{user_id}, 用户名:{user.username})正在创建知识库")

            success, msg = ensure_bge_m3_in_gpustack()
            print(success, msg)
            if not success:
                return fail_api_response(
                    {"error": "bge-m3嵌入模型不存在或添加失败", "details": msg},
                    status=500
                )
            # 获取原始知识库名称
            original_name = request.data.get("name", "")
            if not original_name.strip():
                return fail_api_response({"error": "知识库名称不能为空"}, status=400)

            # 生成带用户ID的知识库名称
            kb_name_with_user_id = f"{original_name.strip()}_{user_id}"

            # 修改请求数据中的名称
            modified_request_data = request.data.copy()
            modified_request_data["name"] = kb_name_with_user_id

            print(f"[INFO] 原始知识库名称: {original_name}")
            print(f"[INFO] 生成的知识库名称: {kb_name_with_user_id}")

            # 调用外部API创建知识库（使用修改后的名称）
            resp = requests.post(f"{BASE_URL}/api/v1/datasets", headers=HEADERS, json=modified_request_data)

            if resp.status_code == 200:
                # 外部API创建成功，解析返回数据
                result = resp.json()
                print(result)
                external_data = result.get("data", {})
                kb_id = external_data.get("id")

                if not kb_id:
                    print("[ERROR] 外部API未返回知识库ID")
                    return fail_api_response("创建失败：未获取到知识库ID", status=500)

                # 从请求数据和返回数据中获取知识库信息
                # 注意：这里使用带用户ID的名称
                kb_name = external_data.get("name") or kb_name_with_user_id
                kb_description = external_data.get("description") or request.data.get("description", "")
                document_count = external_data.get("document_count", 0)  # 新创建的知识库文档数为0

                print(f"[INFO] 准备保存知识库到本地: ID={kb_id}, 名称={kb_name}")

                # 保存到本地知识库表 - 包含用户ID，使用带用户ID的名称
                success = add_knowledge_base(
                    kb_id=kb_id,
                    name=original_name,  # 使用不带用户ID的名称
                    description=kb_description,
                    document_count=document_count,
                    user_id=user_id  # 添加创建者ID
                )

                if success:
                    # 建立用户-知识库关联
                    # link_success = link_user_kb(user_id, kb_id)
                    # if link_success:
                    print(f"[INFO] 用户{user_id}创建知识库成功，本地ID: {kb_id}, 名称: {original_name}")

                    # 返回完整的知识库信息（包含本地数据库信息）
                    local_kb = get_knowledge_base_by_id(kb_id)
                    if local_kb:
                        result["data"]["local_info"] = {
                            "created_at": local_kb.created_at.isoformat() if local_kb.created_at else None,
                            "updated_at": local_kb.updated_at.isoformat() if local_kb.updated_at else None,
                            "creator_id": local_kb.user.id,
                            "creator_name": local_kb.user.username,
                            "document_count": local_kb.document_count,
                            "is_active": local_kb.is_active,
                            "original_name": original_name,  # 添加原始名称信息
                            "final_name": kb_name  # 添加最终名称信息
                        }

                else:
                    print(f"[ERROR] 保存知识库到本地数据库失败")
                    # 即使本地保存失败，也返回外部API的成功结果

                return sucess_api_response(result, status=resp.status_code)
            else:
                # 外部API创建失败
                print(f"[ERROR] 外部API创建知识库失败: {resp.status_code}")
                return fail_api_response(resp.json(), status=resp.status_code)

        except Exception as e:
            print(f"[ERROR] 创建知识库过程中发生异常: {e}")
            traceback.print_exc()
            return fail_api_response(f"创建知识库失败: {str(e)}", status=500)

    # @transaction.atomic()
    # def post(self, request):
    #     """创建知识库时，创建成功的话，同时把'id，名称，描述，文档数'信息存到知识库表里"""
    #     try:
    #         # 从认证的用户对象获取用户信息
    #         user = get_test_user(request)
    #         user_id = user.id
    #         print(f"[INFO] 用户(ID:{user_id}, 用户名:{user.username})正在创建知识库")
    #
    #         # 调用外部API创建知识库
    #         resp = requests.post(f"{BASE_URL}/api/v1/datasets", headers=HEADERS, json=request.data)
    #
    #         if resp.status_code == 200:
    #             # 外部API创建成功，解析返回数据
    #             result = resp.json()
    #             print(result)
    #             external_data = result.get("data", {})
    #             kb_id = external_data.get("id")
    #
    #             if not kb_id:
    #                 print("[ERROR] 外部API未返回知识库ID")
    #                 return fail_api_response("创建失败：未获取到知识库ID", status=500)
    #
    #             # 从请求数据和返回数据中获取知识库信息
    #             kb_name = external_data.get("name") or request.data.get("name", "")
    #             kb_description = external_data.get("description") or request.data.get("description", "")
    #             document_count = external_data.get("document_count", 0)  # 新创建的知识库文档数为0
    #
    #             print(f"[INFO] 准备保存知识库到本地: ID={kb_id}, 名称={kb_name}")
    #
    #             # 保存到本地知识库表 - 包含用户ID
    #             success = add_knowledge_base(
    #                 kb_id=kb_id,
    #                 name=kb_name,
    #                 description=kb_description,
    #                 document_count=document_count,
    #                 user_id=user_id  # 添加创建者ID
    #             )
    #
    #             if success:
    #                 # 建立用户-知识库关联
    #                 link_success = link_user_kb(user_id, kb_id)
    #                 if link_success:
    #                     print(f"[INFO] 用户{user_id}创建知识库成功，本地ID: {kb_id}")
    #
    #                     # 返回完整的知识库信息（包含本地数据库信息）
    #                     local_kb = get_knowledge_base_by_id(kb_id)
    #                     if local_kb:
    #                         result["data"]["local_info"] = {
    #                             "created_at": local_kb.created_at.isoformat() if local_kb.created_at else None,
    #                             "updated_at": local_kb.updated_at.isoformat() if local_kb.updated_at else None,
    #                             "creator_id": local_kb.user.id,
    #                             "creator_name": local_kb.user.username,
    #                             "document_count": local_kb.document_count,
    #                             "is_active": local_kb.is_active
    #                         }
    #                 else:
    #                     print(f"[WARN] 用户{user_id}创建知识库成功，但关联失败")
    #             else:
    #                 print(f"[ERROR] 保存知识库到本地数据库失败")
    #                 # 即使本地保存失败，也返回外部API的成功结果
    #
    #             return sucess_api_response(result, status=resp.status_code)
    #         else:
    #             # 外部API创建失败
    #             print(f"[ERROR] 外部API创建知识库失败: {resp.status_code}")
    #             return fail_api_response(resp.json(), status=resp.status_code)
    #
    #     except Exception as e:
    #         print(f"[ERROR] 创建知识库过程中发生异常: {e}")
    #
    #         traceback.print_exc()
    #         return fail_api_response(f"创建知识库失败: {str(e)}", status=500)

    @transaction.atomic()
    def delete(self, request):
        """删除知识库时，先删除本地数据，再调用RAG接口删除"""
        # 验证用户认证
        user = get_test_user(request)
        user_id = user.id
        print(f"[INFO] 用户(ID:{user.id}, 用户名:{user.username})正在删除知识库")

        # 防护1：检查必须有dataset_ids字段
        if "ids" not in request.data:
            return fail_api_response(
                {"error": "Missing dataset_ids parameter"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 防护2：检查dataset_ids必须是数组
        if not isinstance(request.data.get("ids"), list):
            return fail_api_response(
                {"error": "dataset_ids must be an array"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 防护3：检查数组不能为空
        if len(request.data["ids"]) == 0:
            return fail_api_response(
                {"error": "dataset_ids cannot be empty"},
                status=status.HTTP_400_BAD_REQUEST
            )

        dataset_ids = request.data["ids"]

        try:
            # 先删除本地数据库中的相关记录
            deleted_user_links = 0
            deleted_course_links = 0
            deleted_kbs = 0
            local_delete_errors = []

            print(f"[INFO] 开始删除本地数据库记录，涉及知识库: {dataset_ids}")

            for kb_id in dataset_ids:
                try:
                    kb_id_str = str(kb_id)
                    print(f"[INFO] 正在删除知识库(ID:{kb_id})的本地数据...")

                    # 删除该知识库的所有用户关联记录
                    # user_link_count = delete_all_kb_links(kb_id_str)
                    # deleted_user_links += user_link_count

                    # 删除该知识库的所有课程关联记录
                    course_link_count = delete_all_kb_course_links(kb_id_str)
                    deleted_course_links += course_link_count

                    # 删除知识库本身（软删除）
                    kb_deleted = delete_knowledge_base(kb_id_str, force=False)
                    if kb_deleted:
                        deleted_kbs += 1

                    print(f"[INFO] 知识库(ID:{kb_id})本地数据删除完成 - 课程关联:{course_link_count}, 知识库删除:{kb_deleted}")
                except Exception as e:
                    error_msg = f"删除知识库(ID:{kb_id})的本地记录失败: {e}"
                    print(f"[ERROR] {error_msg}")
                    local_delete_errors.append(error_msg)

            print(
                f"[INFO] 本地数据删除完成 - 知识库:{deleted_kbs}, 用户关联:{deleted_user_links}, 课程关联:{deleted_course_links}")

            # 再调用RAG接口删除
            rag_delete_success = False
            rag_response = None

            try:
                print(f"[INFO] 开始调用RAG删除接口...")
                resp = requests.delete(
                    f"{BASE_URL}/api/v1/datasets",
                    headers={**HEADERS, "Content-Type": "application/json"},
                    json={"ids": dataset_ids},
                    timeout=30
                )

                print(f"[INFO] RAG删除接口响应状态码: {resp.status_code}")

                if resp.status_code == 200:
                    rag_delete_success = True
                    rag_response = resp.json()
                    print(f"[INFO] RAG删除成功")
                else:
                    print(f"[WARN] RAG删除失败: {resp.status_code} - {resp.text}")
                    rag_response = {
                        "error": f"RAG删除失败，状态码: {resp.status_code}",
                        "details": resp.text
                    }

            except requests.exceptions.Timeout:
                error_msg = "RAG删除请求超时"
                print(f"[WARN] {error_msg}")
                rag_response = {"error": error_msg}
            except requests.exceptions.ConnectionError:
                error_msg = "无法连接到RAG服务"
                print(f"[WARN] {error_msg}")
                rag_response = {"error": error_msg}
            except requests.exceptions.RequestException as e:
                error_msg = f"RAG删除网络请求失败: {str(e)}"
                print(f"[WARN] {error_msg}")
                rag_response = {"error": error_msg}

            # 构建响应数据
            response_data = {
                "local_delete_info": {
                    "deleted_knowledge_bases": deleted_kbs,
                    "deleted_user_links": deleted_user_links,
                    "deleted_course_links": deleted_course_links,
                    "local_delete_errors": local_delete_errors
                },
                "rag_delete_info": {
                    "success": rag_delete_success,
                    "response": rag_response
                },
                "summary": {
                    "total_requested": len(dataset_ids),
                    "local_deleted": deleted_kbs,
                    "rag_deleted": rag_delete_success,
                    "operation_time": datetime.now().isoformat()
                }
            }

            # 根据结果返回相应的响应
            if deleted_kbs > 0:  # 本地删除有成功的
                if rag_delete_success:
                    # 本地和RAG都删除成功
                    return sucess_api_response(response_data, status=200)
                else:
                    # 本地删除成功，但RAG删除失败
                    response_data["message"] = "本地数据删除成功，但RAG删除失败"
                    return sucess_api_response(response_data, status=207)  # 207 Multi-Status
            else:
                # 本地删除失败
                if local_delete_errors:
                    response_data["message"] = "本地数据删除失败"
                    return fail_api_response(response_data, status=500)
                else:
                    # 没有要删除的数据
                    response_data["message"] = "没有找到要删除的知识库"
                    return fail_api_response(response_data, status=404)

        except Exception as e:
            print(f"[ERROR] 删除知识库过程中发生异常: {e}")

            traceback.print_exc()
            return fail_api_response(
                {
                    "error": "删除知识库失败",
                    "details": str(e),
                    "operation_time": datetime.now().isoformat()
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class DatasetDetailView(APIView):
    permission_map = {
        'put': ['knowledge_base.change_knowledge_bases'],
    }
    @transaction.atomic()
    def put_course_kb(self, request, dataset_id,):
        """修改知识库时，支持同步绑定课程，支持单个或多个课程ID"""
        user = get_test_user(request)
        user_id = user.id
        print(f"[INFO] 用户(ID:{user.id}, 用户名:{user.username})正在更新知识库: {dataset_id}")

        try:
            print(f"[DEBUG] 开始更新知识库: {dataset_id}")
            print(f"[DEBUG] 请求数据: {request.data}")
            success, msg = ensure_bge_m3_in_gpustack()
            print(success, msg)
            if not success:
                return fail_api_response(
                    {"error": "bge-m3嵌入模型不存在或添加失败", "details": msg},
                    status=500
                )

            # 先调用外部API更新
            url = f"{BASE_URL}/api/v1/datasets/{dataset_id}"
            print(f"[DEBUG] 调用外部API: {url}")
            # 构造只包含非 course_ 开头的字段的字典
            rag_update_data = {k: v for k, v in request.data.items() if not k.startswith("course_")}

            # 调用外部API
            resp = requests.put(url, headers=HEADERS, json=rag_update_data)

            print(f"[DEBUG] 外部API响应状态码: {resp.status_code}")

            if resp.status_code == 200:
                print(f"[DEBUG] 外部API响应内容: {resp.text}")

                try:
                    result = resp.json()
                    business_code = result.get("code", 0)
                    business_message = result.get("message", "")

                    print(f"[DEBUG] RAG业务状态码: {business_code}")
                    print(f"[DEBUG] RAG业务消息: {business_message}")

                    if business_code == 0:
                        external_data = result.get("data", {})
                        new_name = external_data.get("name") or request.data.get("name")
                        new_description = external_data.get("description") or request.data.get("description")
                        new_document_count = external_data.get("document_count") or request.data.get("document_count")


                        # 更新本地知识库表
                        update_success = update_knowledge_base(
                            kb_id=dataset_id,
                            name=new_name,
                            description=new_description,
                            document_count=new_document_count
                        )

                        # 处理课程绑定
                        course_ids = request.data.get('course_ids') or request.data.get('course_id')
                        if course_ids:
                            if isinstance(course_ids, str) or isinstance(course_ids, int):
                                course_ids = [course_ids]
                            bind_results = []
                            for course_id in course_ids:
                                course = get_course_by_id(course_id)
                                if not course:
                                    return fail_api_response({
                                        "code": 400,
                                        "message": "课程ID不存在",
                                        "course_id": course_id,
                                        "dataset_id": dataset_id
                                    }, status=400)

                                # 检查是否已绑定
                                existing_links = get_kb_course_links(dataset_id)
                                already_bound = any(str(link.course.id) == str(course_id) for link in existing_links)
                                if already_bound:
                                    bind_results.append({
                                        "course_id": course_id,
                                        "success": False,
                                        "message": f"知识库{dataset_id}和课程{course_id}已经绑定"
                                    })
                                    continue
                                # 执行绑定
                                success = add_course_kb_link(course_id, dataset_id)
                                if success:
                                    bind_results.append({
                                        "course_id": course_id,
                                        "success": True,
                                        "message": "绑定成功"
                                    })
                                else:
                                    bind_results.append({
                                        "course_id": course_id,
                                        "success": False,
                                        "message": "绑定失败，数据库操作异常"
                                    })
                        else:
                            bind_results = None

                        # 获取更新后的本地数据库信息
                        updated_kb = get_knowledge_base_by_id(dataset_id)
                        success_res = {
                            "code": 0,
                            "message": "知识库更新成功",
                            "data": {
                                "updated_at": updated_kb.updated_at.isoformat() if updated_kb and updated_kb.updated_at else None,
                                "creator_id": updated_kb.user_id if updated_kb else None,
                                "document_count": updated_kb.document_count if updated_kb else None,
                                "kb_id": dataset_id,
                                "name": updated_kb.name if updated_kb else new_name,
                                "description": updated_kb.description if updated_kb else new_description,
                                "course_bind_results": bind_results
                            }
                        }
                        return sucess_api_response(success_res, status=200)

                    else:
                        # 业务失败，返回RAG的错误信息
                        print(f"[ERROR] RAG业务错误: 代码={business_code}, 消息={business_message}")
                        http_status = 400
                        if business_code == 102:
                            http_status = 400
                            error_message = f"配置错误: {business_message}"
                        elif business_code in [401, 403]:
                            http_status = business_code
                            error_message = f"认证或权限错误: {business_message}"
                        elif business_code >= 500:
                            http_status = 500
                            error_message = f"服务器错误: {business_message}"
                        else:
                            error_message = f"更新失败: {business_message}"

                        return fail_api_response({
                            "code": business_code,
                            "message": error_message,
                            "details": business_message,
                            "dataset_id": dataset_id
                        }, status=http_status)

                except json.JSONDecodeError as e:
                    print(f"[ERROR] 解析RAG响应JSON失败: {e}")
                    return fail_api_response({
                        "code": 500,
                        "message": "服务器响应格式错误",
                        "response_text": resp.text
                    }, status=500)
            else:
                print(f"[ERROR] 外部API HTTP请求失败: {resp.status_code}")
                print(f"[ERROR] 响应内容: {resp.text}")

                try:
                    error_data = resp.json()
                    return fail_api_response(error_data, status=resp.status_code)
                except json.JSONDecodeError:
                    return fail_api_response({
                        "code": resp.status_code,
                        "message": f"HTTP请求失败，状态码: {resp.status_code}",
                        "response_text": resp.text
                    }, status=resp.status_code)

        except Exception as e:
            print(f"[ERROR] 更新知识库过程中发生异常: {e}")
            traceback.print_exc()
            return fail_api_response({
                "code": 500,
                "message": f"更新知识库失败: {str(e)}",
                "dataset_id": dataset_id
            }, status=500)

    @transaction.atomic()
    def put(self, request, dataset_id):
        """修改知识库时，修改成功的话，同时修改知识库表的信息"""
        # 验证用户认证
        user = get_test_user(request)
        user_id = user.id
        print(f"[INFO] 用户(ID:{user.id}, 用户名:{user.username})正在更新知识库: {dataset_id}")

        try:
            print(f"[DEBUG] 开始更新知识库: {dataset_id}")
            print(f"[DEBUG] 请求数据: {request.data}")
            success, msg = ensure_bge_m3_in_gpustack()
            print(success, msg)
            if not success:
                return fail_api_response(
                    {"error": "bge-m3嵌入模型不存在或添加失败", "details": msg},
                    status=500
                )
            # 先调用外部API更新
            url = f"{BASE_URL}/api/v1/datasets/{dataset_id}"
            print(f"[DEBUG] 调用外部API: {url}")

            resp = requests.put(url, headers=HEADERS, json=request.data)
            print(f"[DEBUG] 外部API响应状态码: {resp.status_code}")

            # 首先检查HTTP状态码
            if resp.status_code == 200:
                print(f"[DEBUG] 外部API响应内容: {resp.text}")

                try:
                    result = resp.json()
                    business_code = result.get("code", 0)
                    business_message = result.get("message", "")

                    print(f"[DEBUG] RAG业务状态码: {business_code}")
                    print(f"[DEBUG] RAG业务消息: {business_message}")

                    # 检查业务状态码
                    if business_code == 0:
                        # 业务成功，继续本地更新逻辑
                        external_data = result.get("data", {})

                        # 获取更新的字段
                        new_name = external_data.get("name") or request.data.get("name")
                        new_description = external_data.get("description") or request.data.get("description")
                        new_document_count = external_data.get("document_count") or request.data.get("document_count")

                        print(f"[DEBUG] 准备更新本地数据库:")
                        print(f"  - name: {new_name}")
                        print(f"  - description: {new_description}")
                        print(f"  - document_count: {new_document_count}")

                        # 检查知识库是否存在于本地数据库
                        # existing_kb = get_knowledge_base_by_id(dataset_id)
                        # if not existing_kb:
                        #     print(f"[WARN] 本地数据库中不存在知识库 {dataset_id}，跳过本地更新")
                        #     return sucess_api_response(result, status=200)

                        # 更新本地知识库表
                        print(f"[DEBUG] 调用update_knowledge_base函数")
                        update_success = update_knowledge_base(
                            kb_id=dataset_id,
                            name=new_name,
                            description=new_description,
                            document_count=new_document_count
                        )

                        print(f"[DEBUG] 本地更新结果: {update_success}")

                        if update_success:
                            print(f"[INFO] 本地知识库更新成功: ID={dataset_id}")

                            # 获取更新后的本地数据库信息
                            updated_kb = get_knowledge_base_by_id(dataset_id)
                            if updated_kb:
                                success_res = {
                                    "code": 0,
                                    "message": "知识库更新成功",
                                    "data": {
                                        "updated_at": updated_kb.updated_at.isoformat() if updated_kb.updated_at else None,
                                        "creator_id": updated_kb.user_id,
                                        "document_count": updated_kb.document_count,
                                        "kb_id": dataset_id,
                                        "name": updated_kb.name,
                                        "description": updated_kb.description
                                    }
                                }
                            else:
                                success_res = result
                        else:
                            print(f"[WARN] 本地知识库更新失败: ID={dataset_id}")
                            success_res = result

                        return sucess_api_response(success_res, status=200)

                    else:
                        # 业务失败，返回RAG的错误信息
                        print(f"[ERROR] RAG业务错误: 代码={business_code}, 消息={business_message}")

                        # 根据错误码返回相应的HTTP状态码
                        http_status = 400
                        if business_code == 102:
                            # embedding模型不存在的错误
                            http_status = 400
                            error_message = f"配置错误: {business_message}"
                        elif business_code in [401, 403]:
                            http_status = business_code
                            error_message = f"认证或权限错误: {business_message}"
                        elif business_code >= 500:
                            http_status = 500
                            error_message = f"服务器错误: {business_message}"
                        else:
                            error_message = f"更新失败: {business_message}"

                        return fail_api_response({
                            "code": business_code,
                            "message": error_message,
                            "details": business_message,
                            "dataset_id": dataset_id
                        }, status=http_status)

                except json.JSONDecodeError as e:
                    print(f"[ERROR] 解析RAG响应JSON失败: {e}")
                    return fail_api_response({
                        "code": 500,
                        "message": "服务器响应格式错误",
                        "response_text": resp.text
                    }, status=500)
            else:
                # HTTP层面的错误
                print(f"[ERROR] 外部API HTTP请求失败: {resp.status_code}")
                print(f"[ERROR] 响应内容: {resp.text}")

                try:
                    error_data = resp.json()
                    return fail_api_response(error_data, status=resp.status_code)
                except json.JSONDecodeError:
                    return fail_api_response({
                        "code": resp.status_code,
                        "message": f"HTTP请求失败，状态码: {resp.status_code}",
                        "response_text": resp.text
                    }, status=resp.status_code)

        except Exception as e:
            print(f"[ERROR] 更新知识库过程中发生异常: {e}")
            traceback.print_exc()
            return fail_api_response({
                "code": 500,
                "message": f"更新知识库失败: {str(e)}",
                "dataset_id": dataset_id
            }, status=500)

'''----------知识库管理接口结束--------'''

'''------------知识库构建接口---------'''
class DocumentListView(APIView):
    parser_classes = [MultiPartParser, FormParser, JSONParser]
    permission_map = {
        'get': ['knowledge_base.view_knowledge_bases_documents'],
        'post': ['knowledge_base.upload_knowledge_bases_documents'],
        'delete':['knowledge_base.delete_knowledge_bases_documents']
    }

    def get(self, request, dataset_id):
        """列出某个知识库里的文档"""
        url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents"
        resp = requests.get(url, headers=HEADERS, params=request.query_params)
        return sucess_api_response(resp.json(), resp.status_code)


    @transaction.atomic()
    def post(self, request, dataset_id):
        """上传文档，支持多文件，兼容浏览器上传，成功后更新本地知识库的文档数"""
        url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents"
        file_list = request.FILES.getlist('file')
        if not file_list:
            return fail_api_response({"code": 101, "message": "No file part!"}, status=400)

        # 记录上传的文件数量
        upload_file_count = len(file_list)
        print(f"[INFO] 准备上传 {upload_file_count} 个文件到知识库(ID:{dataset_id})")

        # 文件大小预检查
        # MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
        # MAX_TOTAL_SIZE = 200 * 1024 * 1024  # 200MB
        #
        # total_size = 0
        # for file in file_list:
        #     if file.size > MAX_FILE_SIZE:
        #         return fail_api_response({
        #             "code": 413,
        #             "message": f"文件 '{file.name}' 过大，单个文件最大限制 {MAX_FILE_SIZE // (1024*1024)}MB",
        #             "file_size": f"{file.size / (1024*1024):.2f}MB",
        #             "limit": f"{MAX_FILE_SIZE // (1024*1024)}MB"
        #         }, status=413)
        #     total_size += file.size
        #
        # if total_size > MAX_TOTAL_SIZE:
        #     return fail_api_response({
        #         "code": 413,
        #         "message": f"文件总大小过大，总限制 {MAX_TOTAL_SIZE // (1024*1024)}MB",
        #         "total_size": f"{total_size / (1024*1024):.2f}MB",
        #         "limit": f"{MAX_TOTAL_SIZE // (1024*1024)}MB"
        #     }, status=413)

        # 添加详细的调试信息
        print(f"[DEBUG] 接收到 {len(file_list)} 个文件")
        print(f"[DEBUG] User-Agent: {request.META.get('HTTP_USER_AGENT', 'Unknown')}")

        processed_files = []
        for file in file_list:
            # 详细记录文件信息
            print(f"[DEBUG] 文件名: {file.name}")
            print(f"[DEBUG] 文件大小: {file.size} bytes")
            print(f"[DEBUG] 原始Content-Type: {file.content_type}")

            # 浏览器兼容性处理
            file_name = file.name
            file_ext = os.path.splitext(file_name)[1].lower()

            # 重新设置正确的Content-Type（浏览器可能设置错误）
            content_type = file.content_type
            if file_ext == '.pdf' and content_type != 'application/pdf':
                content_type = 'application/pdf'
                print(f"[DEBUG] 修正PDF文件的Content-Type: {file.content_type} -> {content_type}")
            elif file_ext == '.txt' and not content_type.startswith('text/'):
                content_type = 'text/plain'
                print(f"[DEBUG] 修正TXT文件的Content-Type: {file.content_type} -> {content_type}")
            elif file_ext in ['.doc', '.docx'] and 'word' not in content_type:
                if file_ext == '.docx':
                    content_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                else:
                    content_type = 'application/msword'
                print(f"[DEBUG] 修正Word文件的Content-Type: {file.content_type} -> {content_type}")

            # 文件名安全处理（移除可能的特殊字符）
            safe_filename = file_name.replace(' ', '_').replace('(', '').replace(')', '')
            if safe_filename != file_name:
                print(f"[DEBUG] 文件名安全处理: {file_name} -> {safe_filename}")
                file_name = safe_filename

            # 检查文件是否为空
            if file.size == 0:
                return fail_api_response({
                    "code": 400,
                    "message": f"文件为空: {file.name}"
                }, status=400)

            processed_files.append((file_name, file, content_type))

        # 构建文件列表
        files = [('file', (name, file_obj, content_type)) for name, file_obj, content_type in processed_files]

        print(f"[DEBUG] 准备发送到RAG的文件信息:")
        for name, _, content_type in processed_files:
            print(f"  - {name}: {content_type}")

        try:
            # 发送请求，确保不包含Content-Type头（让requests自动处理）
            headers = {"Authorization": HEADERS["Authorization"]}
            resp = requests.post(url, headers=headers, files=files)

            print(f"[DEBUG] RAG响应状态码: {resp.status_code}")
            print(f"[DEBUG] RAG响应内容: {resp.text}")

            # 修改响应处理逻辑 - 重点修改部分
            if resp.status_code == 200:
                try:
                    response_data = resp.json()
                    business_code = response_data.get("code", 0)
                    business_message = response_data.get("message", "")
                    
                    print(f"[DEBUG] RAG业务状态码: {business_code}")
                    print(f"[DEBUG] RAG业务消息: {business_message}")
                    
                    # 根据业务状态码判断成功或失败
                    if business_code == 0:
                        # 业务成功 - 继续原有的成功处理逻辑
                        return self._handle_upload_success(response_data, dataset_id, upload_file_count, processed_files)
                    else:
                        # 业务失败 - 根据具体错误码返回对应错误
                        return self._handle_upload_business_error(business_code, business_message, processed_files)
                        
                except json.JSONDecodeError as e:
                    print(f"[ERROR] 解析RAG响应JSON失败: {e}")
                    return fail_api_response({
                        "code": 500,
                        "message": "服务器响应格式错误",
                        "response_text": resp.text
                    }, status=500)
            else:
                # HTTP层面的错误（非200状态码）
                print(f"[ERROR] RAG HTTP请求失败: {resp.status_code}")
                return fail_api_response({
                    "code": resp.status_code,
                    "message": f"网络请求失败，状态码: {resp.status_code}",
                    "response_text": resp.text
                }, status=resp.status_code)

        except Exception as e:
            print(f"[ERROR] 上传文档失败: {e}")
            traceback.print_exc()
            return fail_api_response({
                "code": 500,
                "message": f"上传文档失败: {str(e)}"
            }, status=500)

    def _handle_upload_business_error(self, business_code, business_message, processed_files):
        """处理业务层面的上传错误"""
        try:
            # 解码Unicode转义字符
            if isinstance(business_message, str) and '\\u' in business_message:
                try:
                    business_message = business_message.encode().decode('unicode_escape')
                except:
                    pass  # 如果解码失败，使用原始消息
            
            print(f"[ERROR] RAG业务错误 - 代码: {business_code}, 消息: {business_message}")
            
            # 根据业务错误码进行分类处理
            if business_code == 413 or "Request Entity Too Large" in business_message:
                # 文件过大错误
                return fail_api_response({
                    "code": 413,
                    "message": "文件过大，超出RAG服务上传限制",
                    "details": business_message,
                    "suggestion": "请减小文件大小或分批上传",
                    "uploaded_files": [{"name": name, "type": ct} for name, _, ct in processed_files]
                }, status=413)
                
            elif business_code == 500 or "not been supported yet" in business_message:
                # 文件类型不支持错误
                return fail_api_response({
                    "code": 400,
                    "message": "文件类型不受RAG支持",
                    "details": business_message,
                    "uploaded_files": [{"name": name, "type": ct} for name, _, ct in processed_files],
                    "suggestion": "请检查文件格式，支持的格式包括：PDF、DOC、DOCX、TXT等"
                }, status=400)
                
            elif business_code == 400:
                # 一般的客户端错误
                return fail_api_response({
                    "code": 400,
                    "message": f"上传参数错误: {business_message}",
                    "details": business_message,
                    "uploaded_files": [{"name": name, "type": ct} for name, _, ct in processed_files]
                }, status=400)
                
            elif business_code in [401, 403]:
                # 认证或权限错误
                return fail_api_response({
                    "code": business_code,
                    "message": f"认证或权限错误: {business_message}",
                    "details": business_message
                }, status=business_code)
                
            elif business_code >= 500:
                # 服务器错误
                return fail_api_response({
                    "code": business_code,
                    "message": f"RAG服务器错误: {business_message}",
                    "details": business_message,
                    "suggestion": "请稍后重试或联系管理员"
                }, status=500)
                
            else:
                # 其他未知错误
                return fail_api_response({
                    "code": business_code,
                    "message": f"RAG服务错误: {business_message}",
                    "details": business_message,
                    "uploaded_files": [{"name": name, "type": ct} for name, _, ct in processed_files]
                }, status=400)
                
        except Exception as e:
            print(f"[ERROR] 处理业务错误时异常: {e}")
            return fail_api_response({
                "code": 500,
                "message": "处理上传错误时发生异常",
                "details": str(e)
            }, status=500)

    def _handle_upload_success(self, response_data, dataset_id, upload_file_count, processed_files):
        """处理上传成功的情况"""
        try:
            # 上传成功后，更新本地知识库的文档数
            current_kb = get_knowledge_base_by_id(dataset_id)
            if current_kb:
                new_document_count = current_kb.document_count + upload_file_count

                update_success = update_knowledge_base(
                    kb_id=dataset_id,
                    document_count=new_document_count
                )

                if update_success:
                    print(f"[INFO] 知识库(ID:{dataset_id})文档数已更新：{current_kb.document_count} -> {new_document_count}")

                    # 构建增强的响应数据
                    if isinstance(response_data, dict) and response_data.get("code") == 0:
                        uploaded_documents = response_data.get("data", [])
                    elif isinstance(response_data, list):
                        uploaded_documents = response_data
                    else:
                        uploaded_documents = []

                    enhanced_response = {
                        "code": 0,
                        "message": "文档上传成功",
                        "data": {
                            "uploaded_documents": uploaded_documents,
                            "upload_summary": {
                                "total_files": upload_file_count,
                                "successful_uploads": len(uploaded_documents),
                                "failed_uploads": max(0, upload_file_count - len(uploaded_documents))
                            },
                            "local_update_info": {
                                "previous_document_count": current_kb.document_count,
                                "uploaded_files": upload_file_count,
                                "new_document_count": new_document_count,
                                "update_time": datetime.now().isoformat()
                            },
                            "document_details": []
                        }
                    }

                    # 解析每个文档的详细信息
                    for doc in uploaded_documents:
                        if isinstance(doc, dict):
                            doc_detail = {
                                "document_id": doc.get("id"),
                                "name": doc.get("name"),
                                "location": doc.get("location"),
                                "size": doc.get("size"),
                                "type": doc.get("type"),
                                "chunk_method": doc.get("chunk_method"),
                                "run_status": doc.get("run"),
                                "parser_config": doc.get("parser_config", {}),
                                "dataset_id": doc.get("dataset") or dataset_id,
                                "created_by": doc.get("created_by")
                            }
                            enhanced_response["data"]["document_details"].append(doc_detail)

                    return sucess_api_response(enhanced_response, 200)
                else:
                    print(f"[WARN] 更新知识库(ID:{dataset_id})文档数失败")
            else:
                print(f"[WARN] 未找到本地知识库(ID:{dataset_id})，跳过文档数更新")

            # 如果本地更新失败，直接返回原始成功响应
            return sucess_api_response(response_data, 200)

        except Exception as e:
            print(f"[ERROR] 处理上传成功时发生异常: {e}")
            traceback.print_exc()
            # 即使处理异常，也返回原始成功响应
            return sucess_api_response(response_data, 200)
        # def post(self, request, dataset_id):
            """上传文档，支持多文件，兼容浏览器上传，成功后更新本地知识库的文档数"""
            url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents"
            file_list = request.FILES.getlist('file')
            if not file_list:
                return fail_api_response({"code": 101, "message": "No file part!"}, status=400)

            # 记录上传的文件数量
            upload_file_count = len(file_list)
            print(f"[INFO] 准备上传 {upload_file_count} 个文件到知识库(ID:{dataset_id})")

            # 添加详细的调试信息
            print(f"[DEBUG] 接收到 {len(file_list)} 个文件")
            print(f"[DEBUG] User-Agent: {request.META.get('HTTP_USER_AGENT', 'Unknown')}")

            processed_files = []
            for file in file_list:
                # 详细记录文件信息
                print(f"[DEBUG] 文件名: {file.name}")
                print(f"[DEBUG] 文件大小: {file.size} bytes")
                print(f"[DEBUG] 原始Content-Type: {file.content_type}")

                # 浏览器兼容性处理
                file_name = file.name
                file_ext = os.path.splitext(file_name)[1].lower()

                # 重新设置正确的Content-Type（浏览器可能设置错误）
                content_type = file.content_type
                if file_ext == '.pdf' and content_type != 'application/pdf':
                    content_type = 'application/pdf'
                    print(f"[DEBUG] 修正PDF文件的Content-Type: {file.content_type} -> {content_type}")
                elif file_ext == '.txt' and not content_type.startswith('text/'):
                    content_type = 'text/plain'
                    print(f"[DEBUG] 修正TXT文件的Content-Type: {file.content_type} -> {content_type}")
                elif file_ext in ['.doc', '.docx'] and 'word' not in content_type:
                    if file_ext == '.docx':
                        content_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                    else:
                        content_type = 'application/msword'
                    print(f"[DEBUG] 修正Word文件的Content-Type: {file.content_type} -> {content_type}")

                # 文件名安全处理（移除可能的特殊字符）
                safe_filename = file_name.replace(' ', '_').replace('(', '').replace(')', '')
                if safe_filename != file_name:
                    print(f"[DEBUG] 文件名安全处理: {file_name} -> {safe_filename}")
                    file_name = safe_filename

                # 检查文件是否为空
                if file.size == 0:
                    return fail_api_response({
                        "code": 400,
                        "message": f"文件为空: {file.name}"
                    }, status=400)

                processed_files.append((file_name, file, content_type))

            # 构建文件列表
            files = [('file', (name, file_obj, content_type)) for name, file_obj, content_type in processed_files]

            print(f"[DEBUG] 准备发送到RAG的文件信息:")
            for name, _, content_type in processed_files:
                print(f"  - {name}: {content_type}")

            try:
                # 发送请求，确保不包含Content-Type头（让requests自动处理）
                headers = {"Authorization": HEADERS["Authorization"]}
                resp = requests.post(url, headers=headers, files=files)

                print(f"[DEBUG] RAG响应状态码: {resp.status_code}")
                print(f"[DEBUG] RAG响应内容: {resp.text}")

                # 处理响应
                if resp.status_code != 200:
                    try:
                        error_data = resp.json()
                        if "not been supported yet" in str(error_data):
                            return fail_api_response({
                                "code": 400,
                                "message": "文件类型不受RAG支持",
                                "details": error_data,
                                "uploaded_files": [{"name": name, "type": ct} for name, _, ct in processed_files]
                            }, status=400)
                        else:
                            return fail_api_response(error_data, status=resp.status_code)
                    except:
                        return fail_api_response({
                            "code": resp.status_code,
                            "message": f"上传失败，状态码: {resp.status_code}",
                            "response_text": resp.text
                        }, status=resp.status_code)

                # 上传成功后，更新本地知识库的文档数
                try:
                    # 获取当前知识库信息
                    current_kb = get_knowledge_base_by_id(dataset_id)
                    if current_kb:
                        new_document_count = current_kb.document_count + upload_file_count

                        update_success = update_knowledge_base(
                            kb_id=dataset_id,
                            document_count=new_document_count
                        )

                        if update_success:
                            print(
                                f"[INFO] 知识库(ID:{dataset_id})文档数已更新：{current_kb.document_count} -> {new_document_count}")

                            # 解析RAG响应数据
                            try:
                                response_data = resp.json()
                                print(f"[DEBUG] RAG响应数据: {response_data}")

                                # 检查响应格式
                                if isinstance(response_data, dict) and response_data.get("code") == 0:
                                    # 获取上传的文档信息
                                    uploaded_documents = response_data.get("data", [])

                                    # 构建增强的响应数据
                                    enhanced_response = {
                                        "code": 0,
                                        "message": "文档上传成功",
                                        "data": {
                                            "uploaded_documents": uploaded_documents,
                                            "upload_summary": {
                                                "total_files": upload_file_count,
                                                "successful_uploads": len(uploaded_documents),
                                                "failed_uploads": upload_file_count - len(uploaded_documents)
                                            },
                                            "local_update_info": {
                                                "previous_document_count": current_kb.document_count,
                                                "uploaded_files": upload_file_count,
                                                "new_document_count": new_document_count,
                                                "update_time": datetime.now().isoformat()
                                            },
                                            "document_details": []
                                        }
                                    }

                                    # 解析每个文档的详细信息
                                    for doc in uploaded_documents:
                                        doc_detail = {
                                            "document_id": doc.get("id"),
                                            "name": doc.get("name"),
                                            "location": doc.get("location"),
                                            "size": doc.get("size"),
                                            "type": doc.get("type"),
                                            "chunk_method": doc.get("chunk_method"),
                                            "run_status": doc.get("run"),
                                            "parser_config": doc.get("parser_config", {}),
                                            "dataset_id": doc.get("dataset") or dataset_id,
                                            "created_by": doc.get("created_by")
                                        }
                                        enhanced_response["data"]["document_details"].append(doc_detail)

                                    return sucess_api_response(enhanced_response, resp.status_code)

                                elif isinstance(response_data, list):
                                    # 如果直接返回列表格式
                                    enhanced_response = {
                                        "code": 0,
                                        "message": "文档上传成功",
                                        "data": {
                                            "uploaded_documents": response_data,
                                            "upload_summary": {
                                                "total_files": upload_file_count,
                                                "successful_uploads": len(response_data),
                                                "failed_uploads": upload_file_count - len(response_data)
                                            },
                                            "local_update_info": {
                                                "previous_document_count": current_kb.document_count,
                                                "uploaded_files": upload_file_count,
                                                "new_document_count": new_document_count,
                                                "update_time": datetime.now().isoformat()
                                            },
                                            "document_details": []
                                        }
                                    }

                                    # 解析每个文档的详细信息
                                    for doc in response_data:
                                        doc_detail = {
                                            "document_id": doc.get("id"),
                                            "name": doc.get("name"),
                                            "location": doc.get("location"),
                                            "size": doc.get("size"),
                                            "type": doc.get("type"),
                                            "chunk_method": doc.get("chunk_method"),
                                            "run_status": doc.get("run"),
                                            "parser_config": doc.get("parser_config", {}),
                                            "dataset_id": doc.get("dataset") or dataset_id,
                                            "created_by": doc.get("created_by")
                                        }
                                        enhanced_response["data"]["document_details"].append(doc_detail)

                                    return sucess_api_response(enhanced_response, resp.status_code)
                                else:
                                    # 其他格式，使用原始响应并添加本地更新信息
                                    enhanced_response = {
                                        "code": 0,
                                        "message": "文档上传成功",
                                        "data": {
                                            "original_response": response_data,
                                            "local_update_info": {
                                                "previous_document_count": current_kb.document_count,
                                                "uploaded_files": upload_file_count,
                                                "new_document_count": new_document_count,
                                                "update_time": datetime.now().isoformat()
                                            }
                                        }
                                    }
                                    return sucess_api_response(enhanced_response, resp.status_code)

                            except json.JSONDecodeError as e:
                                print(f"[ERROR] 解析RAG响应JSON失败: {e}")
                                # 如果响应不是有效JSON，创建标准响应
                                enhanced_response = {
                                    "code": 0,
                                    "message": "文档上传成功",
                                    "data": {
                                        "original_response": resp.text,
                                        "local_update_info": {
                                            "previous_document_count": current_kb.document_count,
                                            "uploaded_files": upload_file_count,
                                            "new_document_count": new_document_count,
                                            "update_time": datetime.now().isoformat()
                                        },
                                        "upload_summary": {
                                            "total_files": upload_file_count,
                                            "successful_uploads": upload_file_count,  # 假设都成功了
                                            "failed_uploads": 0
                                        }
                                    }
                                }
                                return sucess_api_response(enhanced_response, resp.status_code)
                        else:
                            print(f"[WARN] 更新知识库(ID:{dataset_id})文档数失败")
                    else:
                        print(f"[WARN] 未找到本地知识库(ID:{dataset_id})，跳过文档数更新")

                except Exception as e:
                    print(f"[ERROR] 更新文档数时发生异常: {e}")

                    traceback.print_exc()
                    # 即使本地更新失败，也返回上传成功的结果

                # 如果本地更新失败，直接返回原始响应
                try:
                    original_response = resp.json()
                    return sucess_api_response(original_response, resp.status_code)
                except:
                    return sucess_api_response({"message": "上传成功但响应解析失败", "response_text": resp.text},
                                            resp.status_code)

            except Exception as e:
                print(f"[ERROR] 上传文档失败: {e}")

                traceback.print_exc()
                return fail_api_response(f"上传文档失败: {str(e)}", status=500)
#
        @transaction.atomic()
        def delete(self, request, dataset_id):
            """批量删除文档，删除成功后更新本地知识库的文档数"""
            try:
                # 检查要删除的文档ID列表
                document_ids = request.data.get("ids", [])
                if not document_ids:
                    return fail_api_response({"error": "No document IDs provided"}, status=400)

                delete_file_count = len(document_ids)
                print(f"[INFO] 准备删除 {delete_file_count} 个文档从知识库(ID:{dataset_id})")

                # 先获取当前知识库信息（在删除前）
                current_kb = get_knowledge_base_by_id(dataset_id)
                if not current_kb:
                    print(f"[WARN] 未找到本地知识库(ID:{dataset_id})")

                url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents"
                resp = requests.delete(url, headers={**HEADERS, "Content-Type": "application/json"}, json=request.data)

                if resp.status_code == 200:
                    # 删除成功后，更新本地知识库的文档数
                    try:
                        if current_kb:
                            # 确保文档数不为负数
                            new_document_count = max(0, current_kb.document_count - delete_file_count)

                            update_success = update_knowledge_base(
                                kb_id=dataset_id,
                                document_count=new_document_count
                            )

                            if update_success:
                                print(
                                    f"[INFO] 知识库(ID:{dataset_id})文档数已更新：{current_kb.document_count} -> {new_document_count}")

                                # 在响应中添加本地更新信息
                                response_data = resp.json()
                                if "data" not in response_data:
                                    response_data["data"] = {}
                                response_data["data"]["local_update_info"] = {
                                    "previous_document_count": current_kb.document_count,
                                    "deleted_files": delete_file_count,
                                    "new_document_count": new_document_count,
                                    "update_time": datetime.now().isoformat()
                                }
                                return sucess_api_response(response_data, resp.status_code)
                            else:
                                print(f"[WARN] 更新知识库(ID:{dataset_id})文档数失败")
                        else:
                            print(f"[WARN] 未找到本地知识库(ID:{dataset_id})，跳过文档数更新")

                    except Exception as e:
                        print(f"[ERROR] 更新文档数时发生异常: {e}")
                        # 即使本地更新失败，也返回删除成功的结果

                    return sucess_api_response(resp.json(), resp.status_code)
                else:
                    # 删除失败
                    print(f"[ERROR] RAG删除文档失败: {resp.status_code}")
                    return fail_api_response(resp.json(), resp.status_code)

            except Exception as e:
                print(f"[ERROR] 删除文档失败: {e}")
                return fail_api_response(f"删除文档失败: {str(e)}", status=500)

    @transaction.atomic()
    def delete(self, request, dataset_id):
        """批量删除文档，删除成功后更新本地知识库的文档数"""
        try:
            user = get_test_user(request)
            print(f"[INFO] 用户(ID:{user.id}, 用户名:{user.username})正在批量删除文档")

            # 从请求体获取要删除的文档ID列表
            document_ids = request.data.get("ids", [])
            if not document_ids:
                return fail_api_response({
                    "code": 400,
                    "message": "请提供要删除的文档ID列表"
                }, status=400)

            delete_file_count = len(document_ids)
            print(f"[INFO] 准备删除 {delete_file_count} 个文档从知识库(ID:{dataset_id})")

            # 先获取当前知识库信息（在删除前）
            current_kb = get_knowledge_base_by_id(dataset_id)
            if not current_kb:
                print(f"[WARN] 未找到本地知识库(ID:{dataset_id})")

            # 调用RAG API删除文档
            url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents"
            delete_data = {"ids": document_ids}

            print(f"[DEBUG] 调用RAG删除API: {url}")
            print(f"[DEBUG] 删除请求数据: {delete_data}")

            resp = requests.delete(
                url,
                headers={**HEADERS, "Content-Type": "application/json"},
                json=delete_data,
                timeout=30
            )

            print(f"[DEBUG] RAG删除响应状态码: {resp.status_code}")
            print(f"[DEBUG] RAG删除响应内容: {resp.text}")

            # 检查HTTP状态码
            if resp.status_code == 200:
                try:
                    response_data = resp.json()
                    business_code = response_data.get("code", 0)
                    business_message = response_data.get("message", "")

                    print(f"[DEBUG] RAG业务状态码: {business_code}")
                    print(f"[DEBUG] RAG业务消息: {business_message}")

                    # 检查业务状态码
                    if business_code == 0:
                        # 业务成功，更新本地知识库的文档数
                        try:
                            if current_kb:
                                # 确保文档数不为负数
                                new_document_count = max(0, current_kb.document_count - delete_file_count)

                                update_success = update_knowledge_base(
                                    kb_id=dataset_id,
                                    document_count=new_document_count
                                )

                                if update_success:
                                    print(
                                        f"[INFO] 知识库(ID:{dataset_id})文档数已更新：{current_kb.document_count} -> {new_document_count}")

                                    # 构建增强的响应数据
                                    enhanced_response = {
                                        "code": 0,
                                        "message": "文档删除成功",
                                        "data": {
                                            "deleted_document_ids": document_ids,
                                            "deleted_count": delete_file_count,
                                            "local_update_info": {
                                                "previous_document_count": current_kb.document_count,
                                                "deleted_files": delete_file_count,
                                                "new_document_count": new_document_count,
                                                "update_time": datetime.now().isoformat()
                                            }
                                        }
                                    }
                                    return sucess_api_response(enhanced_response, status=200)
                                else:
                                    print(f"[WARN] 更新知识库(ID:{dataset_id})文档数失败")
                            else:
                                print(f"[WARN] 未找到本地知识库(ID:{dataset_id})，跳过文档数更新")

                        except Exception as e:
                            print(f"[ERROR] 更新文档数时发生异常: {e}")
                            # 即使本地更新失败，也返回删除成功的结果

                        # 返回原始成功响应
                        return sucess_api_response(response_data, status=200)

                    else:
                        # 业务失败
                        print(f"[ERROR] RAG业务错误: 代码={business_code}, 消息={business_message}")

                        # 根据错误码返回相应的HTTP状态码
                        http_status = 400
                        if business_code == 102:
                            # 权限错误
                            http_status = 403
                            error_message = f"权限错误: {business_message}"
                        elif business_code in [401, 403]:
                            http_status = business_code
                            error_message = f"认证或权限错误: {business_message}"
                        elif business_code == 404:
                            http_status = 404
                            error_message = f"文档不存在: {business_message}"
                        elif business_code >= 500:
                            http_status = 500
                            error_message = f"服务器错误: {business_message}"
                        else:
                            error_message = f"删除文档失败: {business_message}"

                        return fail_api_response({
                            "code": business_code,
                            "message": error_message,
                            "details": business_message,
                            "dataset_id": dataset_id,
                            "document_ids": document_ids
                        }, status=http_status)

                except json.JSONDecodeError as e:
                    print(f"[ERROR] 解析RAG响应JSON失败: {e}")
                    return fail_api_response({
                        "code": 500,
                        "message": "服务器响应格式错误",
                        "response_text": resp.text
                    }, status=500)
            else:
                # HTTP层面的错误
                print(f"[ERROR] RAG HTTP请求失败: {resp.status_code}")

                try:
                    error_data = resp.json()
                    return fail_api_response(error_data, status=resp.status_code)
                except json.JSONDecodeError:
                    return fail_api_response({
                        "code": resp.status_code,
                        "message": f"HTTP请求失败，状态码: {resp.status_code}",
                        "response_text": resp.text
                    }, status=resp.status_code)

        except requests.exceptions.Timeout:
            print(f"[ERROR] 删除文档请求超时")
            return fail_api_response({
                "code": 504,
                "message": "删除文档请求超时"
            }, status=504)
        except requests.exceptions.ConnectionError:
            print(f"[ERROR] 无法连接到RAG服务")
            return fail_api_response({
                "code": 503,
                "message": "无法连接到RAG服务"
            }, status=503)
        except requests.exceptions.RequestException as e:
            print(f"[ERROR] 删除文档网络请求失败: {e}")
            return fail_api_response({
                "code": 500,
                "message": f"删除文档网络请求失败: {str(e)}"
            }, status=500)
        except Exception as e:
            print(f"[ERROR] 删除文档失败: {e}")
            traceback.print_exc()
            return fail_api_response({
                "code": 500,
                "message": f"删除文档失败: {str(e)}",
                "dataset_id": dataset_id
            }, status=500)

class DocumentDetailView(APIView):
    permission_map = {
        'put': ['knowledge_base.update_knowledge_bases_documents'],
        'get': ['knowledge_base.download_knowledge_bases_documents'],
        'delete': ['knowledge_base.delete_knowledge_bases_documents']
    }

    def put(self, request, dataset_id, document_id):
        """更新文档配置"""
        url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{document_id}"
        resp = requests.put(url, headers={**HEADERS, "Content-Type": "application/json"}, json=request.data)
        return sucess_api_response(resp.json(), status=resp.status_code)
    def get(self, request, dataset_id, document_id):
        """下载文档"""
        try:
            print(f"[INFO] 开始下载文档: dataset_id={dataset_id}, document_id={document_id}")
            
            url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{document_id}"
            
            # 发送请求获取文档
            resp = requests.get(url, headers=HEADERS, stream=True, timeout=30)
            
            print(f"[DEBUG] RAG响应状态码: {resp.status_code}")
            print(f"[DEBUG] RAG响应头: {dict(resp.headers)}")
            
            if resp.status_code != 200:
                print(f"[ERROR] RAG下载失败: {resp.status_code}")
                return fail_api_response({
                    "code": resp.status_code,
                    "message": f"文档下载失败，状态码: {resp.status_code}",
                    "document_id": document_id
                }, status=resp.status_code)
            
            # 提取文件名
            filename = self._extract_filename(resp.headers, document_id)
            print(f"[DEBUG] 提取的文件名: {filename}")
            
            # 获取内容类型
            content_type = resp.headers.get('Content-Type', 'application/octet-stream')
            
            # 创建流式响应
            def file_iterator():
                try:
                    for chunk in resp.iter_content(chunk_size=8192):
                        if chunk:
                            yield chunk
                except Exception as e:
                    print(f"[ERROR] 流式传输异常: {e}")
                    raise
            
            # 创建响应
            response = StreamingHttpResponse(
                file_iterator(),
                content_type=content_type
            )
            
            # 设置安全的文件名
            safe_filename = self._make_filename_safe(filename)
            
            # 设置响应头
            response['Content-Disposition'] = self._build_content_disposition(safe_filename)
            
            # 设置其他有用的响应头
            if 'Content-Length' in resp.headers:
                response['Content-Length'] = resp.headers['Content-Length']
            
            response['X-Document-ID'] = document_id
            response['X-Dataset-ID'] = dataset_id
            
            print(f"[INFO] 文档下载成功: {safe_filename}")
            return response
            
        except requests.exceptions.Timeout:
            print(f"[ERROR] 下载文档超时")
            return fail_api_response({
                "code": 504,
                "message": "文档下载超时",
                "document_id": document_id
            }, status=504)
            
        except requests.exceptions.ConnectionError:
            print(f"[ERROR] 无法连接到RAG服务")
            return fail_api_response({
                "code": 503,
                "message": "无法连接到RAG服务",
                "document_id": document_id
            }, status=503)
            
        except requests.exceptions.RequestException as e:
            print(f"[ERROR] 下载请求失败: {e}")
            return fail_api_response({
                "code": 500,
                "message": f"下载请求失败: {str(e)}",
                "document_id": document_id
            }, status=500)
            
        except Exception as e:
            print(f"[ERROR] 下载文档异常: {e}")
            traceback.print_exc()
            return fail_api_response({
                "code": 500,
                "message": f"下载文档失败: {str(e)}",
                "document_id": document_id
            }, status=500)
    
    def _extract_filename(self, headers, document_id):
        """从响应头中提取文件名"""
        try:
            content_disposition = headers.get('Content-Disposition', '')
            print(f"[DEBUG] Content-Disposition: {content_disposition}")
            
            if content_disposition:
                # 尝试多种方式提取文件名
                patterns = [
                    r'filename\*=UTF-8\'\'([^;]+)',  # RFC 5987格式
                    r'filename="([^"]+)"',           # 标准格式
                    r'filename=([^;]+)',             # 无引号格式
                ]
                
                for pattern in patterns:
                    match = re.search(pattern, content_disposition, re.IGNORECASE)
                    if match:
                        filename = match.group(1).strip()
                        
                        # 处理URL编码
                        try:
                            filename = urllib.parse.unquote(filename, encoding='utf-8')
                        except:
                            pass
                        
                        if filename:
                            print(f"[DEBUG] 从Content-Disposition提取文件名: {filename}")
                            return filename
            
            # 如果无法提取，使用document_id生成默认文件名
            default_filename = f"document_{document_id}.bin"
            print(f"[DEBUG] 使用默认文件名: {default_filename}")
            return default_filename
            
        except Exception as e:
            print(f"[WARN] 提取文件名失败: {e}")
            return f"document_{document_id}.bin"
    
    def _make_filename_safe(self, filename):
        """确保文件名安全，处理特殊字符"""
        try:
            # 移除或替换危险字符
            safe_chars = re.sub(r'[<>:"/\\|?*]', '_', filename)
            
            # 限制长度
            if len(safe_chars) > 200:
                name, ext = os.path.splitext(safe_chars)
                safe_chars = name[:200-len(ext)] + ext
            
            # 确保不为空
            if not safe_chars or safe_chars.isspace():
                safe_chars = "download.bin"
            
            print(f"[DEBUG] 安全文件名: {safe_chars}")
            return safe_chars
            
        except Exception as e:
            print(f"[WARN] 文件名安全处理失败: {e}")
            return "download.bin"
    
    def _build_content_disposition(self, filename):
        """构建Content-Disposition头，支持中文文件名"""
        try:
            # 检查是否包含非ASCII字符
            try:
                filename.encode('ascii')
                # 如果是纯ASCII，使用简单格式
                return f'attachment; filename="{filename}"'
            except UnicodeEncodeError:
                # 包含非ASCII字符，使用RFC 5987格式
                encoded_filename = urllib.parse.quote(filename, encoding='utf-8')
                return f'attachment; filename*=UTF-8\'\'{encoded_filename}'
                
        except Exception as e:
            print(f"[WARN] 构建Content-Disposition失败: {e}")
            return 'attachment; filename="download.bin"'

    @transaction.atomic()
    def delete(self, request, dataset_id, document_id=None):
        """删除文档 - 支持单个删除和批量删除"""
        try:
            user = get_test_user(request)
            print(f"[INFO] 用户(ID:{user.id}, 用户名:{user.username})正在删除文档")

            # 构建删除请求数据
            if document_id:
                # 单个文档删除：通过URL参数传递document_id
                document_ids = [document_id]
                print(f"[INFO] 删除单个文档: {document_id}")
            else:
                # 批量删除：从请求体获取ids
                document_ids = request.data.get("ids", [])
                if not document_ids:
                    return fail_api_response({
                        "code": 400,
                        "message": "请提供要删除的文档ID列表"
                    }, status=400)
                print(f"[INFO] 批量删除文档: {document_ids}")

            delete_file_count = len(document_ids)
            print(f"[INFO] 准备删除 {delete_file_count} 个文档从知识库(ID:{dataset_id})")

            # 先获取当前知识库信息（在删除前）
            current_kb = get_knowledge_base_by_id(dataset_id)
            if not current_kb:
                print(f"[WARN] 未找到本地知识库(ID:{dataset_id})")

            # 调用RAG API删除文档
            url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents"
            delete_data = {"ids": document_ids}

            print(f"[DEBUG] 调用RAG删除API: {url}")
            print(f"[DEBUG] 删除请求数据: {delete_data}")

            resp = requests.delete(
                url,
                headers={**HEADERS, "Content-Type": "application/json"},
                json=delete_data,
                timeout=30
            )

            print(f"[DEBUG] RAG删除响应状态码: {resp.status_code}")
            print(f"[DEBUG] RAG删除响应内容: {resp.text}")

            # 检查HTTP状态码
            if resp.status_code == 200:
                try:
                    response_data = resp.json()
                    business_code = response_data.get("code", 0)
                    business_message = response_data.get("message", "")

                    print(f"[DEBUG] RAG业务状态码: {business_code}")
                    print(f"[DEBUG] RAG业务消息: {business_message}")

                    # 检查业务状态码
                    if business_code == 0:
                        # 业务成功，更新本地知识库的文档数
                        try:
                            if current_kb:
                                # 确保文档数不为负数
                                new_document_count = max(0, current_kb.document_count - delete_file_count)

                                update_success = update_knowledge_base(
                                    kb_id=dataset_id,
                                    document_count=new_document_count
                                )

                                if update_success:
                                    print(
                                        f"[INFO] 知识库(ID:{dataset_id})文档数已更新：{current_kb.document_count} -> {new_document_count}")

                                    # 构建增强的响应数据
                                    enhanced_response = {
                                        "code": 0,
                                        "message": "文档删除成功",
                                        "data": {
                                            "deleted_document_ids": document_ids,
                                            "deleted_count": delete_file_count,
                                            "local_update_info": {
                                                "previous_document_count": current_kb.document_count,
                                                "deleted_files": delete_file_count,
                                                "new_document_count": new_document_count,
                                                "update_time": datetime.now().isoformat()
                                            }
                                        }
                                    }
                                    return sucess_api_response(enhanced_response, status=200)
                                else:
                                    print(f"[WARN] 更新知识库(ID:{dataset_id})文档数失败")
                            else:
                                print(f"[WARN] 未找到本地知识库(ID:{dataset_id})，跳过文档数更新")

                        except Exception as e:
                            print(f"[ERROR] 更新文档数时发生异常: {e}")
                            # 即使本地更新失败，也返回删除成功的结果

                        # 返回原始成功响应
                        return sucess_api_response(response_data, status=200)

                    else:
                        # 业务失败
                        print(f"[ERROR] RAG业务错误: 代码={business_code}, 消息={business_message}")

                        # 根据错误码返回相应的HTTP状态码
                        http_status = 400
                        if business_code == 102:
                            # 权限错误
                            http_status = 403
                            error_message = f"权限错误: {business_message}"
                        elif business_code in [401, 403]:
                            http_status = business_code
                            error_message = f"认证或权限错误: {business_message}"
                        elif business_code == 404:
                            http_status = 404
                            error_message = f"文档不存在: {business_message}"
                        elif business_code >= 500:
                            http_status = 500
                            error_message = f"服务器错误: {business_message}"
                        else:
                            error_message = f"删除文档失败: {business_message}"

                        return fail_api_response({
                            "code": business_code,
                            "message": error_message,
                            "details": business_message,
                            "dataset_id": dataset_id,
                            "document_ids": document_ids
                        }, status=http_status)

                except json.JSONDecodeError as e:
                    print(f"[ERROR] 解析RAG响应JSON失败: {e}")
                    return fail_api_response({
                        "code": 500,
                        "message": "服务器响应格式错误",
                        "response_text": resp.text
                    }, status=500)
            else:
                # HTTP层面的错误
                print(f"[ERROR] RAG HTTP请求失败: {resp.status_code}")

                try:
                    error_data = resp.json()
                    return fail_api_response(error_data, status=resp.status_code)
                except json.JSONDecodeError:
                    return fail_api_response({
                        "code": resp.status_code,
                        "message": f"HTTP请求失败，状态码: {resp.status_code}",
                        "response_text": resp.text
                    }, status=resp.status_code)

        except requests.exceptions.Timeout:
            print(f"[ERROR] 删除文档请求超时")
            return fail_api_response({
                "code": 504,
                "message": "删除文档请求超时"
            }, status=504)
        except requests.exceptions.ConnectionError:
            print(f"[ERROR] 无法连接到RAG服务")
            return fail_api_response({
                "code": 503,
                "message": "无法连接到RAG服务"
            }, status=503)
        except requests.exceptions.RequestException as e:
            print(f"[ERROR] 删除文档网络请求失败: {e}")
            return fail_api_response({
                "code": 500,
                "message": f"删除文档网络请求失败: {str(e)}"
            }, status=500)
        except Exception as e:
            print(f"[ERROR] 删除文档失败: {e}")
            traceback.print_exc()
            return fail_api_response({
                "code": 500,
                "message": f"删除文档失败: {str(e)}",
                "dataset_id": dataset_id
            }, status=500)


# 判断PG表中是否已存在相同的chunk（根据document_id、question、content/answer）
def chunk_exists_in_pg(document_id, question, content):
    return ChunkQA.objects.filter(
        document_id=document_id,
        question=question,
        content=content
    ).exists()
@custom_api_view(['POST', 'DELETE'])
def parse_or_stop_documents(request, dataset_id):
    permission_map = {
        'post': ['knowledge_base.parse_operate_knowledge_bases_documents'],
        'delete': ['knowledge_base.parse_operate_knowledge_bases_documents']
    }
    '''
    解析文档，将PG表中的问答对添加到知识库文档（避免重复添加，按问题+答案+文档id去重）
    '''
    chunk_method = 'naive'
    if request.method == 'POST':
        document_ids = request.data.get("document_ids")
        if not document_ids or not isinstance(document_ids, list):
            return Response({"code": 400, "message": "缺少或格式错误的document_ids"}, status=400)

        # 1. 调用RAG解析接口
        url_parse = f"{BASE_URL}/api/v1/datasets/{dataset_id}/chunks"
        resp_parse = requests.post(url_parse, headers=HEADERS, json={"document_ids": document_ids})
        try:
            resp_json = resp_parse.json()
        except Exception as e:
            return Response({"code": resp_parse.status_code, "message": "RAG解析响应不是JSON", "details": resp_parse.text}, status=resp_parse.status_code)
        print('[DEBUG] RAG解析响应:', resp_json)
        # 判断是否需要先删除chunk再解析
        if resp_json.get("code") != 0 and "Can't stop parsing document with progress at 0 or 100" in resp_json.get("message", ""):
            # 2.先删除所有chunk
            print('需要删除所有chunk')
            for document_id in document_ids:
                url_del = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks"
                # 不传chunk_ids，删除全部
                resp_del = requests.delete(url_del, headers=HEADERS, json={})
                del_json = resp_del.json()
                if del_json.get("code") != 0:
                    print('删除chunk失败:', del_json)
                    return Response({"code": del_json.get("code"), "message": "删除chunk失败", "details": del_json.get("message", resp_del.text)}, status=400)
                else:
                    print('删除chunk成功:', del_json)

            # 2. 重置文档状态

            # 3.再次调用解析接口
            resp_parse = requests.post(url_parse, headers=HEADERS, json={"document_ids": document_ids})
            try:
                resp_json = resp_parse.json()
            except Exception as e:
                return Response({"code": resp_parse.status_code, "message": "RAG解析响应不是JSON", "details": resp_parse.text}, status=resp_parse.status_code)
            if resp_json.get("code") != 0:
                return Response({"code": resp_json.get("code"), "message": "RAG解析失败", "details": resp_json.get("message", resp_parse.text)}, status=400)

        elif resp_json.get("code") != 0:
            return Response({"code": resp_json.get("code"), "message": "RAG解析失败", "details": resp_json.get("message", resp_parse.text)}, status=400)

        # 2. 遍历每个文档，查PG问答对并同步到RAG
        added_chunks = []
        skipped_chunks = []
        for document_id in document_ids:
            chunk_qas = ChunkQA.objects.filter(document_id=document_id)
            for chunk in chunk_qas:
                if chunk_exists_in_pg(document_id, chunk.question, chunk.content):
                    skipped_chunks.append(chunk.chunk_id)
                    continue
                chunk_data = {
                    "content": chunk.content,
                    "important_keywords": [],
                    "questions": [chunk.question] if chunk.question else [],
                }
                url_add = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks"
                resp_add = requests.post(url_add, headers=HEADERS, json=chunk_data)
                try:
                    resp_add_json = resp_add.json()
                except Exception as e:
                    skipped_chunks.append(chunk.chunk_id)
                    continue
                if resp_add.status_code == 200 and resp_add_json.get("code") == 0:
                    rag_chunk = resp_add_json["data"]["chunk"]
                    chunk.chunk_id = rag_chunk["id"]
                    chunk.save(update_fields=["chunk_id"])
                    added_chunks.append(rag_chunk["id"])
                else:
                    skipped_chunks.append(chunk.chunk_id)

        return Response({
            "code": 0,
            "message": "解析并同步chunk完成",
            "added_chunks": added_chunks,
            "skipped_chunks": skipped_chunks,
            "total_chunks": len(added_chunks) + len(skipped_chunks)
        }, status=200)

    elif request.method == 'DELETE':
        document_id = request.data.get("document_id")
        url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks"
        resp = requests.delete(url, headers=HEADERS, json={})
        try:
            resp_json = resp.json()
        except Exception as e:
            return Response({"code": resp.status_code, "message": "RAG删除响应不是JSON", "details": resp.text}, status=resp.status_code)
        if resp_json.get("code") == 0:
            return Response(resp_json, status=200)
        else:
            return Response({"code": resp_json.get("code"), "message": "停止解析失败", "details": resp_json.get("message", resp.text)}, status=500)

'''----------知识库构建接口结束--------'''

'''----------chunk管理接口---------'''
# class ChunkListView(APIView):
#     parser_classes = [JSONParser]
#
#     def post(self, request, dataset_id, document_id):
#         """添加chunk"""
#         url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks"
#         resp = requests.post(url, headers=HEADERS, json=request.data)
#         return Response(resp.json(), status=resp.status_code)
#
#     def get(self, request, dataset_id, document_id):
#         """列出chunk"""
#         url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks"
#         resp = requests.get(url, headers={"Authorization": HEADERS["Authorization"]}, params=request.query_params)
#         return Response(resp.json(), status=resp.status_code)
#
#     def delete(self, request, dataset_id, document_id):
#         """删除chunk"""
#         url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks"
#         resp = requests.delete(url, headers=HEADERS, json=request.data)
#         return Response(resp.json(), status=resp.status_code)
class ChunkListView(APIView):
    parser_classes = [JSONParser]
    permission_map={
        'get':['knowledge_base.view_knowledge_bases_chunks'],
        'post': ['knowledge_base.add_knowledge_bases_chunks'],
        'delete': ['knowledge_base.delete_knowledge_bases_chunks']
    }
    def get(self, request, dataset_id, document_id):
        """列出chunk，支持通过source参数选择数据源"""
        # 获取数据源参数，默认为rag
        data_source = request.GET.get('source', 'rag').lower()

        if data_source == 'pg':
            return self._get_chunks_from_pg(request, dataset_id, document_id)
        else:
            return self._get_chunks_from_rag(request, dataset_id, document_id)

    def _get_chunks_from_rag(self, request, dataset_id, document_id):
        url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks"
        resp = requests.get(url, headers={"Authorization": HEADERS["Authorization"]}, params=request.query_params)

        return Response(resp.json(), status=resp.status_code)

    def _get_chunks_from_pg(self, request, dataset_id, document_id):
        """从PG数据库获取chunk列表"""
        try:
            print(f"[INFO] 从PG数据库获取chunk列表: dataset_id={dataset_id}, document_id={document_id}")

            # 获取查询参数
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 20))

            # 从PG获取chunk数据
            if document_id == 'all':
                # 获取整个知识库的所有chunk
                pg_chunks = get_chunk_qas_by_dataset(dataset_id)
            else:
                # 获取指定文档的chunk
                pg_chunks = get_chunk_qas_by_document(document_id)

            if not pg_chunks:
                return Response({
                    "code": 404,
                    "data": [],
                    "data_source": "pg_only",
                    "message": "PG数据库中未找到chunk数据",
                    "total_count": 0,
                    "page": page,
                    "page_size": page_size,
                    "query_time": datetime.now().isoformat()
                }, status=200)

            # 转换为列表（如果是QuerySet）
            if hasattr(pg_chunks, 'all'):
                pg_chunks = list(pg_chunks.all())
            elif not isinstance(pg_chunks, list):
                pg_chunks = list(pg_chunks)

            # 分页处理
            total_count = len(pg_chunks)
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            paginated_chunks = pg_chunks[start_idx:end_idx]

            # 构建响应数据
            chunks_data = []
            for pg_chunk in paginated_chunks:
                chunk_item = {
                    "id": pg_chunk.chunk_id,
                    "content": pg_chunk.content,
                    "question": pg_chunk.question,
                    "document_id": pg_chunk.document_id,
                    "knowledge_base_id": pg_chunk.knowledge_base_id,
                    "pg_info": {
                        "pg_id": pg_chunk.id,
                        "created_at": pg_chunk.created_at.isoformat(),
                        "updated_at": pg_chunk.updated_at.isoformat(),
                        "is_active": pg_chunk.is_active,
                        "synced": True
                    },
                    # 模拟RAG格式的其他字段
                    "important_keywords": [],
                    "questions": [pg_chunk.question] if pg_chunk.question else [],
                    "create_time": pg_chunk.created_at.strftime("%Y-%m-%d %H:%M:%S") if pg_chunk.created_at else "",
                    "dataset_id": dataset_id,
                    "document_id": document_id if document_id != 'all' else pg_chunk.document_id
                }
                chunks_data.append(chunk_item)

            # 构建完整响应
            response_data = {
                "code": 0,
                "data": chunks_data,
                "data_source": "pg_only",
                "total_count": total_count,
                "page": page,
                "page_size": page_size,
                "total_pages": (total_count + page_size - 1) // page_size,
                "statistics": {
                    "total_chunks": total_count,
                    "active_chunks": sum(1 for chunk in pg_chunks if chunk.is_active),
                    "inactive_chunks": sum(1 for chunk in pg_chunks if not chunk.is_active)
                },
                "query_time": datetime.now().isoformat(),
                "message": f"从PG数据库获取到 {total_count} 个chunk"
            }

            print(f"[INFO] 从PG数据库成功获取 {total_count} 个chunk，当前页 {len(chunks_data)} 个")
            return Response(response_data, status=200)

        except Exception as e:
            print(f"[ERROR] 从PG数据库获取chunk失败: {e}")
            traceback.print_exc()
            return Response({
                "error": f"从PG数据库获取chunk失败: {str(e)}",
                "data_source": "pg_only"
            }, status=500)
    def post(self, request, dataset_id, document_id):
        """添加chunk，同时同步到PG数据库，chunk_id用RAG返回的id"""
        content = request.data.get("content")
        questions = request.data.get("questions", [])
        question = questions[0] if questions else ""
        important_keywords = request.data.get("important_keywords", [])

        # 检查PG是否有重复
        if chunk_exists_in_pg(document_id, question, content):
            return Response({
                "code": 409,
                "message": "chunk已存在，跳过添加"
            }, status=409)

        chunk_data = {
            "content": content,
            "important_keywords": important_keywords,
            "questions": questions
        }
        url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks"
        resp = requests.post(url, headers=HEADERS, json=chunk_data)

        if resp.status_code == 200 and resp.json().get("code") == 0:
            rag_chunk = resp.json()["data"]["chunk"]
            # 用RAG返回的chunk_id同步PG
            ChunkQA.objects.create(
                chunk_id=rag_chunk["id"],
                content=content,
                question=question,
                document_id=document_id,
                knowledge_base_id=dataset_id
            )
            return Response(resp.json(), status=200)
        else:
            return Response({
                "code": resp.status_code,
                "message": "RAG添加chunk失败",
                "details": resp.text
            }, status=resp.status_code)

    def delete(self, request, dataset_id, document_id):
        """删除chunk，同时从PG数据库删除"""
        url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks"

        try:
            # 从请求中获取要删除的chunk_ids
            chunk_ids = request.data.get('chunk_ids', [])

            if not chunk_ids:
                return Response({"error": "chunk_ids不能为空"}, status=400)

            print(f"[INFO] 删除chunks: {chunk_ids}")

            # 先尝试从PG中删除记录
            deleted_count = 0
            failed_chunks = []

            if chunk_ids:
                for chunk_id in chunk_ids:
                    if delete_chunk_qa(chunk_id, soft_delete=True):
                        deleted_count += 1
                        print(f"[INFO] 成功从PG中删除ChunkQA记录: {chunk_id}")
                    else:
                        failed_chunks.append(chunk_id)
                        print(f"[ERROR] 从PG中删除ChunkQA记录失败: {chunk_id}")

            print(f"[INFO] 已从PG中删除{deleted_count}条ChunkQA记录，失败{len(failed_chunks)}条")

            # 检查是否有任何chunk从PG删除失败
            if failed_chunks:
                print(f"[ERROR] 以下chunk从PG删除失败，不执行RAG删除: {failed_chunks}")
                return Response({
                    "error": "部分chunk从PG数据库删除失败，操作已取消",
                    "failed_chunks": failed_chunks,
                    "deleted_from_pg": deleted_count,
                    "total_requested": len(chunk_ids)
                }, status=500)

            # 如果没有chunk从PG成功删除，则不调用RAG API
            if deleted_count == 0:
                print(f"[ERROR] 没有chunk从PG中成功删除，不调用RAG API删除")
                return Response({
                    "error": "没有chunk从PG数据库删除成功，操作已取消",
                    "deleted_from_pg": 0,
                    "total_requested": len(chunk_ids)
                }, status=500)

            # 只有当所有chunk都从PG成功删除后，才调用RAG API删除
            print(f"[INFO] PG删除成功，开始调用RAG API删除")
            resp = requests.delete(url, headers=HEADERS, json=request.data)

            print(f"[DEBUG] RAG API删除响应状态码: {resp.status_code}")
            print(f"[DEBUG] RAG API删除响应内容: {resp.text}")

            # 在响应中添加PG删除信息
            #if resp.status_code == 200:
            if resp.json().get("code", 0) == 0:
                try:
                    response_data = resp.json()
                    if 'data' not in response_data:
                        response_data['data'] = {}

                    response_data['data']['pg_info'] = {
                        'deleted_count': deleted_count,
                        'failed_count': len(failed_chunks),
                        'pg_synced': True,
                        'rag_deleted': True
                    }

                    print(f"[INFO] 成功删除chunk - PG: {deleted_count}条, RAG: 成功")
                    return Response(response_data, status=resp.status_code)
                except Exception as e:
                    print(f"[WARN] 解析RAG删除响应异常: {e}")
                    pass
            else:
                # RAG删除失败，记录错误但不回滚PG操作（因为PG已经删除成功）
                print(f"[ERROR] RAG API删除失败，但PG已删除成功: 状态码{resp.status_code}")
                return Response({
                    "error": "RAG删除失败，但PG删除已成功",
                    "rag_status": resp.status_code,
                    "rag_response": resp.text,
                    "pg_info": {
                        'deleted_count': deleted_count,
                        'pg_synced': True,
                        'rag_deleted': False
                    }
                }, status=207)  # 207 Multi-Status表示部分成功

            return Response(resp.json(), status=resp.status_code)

        except Exception as e:
            print(f"[ERROR] 删除chunk失败: {e}")
            traceback.print_exc()
            return Response({"error": str(e)}, status=500)

# class ChunkDetailView(APIView):
#     parser_classes = [JSONParser]
#
#     def put(self, request, chunk_id, dataset_id, document_id):
#         """更新chunk"""
#         url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks/{chunk_id}"
#         resp = requests.put(url, headers=HEADERS, json=request.data)
#         return Response(resp.json(), status=resp.status_code)
class ChunkDetailView(APIView):
    parser_classes = [JSONParser]
    permission_map = {
        'put': ['knowledge_base.update_knowledge_bases_chunks'],
    }

    '''更新chunk，同时更新PG数据库'''
    def put(self, request, chunk_id, dataset_id, document_id):
        """更新chunk，同时更新PG数据库"""
        url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks/{chunk_id}"

        try:
            print(f"[INFO] 更新chunk: {chunk_id}")

            # 调用RAG API更新chunk
            resp = requests.put(url, headers=HEADERS, json=request.data)

            print(f"[DEBUG] RAG API更新响应状态码: {resp.status_code}")
            print(f"[DEBUG] RAG API更新响应内容: {resp.text}")

            chunk_data = resp.json()

            # 如果RAG API成功，同步到PG数据库
            if chunk_data["code"] == 0:
                try:
                    # 从请求数据中提取更新信息
                    question = request.data.get('question')
                    content = request.data.get('content')

                    print(f"[DEBUG] 更新问题: {question}")
                    print(f"[DEBUG] 更新内容: {content}")

                    # 更新ChunkQA记录
                    success = update_chunk_qa(
                        chunk_id=chunk_id,
                        question=question,
                        content=content
                    )

                    if success:
                        print(f"[INFO] 成功同步更新chunk到PG数据库: {chunk_id}")

                        # 在响应中添加PG更新信息
                        try:
                            response_data = resp.json()
                            if 'data' not in response_data:
                                response_data['data'] = {}

                            response_data['data']['pg_info'] = {
                                'updated': True,
                                'updated_at': datetime.now().isoformat()
                            }

                            return Response(response_data, status=resp.status_code)
                        except:
                            pass
                    else:
                        print(f"[WARN] chunk更新成功但PG同步失败: {chunk_id}")

                except Exception as e:
                    print(f"[ERROR] chunk更新成功但PG同步异常: {e}")
                    traceback.print_exc()

            return Response(resp.json(), status=resp.status_code)

        except Exception as e:
            print(f"[ERROR] 更新chunk失败: {e}")
            traceback.print_exc()
            return Response({"error": str(e)}, status=500)


@api_view(['POST'])
def retrieval_chunks(request):
    permission_classes = [AllowAny]
    """检索chunk"""
    url = f"{BASE_URL}/api/v1/retrieval"
    resp = requests.post(url, headers=HEADERS, json=request.data)
    return Response(resp.json(), status=resp.status_code)


'''----------chunk管理接口结束---------'''

'''----------知识库数据同步函数---------'''
# 在文件末尾添加以下代码
'''
功能：同步RAG和PG知识库表中的数据
1. 比较RAG和PG知识库表中的数据，取交集，其余的删掉
'''
# def sync_knowledge_bases_intersection():
#     """
#     功能1：比较RAG和PG知识库表中的数据，取交集，其余的删掉
#     保留两边都存在的知识库，删除只在一边存在的知识库
#     """
#     print(f"[INFO] 开始执行知识库交集同步")
#
#     sync_result = {
#         "operation": "intersection_sync",
#         "rag_total": 0,
#         "pg_total": 0,
#         "intersection_count": 0,
#         "deleted_from_rag": 0,
#         "deleted_from_pg": 0,
#         "errors": [],
#         "sync_time": datetime.now().isoformat()
#     }
#
#     try:
#         # 1. 获取RAG中的所有知识库
#         rag_datasets = _fetch_all_rag_datasets()
#         rag_ids = {dataset["id"] for dataset in rag_datasets}
#         sync_result["rag_total"] = len(rag_ids)
#
#         print(f"[DEBUG] RAG知识库总数: {len(rag_ids)}")
#
#         # 2. 获取PG中的所有知识库
#         pg_datasets = _fetch_all_pg_datasets()
#         pg_ids = {dataset.id for dataset in pg_datasets}
#         sync_result["pg_total"] = len(pg_ids)
#
#         print(f"[DEBUG] PG知识库总数: {len(pg_ids)}")
#
#         # 3. 计算交集和差集
#         intersection_ids = rag_ids & pg_ids  # 交集：两边都有的
#         rag_only_ids = rag_ids - pg_ids  # RAG独有的
#         pg_only_ids = pg_ids - rag_ids  # PG独有的
#
#         sync_result["intersection_count"] = len(intersection_ids)
#
#         print(f"[DEBUG] 交集数量: {len(intersection_ids)}")
#         print(f"[DEBUG] RAG独有: {len(rag_only_ids)}")
#         print(f"[DEBUG] PG独有: {len(pg_only_ids)}")
#
#         # 4. 删除RAG中独有的知识库
#         for kb_id in rag_only_ids:
#             try:
#                 success = _delete_rag_dataset(kb_id)
#                 if success:
#                     sync_result["deleted_from_rag"] += 1
#                     print(f"[INFO] 从RAG删除知识库: {kb_id}")
#                 else:
#                     sync_result["errors"].append(f"删除RAG知识库失败: {kb_id}")
#             except Exception as e:
#                 error_msg = f"删除RAG知识库异常 {kb_id}: {str(e)}"
#                 print(f"[ERROR] {error_msg}")
#                 sync_result["errors"].append(error_msg)
#
#         # 5. 删除PG中独有的知识库
#         for kb_id in pg_only_ids:
#             try:
#                 success = _delete_pg_dataset_cascade(kb_id)
#                 if success:
#                     sync_result["deleted_from_pg"] += 1
#                     print(f"[INFO] 从PG删除知识库: {kb_id}")
#                 else:
#                     sync_result["errors"].append(f"删除PG知识库失败: {kb_id}")
#             except Exception as e:
#                 error_msg = f"删除PG知识库异常 {kb_id}: {str(e)}"
#                 print(f"[ERROR] {error_msg}")
#                 sync_result["errors"].append(error_msg)
#
#         print(f"[INFO] 交集同步完成 - 保留: {len(intersection_ids)}, "
#               f"从RAG删除: {sync_result['deleted_from_rag']}, "
#               f"从PG删除: {sync_result['deleted_from_pg']}")
#
#         return sync_result
#
#     except Exception as e:
#         error_msg = f"交集同步过程异常: {str(e)}"
#         print(f"[ERROR] {error_msg}")
#         traceback.print_exc()
#         sync_result["errors"].append(error_msg)
#         return sync_result
#
#
#
# def _fetch_all_rag_datasets():
#     """获取RAG中的所有知识库"""
#     try:
#         all_datasets = []
#         page = 1
#         page_size = 100
#
#         while True:
#             resp = requests.get(
#                 f"{BASE_URL}/api/v1/datasets",
#                 headers=HEADERS,
#                 params={
#                     "page": page,
#                     "page_size": page_size
#                 },
#                 timeout=30
#             )
#
#             if resp.status_code == 200:
#                 data = resp.json()
#                 datasets = data.get("data", [])
#
#                 if not datasets:
#                     break
#
#                 all_datasets.extend(datasets)
#                 print(f"[DEBUG] 获取第{page}页RAG数据，本页{len(datasets)}条")
#
#                 # 检查是否还有更多数据
#                 if len(datasets) < page_size:
#                     break
#
#                 page += 1
#             else:
#                 print(f"[ERROR] 获取RAG数据失败: {resp.status_code}")
#                 break
#
#         print(f"[INFO] 共获取RAG知识库 {len(all_datasets)} 个")
#         return all_datasets
#
#     except Exception as e:
#         print(f"[ERROR] 获取RAG数据异常: {e}")
#         return []
#
#
# def _fetch_all_pg_datasets():
#     """获取PG中的所有活跃知识库"""
#     try:
#         # 假设有这个函数获取所有活跃的知识库
#         datasets = get_all_active_knowledge_bases()
#         print(f"[INFO] 共获取PG知识库 {len(datasets)} 个")
#         return datasets
#     except Exception as e:
#         print(f"[ERROR] 获取PG数据异常: {e}")
#         return []
#
#
#
# def _delete_rag_dataset(kb_id):
#     """删除RAG中的知识库 - 参考DatasetListCreateView的delete方法"""
#     try:
#         # 参考DatasetListCreateView中的delete方法，使用批量删除接口
#         # RAG的删除接口需要传递ids数组格式
#         delete_data = {"ids": [kb_id]}
#
#         print(f"[DEBUG] 准备删除RAG知识库: {kb_id}")
#         print(f"[DEBUG] 删除数据: {delete_data}")
#
#         resp = requests.delete(
#             f"{BASE_URL}/api/v1/datasets",  # 注意：这里是批量删除的端点，不是单个删除
#             headers={**HEADERS, "Content-Type": "application/json"},
#             json=delete_data,
#             timeout=30
#         )
#
#         print(f"[DEBUG] RAG删除响应状态码: {resp.status_code}")
#         print(f"[DEBUG] RAG删除响应内容: {resp.text}")
#
#         if resp.status_code == 200:
#             print(f"[INFO] 成功从RAG删除知识库: {kb_id}")
#
#             # 可选：解析响应内容获取详细信息
#             try:
#                 response_data = resp.json()
#                 print(f"[DEBUG] 删除响应详情: {response_data}")
#             except json.JSONDecodeError:
#                 print(f"[WARN] 无法解析RAG删除响应JSON")
#
#             return True
#         else:
#             print(f"[ERROR] 从RAG删除知识库失败: {kb_id}, 状态码: {resp.status_code}")
#             print(f"[ERROR] 错误响应: {resp.text}")
#
#             # 尝试解析错误信息
#             try:
#                 error_data = resp.json()
#                 print(f"[ERROR] 错误详情: {error_data}")
#             except json.JSONDecodeError:
#                 print(f"[ERROR] 无法解析错误响应JSON")
#
#             return False
#
#     except requests.exceptions.Timeout:
#         print(f"[ERROR] 删除RAG知识库请求超时: {kb_id}")
#         return False
#     except requests.exceptions.ConnectionError:
#         print(f"[ERROR] 无法连接到RAG服务删除知识库: {kb_id}")
#         return False
#     except requests.exceptions.RequestException as e:
#         print(f"[ERROR] 删除RAG知识库网络请求失败: {kb_id}, 错误: {e}")
#         return False
#     except Exception as e:
#         print(f"[ERROR] 删除RAG知识库异常 {kb_id}: {e}")
#
#         traceback.print_exc()
#         return False
#
#
# def _delete_pg_dataset_cascade(kb_id):
#     """级联删除PG中的知识库及其关联"""
#     try:
#         # 删除用户-知识库关联
#         deleted_user_links = delete_all_kb_links(kb_id)
#
#         # 删除课程-知识库关联
#         deleted_course_links = delete_all_kb_course_links(kb_id)
#
#         # 软删除知识库本身
#         kb_deleted = delete_knowledge_base(kb_id, force=False)
#
#         if kb_deleted:
#             print(
#                 f"[INFO] 成功从PG删除知识库: {kb_id} (用户关联:{deleted_user_links}, 课程关联:{deleted_course_links})")
#             return True
#         else:
#             print(f"[ERROR] 从PG删除知识库失败: {kb_id}")
#             return False
#
#     except Exception as e:
#         print(f"[ERROR] 删除PG知识库异常 {kb_id}: {e}")
#         return False
#
#
# def _compare_dataset_details(rag_data, pg_data):
#     """详细比较RAG和PG中的知识库数据"""
#     changes = []
#     needs_update = False
#
#     try:
#         # 比较名称
#         rag_name = rag_data.get("name", "").strip()
#         pg_name = (pg_data.name or "").strip()
#         if rag_name != pg_name:
#             changes.append(f"名称: '{pg_name}' -> '{rag_name}'")
#             needs_update = True
#
#         # 比较描述
#         rag_desc = rag_data.get("description", "").strip()
#         pg_desc = (pg_data.description or "").strip()
#         if rag_desc != pg_desc:
#             changes.append(f"描述: '{pg_desc}' -> '{rag_desc}'")
#             needs_update = True
#
#         # 比较文档数量
#         rag_doc_count = rag_data.get("document_count", 0)
#         pg_doc_count = pg_data.document_count or 0
#         if rag_doc_count != pg_doc_count:
#             changes.append(f"文档数: {pg_doc_count} -> {rag_doc_count}")
#             needs_update = True
#
#         # 可以添加更多字段的比较
#         # 比如更新时间、状态等
#
#         return {
#             "needs_update": needs_update,
#             "changes": changes
#         }
#
#     except Exception as e:
#         print(f"[ERROR] 比较数据异常: {e}")
#         return {"needs_update": False, "changes": []}
#
#
# def _update_pg_dataset_from_rag_data(kb_id, rag_data):
#     """使用RAG数据更新PG知识库"""
#     try:
#         success = update_knowledge_base(
#             kb_id=kb_id,
#             name=rag_data.get("name"),
#             description=rag_data.get("description"),
#             document_count=rag_data.get("document_count", 0)
#         )
#         return success
#     except Exception as e:
#         print(f"[ERROR] 更新PG知识库异常 {kb_id}: {e}")
#         return False
#
#
# # def _create_pg_dataset_from_rag_data(rag_data):
# #     """根据RAG数据在PG中创建知识库"""
# #     try:
# #         # 由于没有用户信息，这里需要特殊处理
# #         # 可能需要设置默认用户或从created_by字段获取
# #         default_user_id = 1  # 设置默认用户ID，或者从rag_data["created_by"]获取
# #
# #         success = add_knowledge_base(
# #             kb_id=rag_data["id"],
# #             name=rag_data.get("name", ""),
# #             description=rag_data.get("description", ""),
# #             document_count=rag_data.get("document_count", 0),
# #             user_id=default_user_id
# #         )
# #
# #         return success
# #     except Exception as e:
# #         print(f"[ERROR] 创建PG知识库异常 {rag_data.get('id', 'unknown')}: {e}")
# #         return False
#
#
# def sync_knowledge_bases_rag_to_pg():
#     """
#     功能2：比较RAG和PG知识库表中的数据，不一致的且RAG也有的字段，用PG的数据覆盖RAG知识库的字段
#     PG作为主数据源，更新RAG中不一致的数据
#     """
#     print(f"[INFO] 开始执行PG到RAG的数据同步（PG覆盖RAG）")
#
#     sync_result = {
#         "operation": "pg_to_rag_sync",
#         "rag_total": 0,
#         "pg_total": 0,
#         "updated_datasets": 0,
#         "created_datasets": 0,
#         "comparison_details": [],
#         "errors": [],
#         "sync_time": datetime.now().isoformat()
#     }
#
#     try:
#         # 1. 获取RAG中的所有知识库
#         rag_datasets = _fetch_all_rag_datasets()
#         rag_dict = {dataset["id"]: dataset for dataset in rag_datasets}
#         sync_result["rag_total"] = len(rag_dict)
#
#         # 2. 获取PG中的所有知识库
#         pg_datasets = _fetch_all_pg_datasets()
#         pg_dict = {dataset.id: dataset for dataset in pg_datasets}
#         sync_result["pg_total"] = len(pg_dict)
#
#         print(f"[DEBUG] 准备比较 {len(rag_dict)} 个RAG知识库和 {len(pg_dict)} 个PG知识库")
#
#         # 3. 遍历RAG中的每个知识库
#         for kb_id, rag_data in rag_dict.items():
#             try:
#                 if kb_id in pg_dict:
#                     # PG中存在，检查是否需要更新RAG
#                     pg_data = pg_dict[kb_id]
#                     comparison = _compare_dataset_details_pg_priority(rag_data, pg_data)
#
#                     if comparison["needs_update"]:
#                         # 需要用PG数据更新RAG
#                         success = _update_rag_dataset_from_pg_data(kb_id, pg_data, rag_data)
#                         if success:
#                             sync_result["updated_datasets"] += 1
#                             print(f"[INFO] 用PG数据更新RAG知识库: {kb_id}")
#                             sync_result["comparison_details"].append({
#                                 "kb_id": kb_id,
#                                 "action": "updated",
#                                 "changes": comparison["changes"]
#                             })
#                         else:
#                             sync_result["errors"].append(f"更新RAG知识库失败: {kb_id}")
#                     else:
#                         print(f"[DEBUG] 知识库 {kb_id} 数据一致，无需更新")
#                 else:
#                     # PG中不存在，创建新的PG记录
#                     success = _create_pg_dataset_from_rag_data(rag_data)
#                     if success:
#                         sync_result["created_datasets"] += 1
#                         print(f"[INFO] 在PG中创建知识库: {kb_id}")
#                         sync_result["comparison_details"].append({
#                             "kb_id": kb_id,
#                             "action": "created",
#                             "changes": ["新建知识库"]
#                         })
#                     else:
#                         sync_result["errors"].append(f"在PG中创建知识库失败: {kb_id}")
#
#             except Exception as e:
#                 error_msg = f"处理知识库 {kb_id} 时异常: {str(e)}"
#                 print(f"[ERROR] {error_msg}")
#                 sync_result["errors"].append(error_msg)
#
#         print(f"[INFO] PG到RAG同步完成 - 更新: {sync_result['updated_datasets']}, "
#               f"创建: {sync_result['created_datasets']}")
#
#         return sync_result
#
#     except Exception as e:
#         error_msg = f"PG到RAG同步过程异常: {str(e)}"
#         print(f"[ERROR] {error_msg}")
#
#         traceback.print_exc()
#         sync_result["errors"].append(error_msg)
#         return sync_result
#
#
# def _compare_dataset_details_pg_priority(rag_data, pg_data):
#     """详细比较RAG和PG中的知识库数据 - PG优先"""
#     changes = []
#     needs_update = False
#
#     try:
#         # 比较名称 - 如果PG有名称且与RAG不同，则需要更新
#         rag_name = rag_data.get("name", "").strip()
#         pg_name = (pg_data.name or "").strip()
#         if pg_name and rag_name != pg_name:
#             changes.append(f"名称: RAG '{rag_name}' -> PG '{pg_name}'")
#             needs_update = True
#
#         # 比较描述 - 如果PG有描述且与RAG不同，则需要更新
#         rag_desc = rag_data.get("description", "").strip()
#         pg_desc = (pg_data.description or "").strip()
#         if pg_desc and rag_desc != pg_desc:
#             changes.append(f"描述: RAG '{rag_desc}' -> PG '{pg_desc}'")
#             needs_update = True
#
#         # 比较文档数量 - 如果PG有文档数且与RAG不同，则需要更新
#         rag_doc_count = rag_data.get("document_count", 0)
#         pg_doc_count = pg_data.document_count or 0
#         if pg_doc_count >= 0 and rag_doc_count != pg_doc_count:  # PG文档数大于等于0才更新
#             changes.append(f"文档数: RAG {rag_doc_count} -> PG {pg_doc_count}")
#             needs_update = True
#
#         return {
#             "needs_update": needs_update,
#             "changes": changes
#         }
#
#     except Exception as e:
#         print(f"[ERROR] 比较数据异常: {e}")
#         return {"needs_update": False, "changes": []}
#
#
# def _update_rag_dataset_from_pg_data(kb_id, pg_data, rag_data):
#     """使用PG数据更新RAG知识库"""
#     try:
#         # 构建更新数据，只更新PG中有值的字段
#         update_data = {}
#
#         # 名称
#         if pg_data.name:
#             update_data["name"] = pg_data.name
#
#         # 描述
#         if pg_data.description:
#             update_data["description"] = pg_data.description
#
#         # 其他RAG原有的字段保持不变
#         # 保留RAG中的其他字段
#         for key in ["avatar", "chunk_method", "language", "embedding_model",
#                     "similarity_threshold", "vector_similarity_weight", "parser_config"]:
#             if key in rag_data:
#                 update_data[key] = rag_data[key]
#
#         print(f"[DEBUG] 准备用PG数据更新RAG知识库 {kb_id}: {update_data}")
#
#         # 调用RAG API更新
#         resp = requests.put(
#             f"{BASE_URL}/api/v1/datasets/{kb_id}",
#             headers=HEADERS,
#             json=update_data,
#             timeout=30
#         )
#
#         if resp.status_code == 200:
#             print(f"[INFO] 成功用PG数据更新RAG知识库: {kb_id}")
#             return True
#         else:
#             print(f"[ERROR] 用PG数据更新RAG知识库失败: {kb_id}, 状态码: {resp.status_code}")
#             print(f"[ERROR] 响应内容: {resp.text}")
#             return False
#
#     except Exception as e:
#         print(f"[ERROR] 更新RAG知识库异常 {kb_id}: {e}")
#
#         traceback.print_exc()
#         return False
#
#
# def _create_pg_dataset_from_rag_data(rag_data):
#     """根据RAG数据在PG中创建知识库"""
#     try:
#         # 由于没有用户信息，这里需要特殊处理
#         # 可能需要设置默认用户或从created_by字段获取
#         default_user_id = 1  # 设置默认用户ID，或者从rag_data["created_by"]获取
#
#         success = add_knowledge_base(  # 修正：使用正确的函数名
#             kb_id=rag_data["id"],
#             name=rag_data.get("name", ""),
#             description=rag_data.get("description", ""),
#             document_count=rag_data.get("document_count", 0),
#             user_id=default_user_id
#         )
#
#         return success
#     except Exception as e:
#         print(f"[ERROR] 创建PG知识库异常 {rag_data.get('id', 'unknown')}: {e}")
#         return False

from .sync_data import (sync_knowledge_bases_intersection,
    sync_knowledge_bases_rag_to_pg,_fetch_all_rag_datasets,
     _fetch_all_pg_datasets)
# 同时修改API接口的描述
@api_view(['POST'])
def sync_datasets_rag_to_pg_api(request):
    """执行PG到RAG数据同步的API接口（PG覆盖RAG）"""
    permission_classes = [AllowAny]
    try:
        user = get_test_user(request)
        print(f"[INFO] 用户 {user.username} 触发PG到RAG同步")

        result = sync_knowledge_bases_rag_to_pg()

        return sucess_api_response({
            "message": "PG到RAG同步完成",
            "sync_result": result
        })

    except Exception as e:
        print(f"[ERROR] PG到RAG同步API调用失败: {e}")

        traceback.print_exc()
        return fail_api_response(
            {"error": "PG到RAG同步失败", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
# API接口
@api_view(['POST'])
def sync_datasets_intersection_api(request):
    """执行知识库交集同步的API接口"""
    permission_classes = [AllowAny]
    try:
        user = get_test_user(request)
        print(f"[INFO] 用户 {user.username} 触发知识库交集同步")

        result = sync_knowledge_bases_intersection()

        return sucess_api_response({
            "message": "知识库交集同步完成",
            "sync_result": result
        })

    except Exception as e:
        print(f"[ERROR] 交集同步API调用失败: {e}")
        traceback.print_exc()
        return fail_api_response(
            {"error": "交集同步失败", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
def sync_datasets_comprehensive_api(request):
    """执行综合同步：先做交集同步，再做数据更新同步"""
    permission_classes = [AllowAny]
    try:
        user = get_test_user(request)
        print(f"[INFO] 用户 {user.username} 触发知识库综合同步")

        # 先执行交集同步
        intersection_result = sync_knowledge_bases_intersection()

        # 再执行数据更新同步
        update_result = sync_knowledge_bases_rag_to_pg()

        return sucess_api_response({
            "message": "知识库综合同步完成",
            "intersection_sync": intersection_result,
            "update_sync": update_result
        })

    except Exception as e:
        print(f"[ERROR] 综合同步API调用失败: {e}")
        traceback.print_exc()
        return fail_api_response(
            {"error": "综合同步失败", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# 在文件末尾添加以下代码

@api_view(['GET'])
def knowledge_base_health_check_view(request):
    """
    知识库健康检查视图函数
    """
    try:
        print("[INFO] 开始执行知识库健康检查...")

        # 获取RAG和PG的知识库数量
        rag_datasets = _fetch_all_rag_datasets()
        pg_datasets = _fetch_all_pg_datasets()

        rag_count = len(rag_datasets)
        pg_count = len(pg_datasets)

        # 计算一致性
        rag_ids = {dataset["id"] for dataset in rag_datasets}
        pg_ids = {dataset.id for dataset in pg_datasets}

        intersection_count = len(rag_ids & pg_ids)
        consistency_rate = (intersection_count / max(rag_count, pg_count) * 100) if max(rag_count, pg_count) > 0 else 100

        # 构建详细的健康检查报告
        health_report = {
            "status": "completed",
            "check_time": datetime.now().isoformat(),
            "summary": {
                "rag_total": rag_count,
                "pg_total": pg_count,
                "intersection_count": intersection_count,
                "consistency_rate": round(consistency_rate, 2),
                "rag_only_count": len(rag_ids - pg_ids),
                "pg_only_count": len(pg_ids - rag_ids)
            },
            "details": {
                "rag_only_ids": list(rag_ids - pg_ids)[:10],  # 只显示前10个
                "pg_only_ids": list(pg_ids - rag_ids)[:10],  # 只显示前10个
                "common_ids": list(rag_ids & pg_ids)[:10]  # 只显示前10个
            },
            "health_status": "healthy" if consistency_rate >= 95 else "needs_attention",
            "recommendations": []
        }

        # 生成建议
        if consistency_rate < 95:
            health_report["recommendations"].append("数据一致性较低，建议执行同步任务")
        if len(rag_ids - pg_ids) > 0:
            health_report["recommendations"].append(f"发现 {len(rag_ids - pg_ids)} 个RAG独有知识库")
        if len(pg_ids - rag_ids) > 0:
            health_report["recommendations"].append(f"发现 {len(pg_ids - rag_ids)} 个PG独有知识库")

        if not health_report["recommendations"]:
            health_report["recommendations"].append("系统健康状态良好")

        # 使用 sucess_api_response 返回 Response 对象
        return sucess_api_response(health_report)

    except Exception as e:
        error_msg = f"知识库健康检查失败: {str(e)}"
        print(f"[ERROR] {error_msg}")
        traceback.print_exc()

        # 返回错误响应
        return fail_api_response({
            "error": error_msg,
            "check_time": datetime.now().isoformat(),
            "status": "failed"
        }, status=500)



'''----------知识库数据同步函数结束---------'''


'''----------chunk数据同步函数---------'''
def sync_chunks_pg_to_rag():
    """
    同步PG和RAG中的chunk数据
    1. 根据PG表中激活的chunk_id去查询RAG中的chunk
    2. 如果RAG中有，对比内容是否一致
    3. 如果RAG中没有，删除PG表中的记录
    """
    print(f"[INFO] 开始执行chunk数据同步")

    sync_result = {
        "operation": "chunk_sync",
        "pg_total_chunks": 0,
        "rag_found_chunks": 0,
        "content_matched": 0,
        "content_mismatched": 0,
        "deleted_from_pg": 0,
        "comparison_details": [],
        "errors": [],
        "sync_time": datetime.now().isoformat()
    }

    try:
        # 1. 获取PG中所有激活的chunk记录
        pg_chunks = _fetch_all_active_pg_chunks()
        sync_result["pg_total_chunks"] = len(pg_chunks)

        print(f"[DEBUG] PG中激活的chunk总数: {len(pg_chunks)}")

        if not pg_chunks:
            print(f"[INFO] PG中没有激活的chunk数据")
            return sync_result

        # 2. 逐个检查每个chunk
        for pg_chunk in pg_chunks:
            try:
                chunk_id = pg_chunk.chunk_id
                dataset_id = pg_chunk.knowledge_base_id
                document_id = pg_chunk.document_id

                print(f"[DEBUG] 检查chunk: {chunk_id}")

                # 从RAG获取chunk数据
                rag_chunk_data = _fetch_rag_chunk_by_id(dataset_id, document_id, chunk_id)

                if rag_chunk_data:
                    # RAG中存在，比较内容
                    sync_result["rag_found_chunks"] += 1

                    comparison = _compare_chunk_content(pg_chunk, rag_chunk_data)

                    if comparison["is_matched"]:
                        sync_result["content_matched"] += 1
                        print(f"[DEBUG] chunk {chunk_id} 内容一致")
                    else:
                        sync_result["content_mismatched"] += 1
                        print(f"[WARN] chunk {chunk_id} 内容不一致: {comparison['differences']}")

                        # 可选：用RAG数据更新PG数据
                        if _should_update_pg_from_rag():
                            success = _update_pg_chunk_from_rag(pg_chunk, rag_chunk_data)
                            if success:
                                print(f"[INFO] 已用RAG数据更新PG chunk: {chunk_id}")

                    sync_result["comparison_details"].append({
                        "chunk_id": chunk_id,
                        "dataset_id": dataset_id,
                        "document_id": document_id,
                        "status": "matched" if comparison["is_matched"] else "mismatched",
                        "differences": comparison.get("differences", [])
                    })

                else:
                    # RAG中不存在，删除PG记录
                    print(f"[WARN] RAG中不存在chunk {chunk_id}，准备删除PG记录")

                    success = _soft_delete_pg_chunk(pg_chunk)
                    if success:
                        sync_result["deleted_from_pg"] += 1
                        print(f"[INFO] 已软删除PG chunk记录: {chunk_id}")

                        sync_result["comparison_details"].append({
                            "chunk_id": chunk_id,
                            "dataset_id": dataset_id,
                            "document_id": document_id,
                            "status": "deleted_from_pg",
                            "reason": "not_found_in_rag"
                        })
                    else:
                        error_msg = f"删除PG chunk记录失败: {chunk_id}"
                        sync_result["errors"].append(error_msg)
                        print(f"[ERROR] {error_msg}")

            except Exception as e:
                error_msg = f"处理chunk {pg_chunk.chunk_id} 时异常: {str(e)}"
                print(f"[ERROR] {error_msg}")
                sync_result["errors"].append(error_msg)

        print(f"[INFO] chunk同步完成 - PG总数: {sync_result['pg_total_chunks']}, "
              f"RAG存在: {sync_result['rag_found_chunks']}, "
              f"内容匹配: {sync_result['content_matched']}, "
              f"内容不匹配: {sync_result['content_mismatched']}, "
              f"删除: {sync_result['deleted_from_pg']}")

        return sync_result

    except Exception as e:
        error_msg = f"chunk同步过程异常: {str(e)}"
        print(f"[ERROR] {error_msg}")
        traceback.print_exc()
        sync_result["errors"].append(error_msg)
        return sync_result


def _fetch_all_active_pg_chunks():
    """获取PG中所有激活的chunk记录"""
    try:
        chunks = get_all_active_chunk_qas()  # 需要在chunk_qa_utils.py中实现这个函数
        print(f"[INFO] 共获取PG中激活chunk {len(chunks)} 个")
        return chunks
    except Exception as e:
        print(f"[ERROR] 获取PG chunk数据异常: {e}")
        return []


def _fetch_rag_chunk_by_id(dataset_id, document_id, chunk_id):
    """从RAG获取指定的chunk数据"""
    try:
        # 方法1：通过chunk列表接口查找
        url = f"{BASE_URL}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks"

        # 先尝试获取所有chunks，然后查找指定的chunk
        resp = requests.get(
            url,
            headers={"Authorization": HEADERS["Authorization"]},
            params={"page": 1, "page_size": 1000},  # 假设单个文档不会超过1000个chunk
            timeout=30
        )

        if resp.status_code == 200:
            chunk_data = resp.json()
            if chunk_data.get("code") == 0:
                chunks = chunk_data.get("data", [])

                # 查找指定的chunk_id
                for chunk in chunks:
                    if chunk.get("id") == chunk_id:
                        print(f"[DEBUG] 在RAG中找到chunk: {chunk_id}")
                        return chunk

                print(f"[DEBUG] RAG中未找到chunk: {chunk_id}")
                return None
            else:
                print(f"[WARN] RAG返回业务错误: {chunk_data.get('message', '未知错误')}")
                return None
        else:
            print(f"[WARN] RAG请求失败: {resp.status_code}")
            return None

    except requests.exceptions.Timeout:
        print(f"[ERROR] 查询RAG chunk超时: {chunk_id}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] 查询RAG chunk网络错误: {chunk_id}, {e}")
        return None
    except Exception as e:
        print(f"[ERROR] 查询RAG chunk异常: {chunk_id}, {e}")
        return None


def _compare_chunk_content(pg_chunk, rag_chunk_data):
    """比较PG和RAG中chunk的内容"""
    try:
        differences = []
        is_matched = True

        # 比较content内容
        pg_content = (pg_chunk.content or "").strip()
        rag_content = rag_chunk_data.get("content", "").strip()

        if pg_content != rag_content:
            differences.append(f"内容不同: PG长度={len(pg_content)}, RAG长度={len(rag_content)}")
            is_matched = False

        # 比较questions (如果有的话)
        pg_question = (pg_chunk.question or "").strip()
        rag_questions = rag_chunk_data.get("questions", [])
        rag_question = rag_questions[0] if rag_questions else ""

        if pg_question != rag_question:
            differences.append(f"问题不同: PG='{pg_question}', RAG='{rag_question}'")
            is_matched = False

        # 可以添加更多字段的比较
        # 比如keywords等

        return {
            "is_matched": is_matched,
            "differences": differences
        }

    except Exception as e:
        print(f"[ERROR] 比较chunk内容异常: {e}")
        return {"is_matched": False, "differences": [f"比较异常: {str(e)}"]}


def _should_update_pg_from_rag():
    """判断是否应该用RAG数据更新PG数据"""
    # 这里可以根据业务需求决定
    # 如果RAG是权威数据源，返回True
    # 如果PG是权威数据源，返回False
    return False  # 默认不更新，只做检查


def _update_pg_chunk_from_rag(pg_chunk, rag_chunk_data):
    """用RAG数据更新PG chunk"""
    try:
        rag_content = rag_chunk_data.get("content", "")
        rag_questions = rag_chunk_data.get("questions", [])
        rag_question = rag_questions[0] if rag_questions else ""

        success = update_chunk_qa(
            chunk_id=pg_chunk.chunk_id,
            content=rag_content,
            question=rag_question
        )

        return success
    except Exception as e:
        print(f"[ERROR] 更新PG chunk异常: {e}")
        return False


def _soft_delete_pg_chunk(pg_chunk):
    """软删除PG中的chunk记录"""
    try:
        success = delete_chunk_qa(pg_chunk.chunk_id, soft_delete=True)
        return success
    except Exception as e:
        print(f"[ERROR] 软删除PG chunk异常: {e}")
        return False


# API接口
@api_view(['POST'])
def sync_chunks_api(request):
    """执行chunk数据同步的API接口"""
    permission_classes = [AllowAny]
    try:
        user = get_test_user(request)
        print(f"[INFO] 用户 {user.username} 触发chunk数据同步")

        # 获取可选参数
        dataset_id = request.data.get('dataset_id')  # 可选：只同步指定知识库
        force_update = request.data.get('force_update', False)  # 是否强制更新不一致的数据

        if dataset_id:
            print(f"[INFO] 只同步知识库 {dataset_id} 的chunk数据")
            result = sync_chunks_by_dataset(dataset_id, force_update)
        else:
            result = sync_chunks_pg_to_rag()

        return sucess_api_response({
            "message": "chunk数据同步完成",
            "sync_result": result
        })

    except Exception as e:
        print(f"[ERROR] chunk同步API调用失败: {e}")
        traceback.print_exc()
        return fail_api_response(
            {"error": "chunk同步失败", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def sync_chunks_by_dataset(dataset_id, force_update=False):
    """同步指定知识库的chunk数据"""
    print(f"[INFO] 开始同步知识库 {dataset_id} 的chunk数据")

    sync_result = {
        "operation": "chunk_sync_by_dataset",
        "dataset_id": dataset_id,
        "pg_chunks_in_dataset": 0,
        "rag_found_chunks": 0,
        "content_matched": 0,
        "content_mismatched": 0,
        "deleted_from_pg": 0,
        "updated_from_rag": 0,
        "comparison_details": [],
        "errors": [],
        "sync_time": datetime.now().isoformat()
    }

    try:
        # 获取指定知识库的所有激活chunk
        pg_chunks = get_chunk_qas_by_dataset(dataset_id)
        if hasattr(pg_chunks, 'filter'):
            pg_chunks = pg_chunks.filter(is_active=True)

        pg_chunks = list(pg_chunks)
        sync_result["pg_chunks_in_dataset"] = len(pg_chunks)

        print(f"[DEBUG] 知识库 {dataset_id} 中有 {len(pg_chunks)} 个激活chunk")

        for pg_chunk in pg_chunks:
            try:
                chunk_id = pg_chunk.chunk_id
                document_id = pg_chunk.document_id

                # 从RAG获取chunk数据
                rag_chunk_data = _fetch_rag_chunk_by_id(dataset_id, document_id, chunk_id)

                if rag_chunk_data:
                    sync_result["rag_found_chunks"] += 1

                    comparison = _compare_chunk_content(pg_chunk, rag_chunk_data)

                    if comparison["is_matched"]:
                        sync_result["content_matched"] += 1
                    else:
                        sync_result["content_mismatched"] += 1

                        if force_update:
                            success = _update_pg_chunk_from_rag(pg_chunk, rag_chunk_data)
                            if success:
                                sync_result["updated_from_rag"] += 1
                                print(f"[INFO] 已用RAG数据更新PG chunk: {chunk_id}")

                    sync_result["comparison_details"].append({
                        "chunk_id": chunk_id,
                        "document_id": document_id,
                        "status": "matched" if comparison["is_matched"] else "mismatched",
                        "differences": comparison.get("differences", [])
                    })

                else:
                    # RAG中不存在，删除PG记录
                    success = _soft_delete_pg_chunk(pg_chunk)
                    if success:
                        sync_result["deleted_from_pg"] += 1
                        print(f"[INFO] 已删除不存在于RAG的chunk: {chunk_id}")

            except Exception as e:
                error_msg = f"处理chunk {pg_chunk.chunk_id} 时异常: {str(e)}"
                sync_result["errors"].append(error_msg)

        return sync_result

    except Exception as e:
        error_msg = f"同步知识库 {dataset_id} chunk异常: {str(e)}"
        print(f"[ERROR] {error_msg}")
        sync_result["errors"].append(error_msg)
        return sync_result


'''----------chunk数据同步函数结束---------'''

'''--------知识库和课程绑定接口------------'''
class CourseKBLinkView(APIView):
    permission_classes = [AllowAny]
    """课程-知识库关联管理"""
    permission_map={
        'post': ['knowledge_base.link_course_to_knowledge_bases'],
        'delete': ['knowledge_base.unlink_course_from_knowledge_bases']
    }
    def post(self, request):
        """
        批量绑定知识库和课程（会先删除原有绑定，再绑定新课程）
        参数示例：
        {
            "kb_id": "xxx",
            "course_ids": [1, 2, 3]
        }
        """
        try:
            user = get_test_user(request)
            kb_id = request.data.get('kb_id')
            course_ids = request.data.get('course_ids')

            if not kb_id or not course_ids or not isinstance(course_ids, list):
                return fail_api_response({
                    "code": 400,
                    "message": "kb_id和course_ids（数组）不能为空"
                }, status=400)

            # 检查知识库是否存在
            kb = get_knowledge_base_by_id(kb_id)
            if not kb:
                return fail_api_response({
                    "code": 404,
                    "message": "知识库不存在"
                }, status=404)

            # 1. 删除原有绑定
            deleted_count = delete_all_kb_course_links(kb_id)
            print(f"[INFO] 已删除知识库{kb_id}原有{deleted_count}个课程绑定")

            # 2. 重新绑定
            results = []
            for course_id in course_ids:
                course = get_course_by_id(course_id)
                if not course:
                    results.append({
                        "course_id": course_id,
                        "success": False,
                        "message": "课程不存在"
                    })
                    continue
                success = add_course_kb_link(course_id, kb_id)
                results.append({
                    "course_id": course_id,
                    "success": success,
                    "message": "绑定成功" if success else "绑定失败"
                })

            return sucess_api_response({
                "code": 0,
                "message": "批量绑定完成（原有绑定已清空）",
                "results": results
            }, status=200)

        except Exception as e:
            print(f"[ERROR] 批量绑定知识库和课程失败: {e}")
            traceback.print_exc()
            return fail_api_response({
                "code": 500,
                "message": f"绑定失败: {str(e)}"
            }, status=500)

    def delete(self, request):
        """批量解绑知识库和课程，支持数组"""
        try:
            user = get_test_user(request)
            print(f"[INFO] 用户(ID:{user.id}, 用户名:{user.username})正在批量解绑知识库和课程")

            # 支持单个或多个
            kb_ids = request.data.get('kb_ids') or request.data.get('kb_id')
            course_ids = request.data.get('course_ids') or request.data.get('course_id')

            # 兼容单个字符串/数字
            if isinstance(kb_ids, (str, int)):
                kb_ids = [kb_ids]
            if isinstance(course_ids, (str, int)):
                course_ids = [course_ids]

            if not kb_ids or not course_ids:
                return fail_api_response({
                    "code": 400,
                    "message": "知识库ID和课程ID不能为空"
                }, status=400)

            print(f"[DEBUG] 批量解绑参数: kb_ids={kb_ids}, course_ids={course_ids}")

            results = []
            for kb_id in kb_ids:
                for course_id in course_ids:
                    # 检查绑定关系是否存在
                    existing_links = get_kb_course_links(kb_id)
                    link_exists = any(str(link.course.id) == str(course_id) for link in existing_links)
                    if not link_exists:
                        results.append({
                            "kb_id": kb_id,
                            "course_id": course_id,
                            "success": False,
                            "message": f"知识库{kb_id}和课程{course_id}未绑定"
                        })
                        continue

                    # 执行解绑
                    success = delete_course_kb_link(course_id, kb_id)
                    if success:
                        results.append({
                            "kb_id": kb_id,
                            "course_id": course_id,
                            "success": True,
                            "unbind_time": datetime.now().isoformat(),
                            "operator_id": user.id,
                            "operator_name": user.username
                        })
                    else:
                        results.append({
                            "kb_id": kb_id,
                            "course_id": course_id,
                            "success": False,
                            "message": "解绑失败，数据库操作异常"
                        })

            return sucess_api_response({
                "code": 0,
                "message": "批量解绑完成",
                "results": results
            }, status=200)

        except Exception as e:
            print(f"[ERROR] 批量解绑知识库和课程失败: {e}")
            traceback.print_exc()
            return fail_api_response({
                "code": 500,
                "message": f"解绑失败: {str(e)}"
            }, status=500)
    # def delete(self, request):
    #     """解绑知识库和课程"""
    #     try:
    #         user = get_test_user(request)
    #         print(f"[INFO] 用户(ID:{user.id}, 用户名:{user.username})正在解绑知识库和课程")
    #
    #         # 获取参数
    #         kb_id = request.data.get('kb_id')
    #         course_id = request.data.get('course_id')
    #
    #         # 参数验证
    #         if not kb_id:
    #             return fail_api_response({
    #                 "code": 400,
    #                 "message": "知识库ID不能为空"
    #             }, status=400)
    #
    #         if not course_id:
    #             return fail_api_response({
    #                 "code": 400,
    #                 "message": "课程ID不能为空"
    #             }, status=400)
    #
    #         print(f"[DEBUG] 解绑参数: kb_id={kb_id}, course_id={course_id}")
    #
    #         # 检查绑定关系是否存在
    #         existing_links = get_kb_course_links(kb_id)
    #         link_exists = False
    #         for link in existing_links:
    #             if str(link.course.id) == str(course_id):
    #                 link_exists = True
    #                 break
    #
    #         if not link_exists:
    #             return fail_api_response({
    #                 "code": 404,
    #                 "message": f"知识库{kb_id}和课程{course_id}未绑定"
    #             }, status=404)
    #
    #         # 执行解绑
    #         success = delete_course_kb_link(course_id, kb_id)
    #
    #         if success:
    #             print(f"[INFO] 成功解绑知识库{kb_id}和课程{course_id}")
    #
    #             response_data = {
    #                 "code": 0,
    #                 "message": "解绑成功",
    #                 "data": {
    #                     "kb_id": kb_id,
    #                     "course_id": course_id,
    #                     "unbind_time": datetime.now().isoformat(),
    #                     "operator_id": user.id,
    #                     "operator_name": user.username
    #                 }
    #             }
    #
    #             return sucess_api_response(response_data, status=200)
    #         else:
    #             return fail_api_response({
    #                 "code": 500,
    #                 "message": "解绑失败，数据库操作异常"
    #             }, status=500)
    #
    #     except Exception as e:
    #         print(f"[ERROR] 解绑知识库和课程失败: {e}")
    #         traceback.print_exc()
    #         return fail_api_response({
    #             "code": 500,
    #             "message": f"解绑失败: {str(e)}"
    #         }, status=500)



class CourseKBListView(APIView):
    """查看课程绑定的知识库列表"""
    permission_classes =   [IsAuthenticated]
    def get(self, request, course_id):
        """获取指定课程绑定的所有知识库"""
        try:
            user = get_test_user(request)
            print(f"[INFO] 用户(ID:{user.id}, 用户名:{user.username})查询课程{course_id}绑定的知识库")

            # 检查课程是否存在
            course = get_course_by_id(course_id)
            if not course:
                return fail_api_response({
                    "code": 404,
                    "message": f"课程不存在: {course_id}"
                }, status=404)

            # 获取课程绑定的知识库
            kb_links = get_course_kb_links(course_id)

            # 构建响应数据
            knowledge_bases = []
            for link in kb_links:
                try:
                    kb = link.knowledge_base
                    kb_info = {
                        "kb_id": kb.id,
                        "kb_name": kb.name,
                        "kb_description": kb.description,
                        "document_count": kb.document_count,
                        "is_active": kb.is_active,
                        "created_at": kb.created_at.isoformat() if kb.created_at else None,
                        "updated_at": kb.updated_at.isoformat() if kb.updated_at else None,
                        "creator_id": kb.user.id if kb.user else None,
                        "creator_name": kb.user.username if kb.user else "未知用户",
                        "bind_time": link.linked_at.isoformat() if hasattr(link, 'linked_at') and link.linked_at else None
                    }
                    knowledge_bases.append(kb_info)
                except Exception as e:
                    print(f"[ERROR] 处理知识库信息失败: {e}")
                    traceback.print_exc()
                    # 这里改为返回错误而不是继续，避免静默失败
                    return fail_api_response({
                        "code": 500,
                        "message": f"处理知识库信息失败: {str(e)}"
                    }, status=500)

            response_data = {
                "code": 0,
                "message": "查询成功",
                "data": {
                    "course_id": course_id,
                    "course_name": course.name if hasattr(course, 'name') else course.title if hasattr(course, 'title') else "课程",
                    "total_count": len(knowledge_bases),
                    "knowledge_bases": knowledge_bases,
                    "query_time": datetime.now().isoformat()
                }
            }

            print(f"[INFO] 课程{course_id}绑定了{len(knowledge_bases)}个知识库")
            return sucess_api_response(response_data, status=200)

        except Exception as e:
            print(f"[ERROR] 查询课程绑定的知识库失败: {e}")
            traceback.print_exc()
            return fail_api_response({
                "code": 500,
                "message": f"查询失败: {str(e)}"
            }, status=500)



class KBCourseListView(APIView):
    """查看知识库绑定的课程列表"""
    permission_classes = [IsAuthenticated]

    def get(self, request, kb_id):
        """获取指定知识库绑定的所有课程"""
        try:
            user = get_test_user(request)
            print(f"[INFO] 用户(ID:{user.id}, 用户名:{user.username})查询知识库{kb_id}绑定的课程")

            # 检查知识库是否存在
            kb = get_knowledge_base_by_id(kb_id)
            if not kb:
                return fail_api_response({
                    "code": 404,
                    "message": f"知识库不存在: {kb_id}"
                }, status=404)

            # 获取知识库绑定的课程
            course_links = get_kb_course_links(kb_id)

            # 构建响应数据
            courses = []
            for link in course_links:
                try:
                    course = link.course
                    course_info = {
                        "course_id": course.id,
                        "course_title": course.title if hasattr(course, 'title') else "课程",
                        "course_description": course.description if hasattr(course, 'description') else "",
                        "course_status": course.status if hasattr(course, 'status') else "未知",
                        "created_at": course.created_at.isoformat() if hasattr(course,
                                                                               'created_at') and course.created_at else None,
                        "bind_time": link.created_at.isoformat() if hasattr(link,
                                                                            'created_at') and link.created_at else None
                    }
                    courses.append(course_info)
                except Exception as e:
                    print(f"[WARN] 处理课程信息失败: {e}")
                    continue

            response_data = {
                "code": 0,
                "message": "查询成功",
                "data": {
                    "kb_id": kb_id,
                    "kb_name": kb.name,
                    "kb_description": kb.description,
                    "total_count": len(courses),
                    "courses": courses,
                    "query_time": datetime.now().isoformat()
                }
            }

            print(f"[INFO] 知识库{kb_id}绑定了{len(courses)}个课程")
            return sucess_api_response(response_data, status=200)

        except Exception as e:
            print(f"[ERROR] 查询知识库绑定的课程失败: {e}")
            traceback.print_exc()
            return fail_api_response({
                "code": 500,
                "message": f"查询失败: {str(e)}"
            }, status=500)
'''知识库和课程绑定接口结束'''
#@