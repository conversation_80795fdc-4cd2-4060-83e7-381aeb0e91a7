from rest_framework.permissions import BasePermission
from guardian.shortcuts import get_perms
import sys
import os
sys.path.insert(0, os.path.abspath("/root/recommendation/knowledge"))
from pgdatabase import *

class CanManageDataset(BasePermission):
    """
    仅允许老师（有 manage_dataset 权限）增删改知识库
    """
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.role == 'teacher'

class CanViewDataset(BasePermission):
    """
    允许有 view_dataset 权限的用户（老师和学生）查看知识库
    """
    def has_permission(self, request, view):
        return request.user.is_authenticated


class CanAccessKnowledgeBase(BasePermission):
    """检查用户是否有权限访问特定知识库"""

    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False

        # 对于需要检查特定知识库权限的操作
        kb_id = view.kwargs.get('dataset_id')
        if kb_id:
            user_id = request.user.id
            user_kb_links = get_user_kb_links(user_id)
            kb_ids = [link.kb_id for link in user_kb_links]
            return int(kb_id) in kb_ids

        return True