import docx
import json
import os
import requests
import time

from docx import Document
from django_filters.rest_framework import DjangoFilterBackend
from django.conf import settings
from django.core.files.storage import default_storage
from django.db import transaction
from django.db.models import Q
from django.http import StreamingHttpResponse
from django.utils import timezone
from langchain_core.prompts import PromptTemplate
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from openai import OpenAI
from rest_framework import mixins, status, viewsets, permissions
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response

from .conversation import *
from .filters import Ppt<PERSON>ilter, PptTmpFilter
from .models import PptTmpModel, PptModel
from .prompt import ppt_content_template, ppt_outline_template, ppt_regenerate_outline_template
from .serializers import *
from .utils import generate_stream_content, api_response, upload_file_docx
from .ai_ppt import AIPPTService

from teachplan.utils import filter_think_tags, chunk_retrievals
from user.permissions import TablePermission


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 10  # 每页的数据量
    page_size_query_param = 'page_size'  # 允许客户端通过 `page_size` 参数自定义每页数据量
    max_page_size = 100  # 每页最大数据量


class PptTmpViewSet(viewsets.ModelViewSet):
    """
    PPT模版视图集
    """
    queryset = PptTmpModel.objects.all()
    serializer_class = PptTmpModelSerializer
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend]  # 启用过滤后端
    filterset_class = PptTmpFilter  # 指定过滤器类
    lookup_field = 'id'
    # permission_classes = (TablePermission,)
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request, *args, **kwargs):
        """
        获取PPT模版列表
        """
        user = request.user
        if not user:
            return api_response(400, '用户Session未正常传输')
        
        queryset = self.filter_queryset(self.get_queryset())
        queryset = queryset.filter(
            Q(is_public=True) | Q(is_public=False, author_id=user.id)
        ).order_by('-id')  # 按照ID降序排列
        
        # 分页处理
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            response_data = self.get_paginated_response(serializer.data).data
        else:
            serializer = self.get_serializer(queryset, many=True)
            response_data = serializer.data

        return api_response(200, 'success', response_data)

    def retrieve(self, request, *args, **kwargs):
        """
        获取PPT模板
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        instance = self.get_object()
        tmp_json_file = os.path.join(settings.MEDIA_ROOT, instance.tmp_json_file.path)
        with open(tmp_json_file, 'r', encoding='utf-8') as file:  # 读取JSON文件内容
            data = file.read()
            json_data = json.loads(data)  # 解析JSON数据
        data = {
            'id': instance.id,
            'ppt_tmp_json': json_data
        }
        return Response(data)

    def create(self, request, *args, **kwargs):
        """
        上传保存PPT模版
        """
        user_id = request.user.id
        if not user_id:
            return api_response(400, '用户Session未正常传输')
        data = request.data.copy()
        data['author_id'] = user_id
        serializers = self.get_serializer(data=data)

        try:
            serializers.is_valid(raise_exception=True)
            ppt_tmp = serializers.save()

            # 处理上传的PPT模板图片并保存在json_file_content中
            images = request.FILES.getlist('images')  # 获取上传的图片文件列表
            if images:
                # 初始化json_file_content
                ppt_tmp.json_file_content = {
                    "images": []
                }
                # 遍历处理上传的图片文件
                for idx, upload_file in enumerate(images):
                    file_path = default_storage.save(
                        ppt_png_path(ppt_tmp.id, upload_file.name),
                        upload_file
                    )
                    # 构建图片信息
                    image_info = {
                    "id": idx + 1,  # 图片ID
                    "url": default_storage.url(file_path),  # 图片URL
                    "path": file_path  # 图片路径
                    }
                    ppt_tmp.json_file_content['images'].append(image_info)  # 添加到JSON数组中
                ppt_tmp.save()  # 保存PPT模板对象
            return api_response(200, 'PPT模版保存成功', serializers.data)
        except Exception as e:
            return api_response(400, 'error', str(e))


    def update(self, request, *args, **kwargs):
        """
        更新PPT模版
        """
        instance = self.get_object()
        serializers = self.get_serializer(instance, data=request.data, partial=True)
        try:
            serializers.is_valid(raise_exception=True)
            # 获取软删除标志
            is_deleted = serializers.validated_data.get('is_deleted', None)
            if is_deleted:
                # 设置软删除时间为当前时间
                serializers.validated_data['deleted_at'] = timezone.now()
            else:
                # 如果不是软删除，重置软删除时间为None
                serializers.validated_data['deleted_at'] = None
            self.perform_update(serializers)

            # 处理更新的PPT模板图片
            images = request.FILES.getlist('images')
            if images:
                # 初始化或获取现有的图片列表
                instance.json_file_content = {
                    "images": []
                }
                
                # 计算新图片的起始ID
                exiting_count = len(instance.json_file_content['images'])

                for idx, upload_file in enumerate(images):
                    file_path = default_storage.save(
                        ppt_png_path(instance.id, upload_file.name),
                        upload_file
                    )
                    image_info = {
                        "id": idx + 1 + exiting_count,  # 图片ID
                        "url": default_storage.url(file_path),  # 图片URL      
                    }
                    instance.json_file_content['images'].append(image_info)  # 添加到JSON数组中
                instance.save()  # 保存PPT模板对象
                print(instance.json_file_content)
            return api_response(200, 'success', serializers.data)
        except Exception as e:
            return api_response(400, 'error', str(e))

    @action(methods=['POST'], detail=False, url_path='soft_delete')
    def soft_delete_or_restore(self, request, *args, **kwargs):
        """
        软删除及恢复PPT模版
        """
        ids = request.data.get('ids', [])
        action_type = request.data.get('action', '')

        if not ids or action_type not in ['delete', 'restore']:
            return api_response(200, 'error', '参数错误，需提供ids列表和action "delete 或 restore"')

        user = request.user
        if not user:
            return api_response(400, '用户Session未正常传输')
        
        # 只允许用户操作自己创建的模版
        queryset = self.get_queryset().filter(id__in=ids, author_id=user.id)
        if not queryset.exists():  # 检查是否存在要操作的对象
            return api_response(400, 'error', '找不到对应的记录或无权限操作')

        # 软删除时，同时更新deleted_at字段为当前时间
        if action_type == 'delete':
            queryset.update(
                is_deleted=True,
                deleted_at=timezone.now(),
            )  # 软删除
            return api_response(200, 'success', 'PPT模版删除成功')

        elif action_type == 'restore':
            queryset.update(
                is_deleted=False,
                deleted_at=None
            )  # 恢复
            return api_response(200, 'success', 'PPT模版恢复成功')

    @action(methods=['POST'], detail=False, url_path='hard_delete')
    def hard_delete(self, request, *args, **kwargs):
        """
        硬删除PPT模版（物理删除数据库记录）支持单个删除和批量删除
        """
        ids = request.data.get('ids', [])
        if not ids:  # 检查是否提供了ids列表
            return api_response(400, 'error', '参数错误，需提供ids列表')
        user = request.user
        if not user:
            return api_response(400, '用户Session未正常传输')
        
        # 只允许用户操作自己创建的模版
        queryset = self.get_queryset().filter(id__in=ids, author_id=user.id)
        deleted_count = queryset.count()

        if deleted_count == 0:  # 检查是否存在要删除的对象
            return api_response(400, 'error', '找不到对应的记录或无权限操作')
        queryset.delete()  # 执行物理删除
        return api_response(200, 'success', 'PPT模版彻底删除成功')


class PptViewSet(viewsets.ModelViewSet):
    """
    PPT视图集
    """
    queryset = PptModel.objects.all()
    serializer_class = PptModelSerializer
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend]  # 启用过滤后端
    filterset_class = PptFilter  # 指定过滤器类
    permission_classes = [permissions.IsAuthenticated]

    def create(self, request, *args, **kwargs):
        """
        保存PPT
        """
        # 检查用户是否存在
        user_id = request.user.id
        if not user_id:
            return api_response(400, '用户Session未正常传输')
        
        # chapter_ids = request.POST.getlist('chapter')  # 获取数组（字符串类型）
        # chapter = [int(chapter_ids) for chapter_ids in chapter_ids]
        
        data = request.data.copy()
        data['author_id'] = user_id
        # data['chapter'] = chapter
        print(data)


        serializers = self.get_serializer(data=data)

        try:
            serializers.is_valid(raise_exception=True)
            self.perform_create(serializers)
            return api_response(200, 'PPT保存成功', serializers.data)
        except Exception as e:
            return api_response(400, 'error', str(e))

    def list(self, request, *args, **kwargs):
        """
        获取PPT列表
        """
        print(f"原始查询集：{self.get_queryset().count()}")
        print(f"请求参数：{request.query_params}")
        user = request.user
        if not user:
            return api_response(400, '用户Session未正常传输')
        queryset = self.filter_queryset(self.get_queryset()).filter(author_id=user.id).order_by('-id')
        
        # 分页处理
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            response_data = self.get_paginated_response(serializer.data).data
        else:
            serializer = self.get_serializer(queryset, many=True)
            response_data = serializer.data
        
        return api_response(200, 'success', response_data)
    
    def retrieve(self, request, *args, **kwargs):
        """
        获取PPT详情
        """
        try:
            instance = self.get_object()
        except Exception as e:
            return api_response(400, 'error', str(e))
        serializer = self.get_serializer(instance)
        return api_response(200,'success', serializer.data)

    
    def update(self, request, *args, **kwargs):
        """
        更新PPT
        """
        instance = self.get_object()
        user = request.user
        if not user or instance.author_id != user.id:
            return api_response(400, '无权限操作')
        serializers = self.get_serializer(instance, data=request.data, partial=True)
        try:
            serializers.is_valid(raise_exception=True)
            # 获取软删除标志
            is_deleted = serializers.validated_data.get('is_deleted', None)
            if is_deleted:
                # 设置软删除时间为当前时间
                serializers.validated_data['deleted_at'] = timezone.now()
            else:
                # 如果不是软删除，重置软删除时间为None
                serializers.validated_data['deleted_at'] = None
            self.perform_update(serializers)
            return api_response(200, 'success', serializers.data)
        except Exception as e:
            return api_response(400, 'error', str(e))
        
    @action(methods=['POST'], detail=False, url_path='batch_unlink')
    def cancel_relate_chapter(self, request, *args, **kwargs):
        """
        取消关联章节
        """
        ids = request.data.get('ppt_ids', [])
        if not ids:  # 检查是否提供了ids列表
            return api_response(400, 'error', '参数错误，需提供ids列表')
        user = request.user
        if not user:
            return api_response(400, '用户Session未正常传输')
        # 只允许用户操作自己创建的PPT
        queryset = self.get_queryset().filter(id__in=ids, author_id=user.id)
        updated_count = queryset.count()

        if updated_count == 0:  # 检查是否存在要取消关联的对象
            return api_response(400, 'error', '找不到对应的记录或无权限取消关联')
        for ppt in queryset:
            ppt.chapter.clear()  # 清空所有关联章节
        return api_response(200,'success', f'章节成功取消关联 {updated_count} 份PPT')

    @action(methods=['POST'], detail=False, url_path='soft_delete')
    def soft_delete_or_restore(self, request, *args, **kwargs):
        """
        软删除及恢复PPT模版
        """
        ids = request.data.get('ids', [])
        action_type = request.data.get('action', '')

        if not ids or action_type not in ['delete', 'restore']:
            return api_response(400, 'error', '参数错误，需提供ids列表和action "delete 或 restore"')
        
        user = request.user
        if not user:
            return api_response(400, '用户Session未正常传输')

        queryset = self.get_queryset().filter(id__in=ids, author_id=user.id)
        if not queryset.exists():  # 检查是否存在要操作的对象
            return api_response(400, 'error', '找不到对应的记录或无权限操作')

        if action_type == 'delete':
            queryset.update(
                is_deleted=True,
                deleted_at=timezone.now(),
            )  # 软删除
            return api_response(200, 'success', '删除成功')

        elif action_type == 'restore':
            queryset.update(
                is_deleted=False,
                deleted_at=None
            )  # 恢复
            return api_response(200, 'success', '恢复成功')

    @action(methods=['POST'], detail=False, url_path='hard_delete')
    def hard_delete(self, request, *args, **kwargs):
        """
        硬删除PPT（物理删除数据库记录）支持单个删除和批量删除
        """
        ids = request.data.get('ids', [])
        if not ids:  # 检查是否提供了ids列表
            return api_response(400, 'error', '参数错误，需提供ids列表')
        user = request.user
        if not user:
            return api_response(400, '用户Session未正常传输')
        # 只允许用户操作自己创建的PPT
        queryset = self.get_queryset().filter(id__in=ids, author_id=user.id)
        deleted_count = queryset.count()

        if deleted_count == 0:  # 检查是否存在要删除的对象
            return api_response(400, 'error', '找不到对应的记录或无权限操作')
        queryset.delete()  # 执行物理删除
        return api_response(200, 'success', 'PPT彻底删除成功')

    @action(methods=['POST'], detail=False, url_path='outline')
    def get_ppt_outline(self, request):
        """
        获取PPT大纲
        """
        chapter_ids = request.POST.getlist('chapter')
        print(f"chapter_ids: {chapter_ids}")
        
        dataset_ids = request.POST.getlist('dataset_ids')
        print(f"dataset_ids: {dataset_ids}")

        data = request.data
        
        if 'theme' not in data or not isinstance(data['theme'], str):
            return Response({'error': '缺少主题词'}, status=status.HTTP_400_BAD_REQUEST)

        theme = data['theme']
        input_parts = [f"PPT主题：{theme}"]

        if chapter_ids:
            chapters = Chapter.objects.filter(id__in=chapter_ids)
            if not chapters:
                return api_response(400, 'error', '章节不存在')
            chapter_content = "\n".join(
                f"{chapter.title}：{chapter.content}"
                for chapter in chapters
            )
            input_parts.append(f"关联章节：{chapter_content}")

        contents = ''
        if dataset_ids:
            contents = chunk_retrievals(theme, dataset_ids)
            input_parts.append(f"关联知识库：{contents}")


        document = request.FILES.get('document')  # 获取上传的文件
        print(f"ppt_outline_document: {document}")
        document_content = ""  # 初始化文件内容为空
        if document:  # 如果有文件上传
            if not document.name.lower().endswith('.docx'):  # 检查文件类型
                return api_response(400, 'error', '仅支持上传Word文档(.docx)')
            try:
                document_content = upload_file_docx(document)  # 处理上传的文件
                input_parts.append(f"上传文档内容：{document_content}")
            except Exception as e:
                return api_response(400, 'error', str(e))

        user_input = "\n\n".join(input_parts)
        print(f"用户输入内容：{user_input}")

        llm = ChatOpenAI(
            # model="QwQ-think",
            model=settings.LLM_NAME,
            openai_api_key=settings.LLM_API_KEY,
            openai_api_base=settings.LLM_BASE_URL_LESSON,
            max_tokens=4000,
            temperature=1,
        )
        prompt = ChatPromptTemplate.from_messages([
            ("system", ppt_outline_template),
            ("human", "{user_input}"),
        ])
        messages = prompt.format_messages(
            file_content=document_content,
            user_input=user_input
        )

        res = llm.stream(messages)  # 流式输出
        print(f"res:{res}")
        # def generate():
        #     for chunk in res:
        #         content = chunk.content
        #         if content:
        #             yield content
        return StreamingHttpResponse(filter_think_tags(res), content_type="text/plain; charset=utf-8")  # 返回响应大纲

    @action(methods=['POST'], detail=False, url_path='re_outline')
    def regenerate_ppt_outline(self, request):
        """
        重新生成PPT大纲
        """
        data = request.data
        descriptions = data.get('descriptions')  # 获取用户提供的描述信息
        contents = data.get('contents')  # 获取用户要修改的大纲
        user_input = f"主题描述：{descriptions}\n现有大纲内容：{contents}"  # 构建用户输入

        llm = ChatOpenAI(
            # model="QwQ-think",
            model=settings.LLM_NAME,
            openai_api_key=settings.LLM_API_KEY,
            openai_api_base=settings.LLM_BASE_URL_LESSON,
            max_tokens=4000,
            temperature=1,
        )
        prompt = ChatPromptTemplate.from_messages([
            ("system", "{ppt_regenerate_outline_template}"),
            ("human", "{user_input}"),
        ])
        messages = prompt.format_messages(
            ppt_regenerate_outline_template=ppt_regenerate_outline_template,
            user_input=user_input
        )

        res = llm.stream(messages)  # 流式输出
        # def generate():
        #     for chunk in res:
        #         content = chunk.content
        #         if content:
        #             yield content
        return StreamingHttpResponse(filter_think_tags(res), content_type="text/plain; charset=utf-8")  # 返回响应大纲

    @action(methods=['POST'], detail=False, url_path='content')
    def get_ppt_content(self, request):
        """
        获取PPT内容
        """
        data = request.data
        if 'outline' not in data or not isinstance(data['outline'], str):
            return Response({'error': '缺少大纲内容'}, status=status.HTTP_400_BAD_REQUEST)
        outline = data.get('outline')


        ppt_service = AIPPTService()

        def event_stream():
            """事件流生成器"""
            # progress_data = {
            #     "type": "cover",
            #     "data": {
            #         "message": "开始生成PPT内容",
            #     }
            # }
            # json_data = json.dumps(progress_data, ensure_ascii=False)
            # yield f"data: {json_data}\n\n"


            # 处理流式生成
            for slide in ppt_service.generate_ppt_stream(outline):
                # 单页PPT的内容
                progress_data = {
                    "type": slide["type"],
                    "data": slide["data"] if slide["type"] != "end" else None
                }
                json_data = json.dumps(progress_data, ensure_ascii=False)
                yield f"data: {json_data}\n\n"

            # # 结束事件
            # progress_data = {
            #     "type": "end",
            #     "data": {
            #         "message": "PPT内容生成完成",
            #     }
            # }
            # json_data = json.dumps(progress_data, ensure_ascii=False)

            # yield f"data: {json_data}\n\n"  # 打印输出完整的ppt json内容
        
        return StreamingHttpResponse(
            event_stream(),
            content_type='text/event-stream; charset=utf-8',
            headers={
                'Cache-Control': 'no-cache',
                'X-Accel-Buffering': 'no',
                }
            )


        # llm = ChatOpenAI(
        #     model=settings.LLM_NAME,
        #     openai_api_key=settings.LLM_API_KEY,
        #     openai_api_base=settings.LLM_BASE_URL_LESSON,
        #     max_tokens=6000,
        #     temperature=1,
        #     streaming=True  # 启用流式响应
        # )

        # prompt = ChatPromptTemplate.from_messages([
        #     ("system", "{ppt_generate_content_template}"),
        #     ("human", "{user_input}"),
        # ])
        # messages = prompt.format_messages(
        #     ppt_generate_content_template=ppt_content_template,
        #     user_input=outline
        # )

        # # 创建一个生成器函数来处理流式响应
        # def event_stream():
        #     for chunk in llm.stream(messages):
        #         print(chunk.content)
        #         yield chunk.content

        # return StreamingHttpResponse(event_stream(), content_type='text/event-stream; charset=utf-8')


        # llm = ChatOpenAI(
        #     # model="QwQ-think",
        #     model=settings.LLM_NAME,
        #     openai_api_key=settings.LLM_API_KEY,
        #     openai_api_base=settings.LLM_BASE_URL_LESSON,
        #     max_tokens=4000,
        #     temperature=1,
        # )
        # prompt = ChatPromptTemplate.from_messages([
        #     ("system", "{ppt_generate_content_template}"),
        #     ("human", "{user_input}"),
        # ])
        # messages = prompt.format_messages(
        #     ppt_generate_content_template=ppt_content_template,
        #     user_input=outline
        # )

        # result = llm.invoke(messages).content
        # print(f"ppt_content_result:{result}")
        # # 在返回response的时候，指定utf-8的编码格式
        # return StreamingHttpResponse(generate_stream_content(result),
        #                              content_type='text/event-stream; charset=utf-8',
        #                              headers={'Cache-Control': 'no-cache'},
        #                              status=status.HTTP_200_OK)


    @action(methods=['POST'], detail=False, url_path='conversation')
    def get_ppt_conversation(self, request):
        """
        获取PPT的AI对话
        """
        data = request.data
        llm_chain = build_llm_chain()  # 构建LLM链
        graph = build_conversation_graph(llm_chain, streaming=True)  # 构建对话图，可改为 False 测试非流式模式

        user_input = data.get('user_input')  # 获取用户输入
        user_id = request.user.username  # 获取用户ID
        output = run_for_user(user_id, user_input, graph)  # 运行对话
        print(f"output:{output}")
        return StreamingHttpResponse(output, status=status.HTTP_200_OK)  # 返回响应
