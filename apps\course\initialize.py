import os
import sys
import django
from .models import Semester


class Initialize:
    def init_semester(self):
        """
        初始化学期
        """
        # semester1 = Semester.objects.create(name='2024-2025 上学期', start_date='2024-09-01', end_date='2024-12-01')
        # semester2 = Semester.objects.create(name='2024-2025 下学期', start_date='2025-01-01', end_date='2025-07-01')
        # semester3 = Semester.objects.create(name='2025-2026 上学期', start_date='2025-07-01', end_date='2025-12-01')

    def run(self):
        self.init_semester()


def main():
    Initialize().run()


if __name__ == '__main__':
    main()
