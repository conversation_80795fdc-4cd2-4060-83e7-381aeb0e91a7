from django.urls import path
from . import views
from .views import DocumentListView, DocumentDetailView #, parse_documents, stop_parse_documents
from .views import ChunkListView, ChunkDetailView, retrieval_chunks
from .views import parse_or_stop_documents
from .views import (
    sync_datasets_intersection_api,
    sync_datasets_rag_to_pg_api,
    sync_datasets_comprehensive_api,
    knowledge_base_health_check_view,
    sync_chunks_api
)

urlpatterns = [
    path('datasets/', views.DatasetListCreateView.as_view()),            # GET/POST
    path('datasets/<str:dataset_id>/', views.DatasetDetailView.as_view()), # GET/PUT/DELETE
    path('datasets/<str:dataset_id>/documents/', DocumentListView.as_view()),  # GET/POST/DELETE
    path('datasets/<str:dataset_id>/documents/<str:document_id>/', DocumentDetailView.as_view()),  # GET/PUT
    path('datasets/<str:dataset_id>/parse-tasks/', parse_or_stop_documents, name='parse_or_stop_documents'),

    # Chunk管理（增、查、删）
    path(
        'datasets/<str:dataset_id>/documents/<str:document_id>/chunks/',
        ChunkListView.as_view(),
        name='chunk-list'
    ),
    # Chunk更新
    path(
        'datasets/<str:dataset_id>/documents/<str:document_id>/chunks/<str:chunk_id>/',
        ChunkDetailView.as_view(),
        name='chunk-detail'
    ),
    # Chunk检索
    path(
        'chunk-retrievals/',
        retrieval_chunks,
        name='retrieval-chunks'
    ),
    # 登录接口
    path('auth/login/', views.user_login, name='user_login'),

    # 新增：知识库数据同步接口
    path(
        'knowledge_base/sync/intersection/',
        sync_datasets_intersection_api,
        name='sync-datasets-intersection'
    ),
    path(
        'knowledge_base/sync/rag-to-pg/',
        sync_datasets_rag_to_pg_api,
        name='sync-datasets-rag-to-pg'
    ),
    path(
        'knowledge_base/sync/comprehensive/',
        sync_datasets_comprehensive_api,
        name='sync-datasets-comprehensive'
    ),
    path(
        'knowledge_base/sync/health_check/',
        knowledge_base_health_check_view,
        name='sync-datasets-comprehensive'
    ),

    # 知识库和课程绑定接口
    path('course-kb-link/', views.CourseKBLinkView.as_view(), name='course_kb_link'),  # POST绑定, DELETE解绑
    path('courses/<int:course_id>/knowledge-bases/', views.CourseKBListView.as_view(), name='course_kb_list'),  # GET查看课程绑定的知识库
    path('knowledge-bases/<str:kb_id>/courses/', views.KBCourseListView.as_view(), name='kb_course_list'),  # GET查看知识库绑定的课程

    #chunk同步接口
    path(
        'sync/sync_chunks/',
            sync_chunks_api,
            name='sync_chunks_api'
        ),
    # 权限管理
    # path('kb-access/grant/', views.grant_kb_access, name='grant-kb-access'),
    # path('kb-access/revoke/', views.revoke_kb_access, name='revoke-kb-access'),
    # path('users/<int:user_id>/accessible-kbs/', views.get_user_accessible_kbs, name='user-accessible-kbs'),
]