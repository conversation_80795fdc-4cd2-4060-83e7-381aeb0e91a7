from celery import shared_task
from django.core.exceptions import ObjectDoesNotExist
from django.conf import settings
from .models import GraphTask
from .prompt import graph_prompt
from .janusgraph_client import JanusGraphClient
from gremlin_python.process.traversal import T, Direction
from .utils import parse_llm_response, ensure_all_nodes_inserted_and_id_mapped, merge_nodes_and_edges
from langchain_openai import ChatOpenAI
from langchain_core.prompts import Chat<PERSON>romptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
import json
from datetime import datetime
import logging
import warnings

logger = logging.getLogger(__name__)


@shared_task(bind=True, default_retry_delay=300, max_retries=3)
def extract_knowledge_graph(self, task_id, text_chunks):
    """
    异步提取知识图谱任务
    """
    warnings.filterwarnings("ignore", module="langchain")

    try:
        # 获取任务对象并更新状态
        task = GraphTask.objects.get(id=task_id)
        task.status = 'processing'
        task.save()

        # 初始化JanusGraph客户端
        janusgraph_url = settings.JANUSGRAPH_URL
        # client = JanusGraphClient(janusgraph_url)

        with JanusGraphClient(janusgraph_url) as client:

            # LLM配置
            llm = ChatOpenAI(
                model=settings.LLM_NAME,
                openai_api_key=settings.LLM_API_KEY,
                openai_api_base=settings.LLM_BASE_URL_LESSON,
                max_tokens=4000,
                temperature=1,
                streaming=False,  # 非流式处理
            )

            # 加载提示模板和示例
            data_format = _load_data_format()
            data_format_example = _load_data_format_example()

            # 创建提示模板
            prompt = ChatPromptTemplate.from_messages([
                SystemMessagePromptTemplate.from_template(graph_prompt()),
                HumanMessagePromptTemplate.from_template("请从以下内容中抽取与主题相关的实体与关系：{chunk_text}")
            ])

            all_nodes = []
            all_edges = []

            # 处理文本块
            for idx, chunk in enumerate(text_chunks):
                logger.info(f"处理第 {idx + 1}/{len(text_chunks)} 块文本...")

                try:
                    # 格式化提示消息
                    messages = prompt.format_messages(
                        data_format=json.dumps(data_format, ensure_ascii=False, indent=2),
                        data_format_example=json.dumps(data_format_example, ensure_ascii=False, indent=2),
                        chunk_text=chunk
                    )

                    # 调用LLM
                    response = llm.invoke(messages)
                    response_text = response.content if hasattr(response, 'content') else str(response)

                    # 解析LLM响应
                    parsed = parse_llm_response(response_text)

                    if parsed["status"] == "success":
                        all_nodes.extend(parsed["data"]["nodes"])
                        all_edges.extend(parsed["data"]["edges"])
                        logger.debug(
                            f"第 {idx + 1} 块文本处理成功，提取节点: {len(parsed['data']['nodes'])}, 边: {len(parsed['data']['edges'])}")
                    else:
                        logger.warning(f"第 {idx + 1} 块文本解析失败: {parsed['message']}")
                        logger.debug(f"原始LLM响应: {response_text}")
                except Exception as e:
                    logger.error(f"处理第 {idx + 1} 块文本时出错: {str(e)}", exc_info=True)

            # 合并和去重节点与边
            unique_nodes, unique_edges = merge_nodes_and_edges(all_nodes, all_edges)
            logger.info(f"去重后: 节点={len(unique_nodes)}, 边={len(unique_edges)}")

            user_id = task.user_id if task.user else 666  # 默认用户ID

            # 批量存储节点
            stored_node_count = _batch_store_nodes(client, unique_nodes, user_id, task_id)
            logger.info(f"成功存储 {stored_node_count}/{len(unique_nodes)} 个节点")

            # 确保所有节点都已插入并映射ID
            name_to_id = ensure_all_nodes_inserted_and_id_mapped(client, unique_nodes, task_id)
            logger.info(f"映射了 {len(name_to_id)}/{len(unique_nodes)} 个节点ID")

            # 批量存储边
            stored_edges, skipped_edges = _batch_store_edges(client, unique_edges, name_to_id, user_id, task_id)
            logger.info(f"成功存储 {len(stored_edges)}/{len(unique_edges)} 条边，跳过 {len(skipped_edges)} 条边")

            # 构建结果
            result_data = {
                "nodes_processed": len(unique_nodes),
                "edges_processed": len(unique_edges),
                "edges_stored": len(stored_edges),
                "edges_skipped": len(skipped_edges),
                "skipped_details": skipped_edges,
                "timestamp": str(datetime.now())
            }

            # 更新任务状态为完成
            task.status = 'completed'
            task.result = json.dumps(result_data, ensure_ascii=False)
            task.node_count = len(unique_nodes)
            task.edge_count = len(unique_edges)
            task.save()

            logger.info(f"[TASK SUCCESS] 任务ID: {task_id}, 节点数量: {len(unique_nodes)}, 边数量: {len(unique_edges)}")
            return result_data

    except ObjectDoesNotExist:
        logger.error(f"任务ID {task_id} 不存在")
        raise

    except Exception as e:
        # 更新任务状态为失败
        try:
            task = GraphTask.objects.get(id=task_id)
            task.status = 'failed'
            task.error_message = str(e)
            task.save()
        except ObjectDoesNotExist:
            logger.error(f"更新失败状态时任务ID {task_id} 不存在")

        logger.error(f"知识图谱提取任务失败: {str(e)}", exc_info=True)
        self.retry(exc=e)


def _load_data_format():
    """加载数据格式配置"""
    try:
        with open("./apps/graph/generate_format/data_format.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载数据格式失败: {str(e)}")
        return {}


def _load_data_format_example():
    """加载数据格式示例"""
    try:
        with open("./apps/graph/generate_format/data_format_example.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载数据格式示例失败: {str(e)}")
        return {}


def _batch_store_nodes(client, nodes, user_id, task_id):
    """批量存储节点"""
    stored_count = 0

    for node in nodes:
        try:
            label = node['label']
            properties = node['properties']
            client.add_node(label, properties, user_id, task_id)
            stored_count += 1
        except Exception as e:
            logger.error(f"存储节点失败: {str(node)}, 错误: {str(e)}")

    return stored_count


def _batch_store_edges(client, edges, name_to_id, user_id, task_id):
    """批量存储边"""
    stored_edges = []
    skipped_edges = []

    for edge in edges:
        try:
            edge_label = edge["label"]
            from_entity = edge["from_entity"]
            to_entity = edge["to_entity"]
            properties = {
                **edge["properties"],
                "userID": user_id,
                "taskId": task_id
            }

            # 获取起点和终点的ID
            from_id = name_to_id.get(from_entity)
            to_id = name_to_id.get(to_entity)

            # 如果两个实体ID都存在，则创建边
            if from_id and to_id:
                from_vertex_id = from_id[T.id]
                to_vertex_id = to_id[T.id]

                if from_vertex_id and to_vertex_id:
                    # 存储边
                    edge_result = client.add_edge(
                        from_vertex_id,
                        to_vertex_id,
                        edge_label,
                        properties,
                    )
                    stored_edges.append({
                        "edge": edge,
                        "result": edge_result
                    })
                else:
                    skipped_edges.append({
                        "edge": edge,
                        "reason": f"无法提取顶点ID - from: {from_vertex_id}, to: {to_vertex_id}"
                    })
            else:
                skipped_edges.append({
                    "edge": edge,
                    "reason": f"找不到实体ID - from: {from_entity}, to: {to_entity}"
                })
        except Exception as e:
            skipped_edges.append({
                "edge": edge,
                "reason": f"存储边时出错: {str(e)}"
            })
            logger.error(f"存储边失败: {str(edge)}, 错误: {str(e)}")

    return stored_edges, skipped_edges