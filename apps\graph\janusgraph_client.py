import asyncio
import re
import sys
from typing import List, Dict, Any, Optional
from contextlib import contextmanager
from threading import local
import threading
import logging
from gremlin_python.structure.graph import Graph
from gremlin_python.driver.driver_remote_connection import DriverRemoteConnection
from gremlin_python.process.anonymous_traversal import traversal
from gremlin_python.process.graph_traversal import __
from gremlin_python.driver.serializer import GraphSONSerializersV3d0
from gremlin_python.driver.serializer import GraphSONMessageSerializer
from gremlin_python.process.traversal import T, Direction
from datetime import datetime
from django.conf import settings
from concurrent.futures import ThreadPoolExecutor, as_completed

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('janusgraph_client')

# 线程本地存储，为每个线程提供独立的JanusGraph连接
thread_local = local()

class JanusGraphClient:
    """JanusGraph数据库操作客户端，支持多线程环境"""

    def __init__(self, url=settings.JANUSGRAPH_URL):
        self.url = url
        self.graph = Graph()
        self.serializer = GraphSONMessageSerializer()
        self.node_labels = {
            'course': 'course',
            'chapter': 'chapter',
            'section': 'section',
            'knowledge_point': 'knowledge_point'
        }
        self.edge_labels = {
            'course_to_chapter': 'has_chapter',
            'chapter_to_section': 'has_section',
            'section_to_knowledge': 'has_knowledge',
            'chapter_to_knowledge': 'has_knowledge' # 章节到知识点的边类型
        }

    @property
    def g(self):
        """获取线程本地的JanusGraph遍历对象"""
        if not hasattr(thread_local, 'g') or self._is_connection_closed():
            self._init_connection()
        return thread_local.g

    def _init_connection(self):
        """初始化JanusGraph连接"""
        try:
            thread_local.connection = DriverRemoteConnection(
                self.url, 'g', message_serializer=self.serializer
            )
            thread_local.g = self.graph.traversal().withRemote(thread_local.connection)
            logger.info(f"线程 {threading.get_ident()} 成功连接到JanusGraph: {self.url}")
        except Exception as e:
            logger.error(f"线程 {threading.get_ident()} 连接JanusGraph失败: {e}")
            raise

    def _is_connection_closed(self):
        """检查连接是否已关闭"""
        if not hasattr(thread_local, 'connection'):
            return True
        try:
            # 尝试执行简单查询检测连接状态
            thread_local.g.V().limit(1).toList()
            return False
        except Exception as e:
            logger.warning(f"检测到连接异常: {e}")
            return True

    def close(self):
        """关闭JanusGraph连接"""
        if hasattr(thread_local, 'connection'):
            try:
                thread_local.connection.close()
                logger.info(f"线程 {threading.get_ident()} 已关闭JanusGraph连接")
            except Exception as e:
                logger.warning(f"关闭JanusGraph连接时出错: {str(e)}")
            finally:
                delattr(thread_local, 'connection')
                delattr(thread_local, 'g')

    def is_connected(self):
        """检查是否仍然连接到数据库"""
        return not self._is_connection_closed()

    def reconnect(self):
        """重新建立与 JanusGraph 的连接"""
        self.close()
        self._init_connection()

    @contextmanager
    def transaction(self):
        """提供事务上下文管理器"""
        try:
            yield
            self.g.tx().commit()
        except Exception as e:
            self.g.tx().rollback()
            raise

    def nodes_id(self, label, name, task_id):
        """通过标签、名称和任务ID查询节点"""
        try:
            label = str(label)
            name = str(name)
            task_id = int(task_id)
            logger.debug(f"查询节点: label={label}, name={name}, task_id={task_id}")

            nodess = self.g.V().has_label(label).has("name", name).has("task_id", task_id).element_map().toList()
            return nodess
        except Exception as e:
            logger.error(f"查询节点失败: {str(e)}")
            raise

    def add_node(self, label, properties, user_id, task_id):
        """添加节点"""
        try:
            traversal = self.g.add_v(label).property('user_id', user_id).property('task_id', task_id)
            for key, value in properties.items():
                traversal = traversal.property(key, value)
            vertex = traversal.next()
            logger.debug(f"添加节点成功: label={label}, properties={properties}")
            return vertex
        except Exception as e:
            logger.error(f"添加节点失败: {str(e)}")
            raise

    def get_node(self, vertex_id):
        """获取节点详细信息"""
        try:
            raw_data = self.g.V(vertex_id).element_map().toList()
            result = []
            for data in raw_data:
                node_info = {
                    'id': data[T.id],
                    'label': data[T.label],
                    'properties': {k: v for k, v in data.items() if k not in [T.id, T.label]}
                }
                result.append(node_info)
            return result
        except Exception as e:
            logger.error(f"获取节点信息失败: {str(e)}")
            raise

    def get_node_by_kv(self, properties):
        """通过键值对查询节点"""
        try:
            traversal = self.g.V()
            for key, value in properties.items():
                traversal = traversal.has(key, value)
            return traversal.value_map().toList()
        except Exception as e:
            logger.error(f"通过键值对查询节点失败: {str(e)}")
            raise

    def update_node(self, vertex_id, properties):
        """更新节点属性"""
        try:
            # 先删除所有现有属性
            self.g.V(vertex_id).properties().drop().iterate()

            # 设置新属性
            for key, value in properties.items():
                self.g.V(vertex_id).property(key, value).iterate()

            # 返回更新后的节点信息
            return self.g.V(vertex_id).value_map().next()
        except Exception as e:
            logger.error(f"更新节点失败: {str(e)}")
            raise

    def delete_node(self, vertex_id):
        """删除节点"""
        try:
            self.g.V(vertex_id).drop().iterate()
            logger.debug(f"删除节点成功: vertex_id={vertex_id}")
        except Exception as e:
            logger.error(f"删除节点失败: {str(e)}")
            raise

    def delete_all_nodes(self):
        """删除所有节点"""
        try:
            self.g.V().drop().iterate()
            logger.debug("删除所有节点成功")
        except Exception as e:
            logger.error(f"删除所有节点失败: {str(e)}")
            raise

    def add_edge(self, from_vertex_id, to_vertex_id, label, properties):
        """添加边"""
        try:
            # 构建带属性的边添加步骤
            g_step = self.g.V(from_vertex_id).addE(label).to(__.V(to_vertex_id))

            # 添加所有属性到步骤中
            for key, value in properties.items():
                g_step = g_step.property(key, value)

            # 执行步骤并获取边数据
            edge_data_list = g_step.elementMap().toList()

            if edge_data_list:
                logger.debug(f"添加边成功: from={from_vertex_id}, to={to_vertex_id}, label={label}")
                return edge_data_list[0]
            else:
                logger.warning(f"添加边失败: from={from_vertex_id}, to={to_vertex_id}, label={label}")
                return None
        except Exception as e:
            logger.error(f"添加边失败: {str(e)}")
            return None

    def get_edge(self, edge_id):
        """获取边详细信息"""
        try:
            edges = self.g.E(edge_id).elementMap().next()
            logger.debug(f"获取边信息成功: edge_id={edge_id}")

            edge_info = {
                'id': str(edges[T.id]),
                'label': edges[T.label],
                'source': edges[Direction.OUT][T.id],
                'target': edges[Direction.IN][T.id],
                'userId': edges.get('userID', "无用户ID"),
                'taskId': edges.get('taskId', "无任务ID")
            }
            return edge_info
        except Exception as e:
            logger.error(f"获取边信息失败: {str(e)}")
            raise

    def delete_nodes(self):
        result = self.g.V().drop().iterate()
        # return result

    def update_edge(self, edge_id, properties):
        for key, value in properties.items():
            self.g.E(edge_id).property(key, value).next()
        return self.g.E(edge_id).value_map().next()

    def update_edge_label(self, edge_id, new_label):
        # 获取旧边的信息
        old_edge = self.g.E(edge_id).element_map().next()

        # 获取旧边的源节点和目标节点
        out_vertex_id = old_edge[Direction.OUT][T.id]
        in_vertex_id = old_edge[Direction.IN][T.id]

        # 获取旧边的属性
        properties = {k: v[0] if isinstance(v, list) else v for k, v in old_edge.items() if
                      k not in [T.id, T.label, Direction.OUT, Direction.IN]}
        # 删除旧边
        self.g.E(edge_id).drop().iterate()

        # 创建新边
        new_edge = self.g.V(out_vertex_id).addE(new_label).to(__.V(in_vertex_id)).next()

        datas = self.edge_id_dict(new_edge)

        # 添加旧边的属性到新边
        for key, value in properties.items():
            self.g.E(datas).property(key, value).next()
        # data =  self.g.E(datas).elementMap().toList()
        # 返回新边的信息
        return

    def delete_edge(self, edge_id):
        """删除边"""
        try:
            self.g.E(edge_id).drop().iterate()
            logger.debug(f"删除边成功: edge_id={edge_id}")
        except Exception as e:
            logger.error(f"删除边失败: {str(e)}")
            raise

    def get_all_nodes(self):
        """获取所有节点"""
        try:
            nodes = self.g.V().element_map().toList()
            result = []
            for node in nodes:
                node_info = {
                    'id': node[T.id],
                    'label': node[T.label],
                    'properties': {k: v for k, v in node.items() if k not in [T.id, T.label]}
                }
                result.append(node_info)
            return result
        except Exception as e:
            logger.error(f"获取所有节点失败: {str(e)}")
            raise

    def by_id_get_all_nodes(self, task_id):
        """根据任务ID获取所有节点"""
        try:
            task = int(task_id)
            nodes = self.g.V().has('task_id', task).element_map().toList()
            results = []
            for node in nodes:
                node_info = {
                    'id': node[T.id],
                    'label': node[T.label],
                    'properties': {k: v for k, v in node.items() if k not in [T.id, T.label]}
                }
                results.append(node_info)
            return results
        except Exception as e:
            logger.error(f"根据任务ID获取节点失败: {str(e)}")
            raise

    def get_all_edges(self):
        """获取所有边"""
        try:
            edges = self.g.E().element_map().to_list()
            result = []
            for edge in edges:
                edge_info = {
                    'id': edge[T.id],
                    'label': edge[T.label],
                    'properties': {k: v for k, v in edge.items() if k not in [T.id, T.label]}
                }
                result.append(edge_info)
            return result
        except Exception as e:
            logger.error(f"获取所有边失败: {str(e)}")
            raise

    def by_id_get_all_edges(self, task_id):
        """根据任务ID获取所有边"""
        try:
            task = int(task_id)
            edges = self.g.E().has('taskId', task).element_map().to_list()
            result = []
            for edge in edges:
                edge_info = {
                    'id': str(edge[T.id]),
                    'label': edge[T.label],
                    'source': edge[Direction.OUT][T.id],
                    'target': edge[Direction.IN][T.id],
                    'userId': edge['userID'],
                    'taskId': edge['taskId']
                }
                result.append(edge_info)
            return result
        except Exception as e:
            logger.error(f"根据任务ID获取边失败: {str(e)}")
            raise

    def get_all_edges_with_details(self):
        """获取所有边的详细信息"""
        try:
            edges = self.g.E().elementMap().toList()
            result = []
            for edge in edges:
                edge_info = {
                    'id': str(edge[T.id]),
                    'label': edge[T.label],
                    'source': edge[Direction.OUT][T.id],
                    'target': edge[Direction.IN][T.id],
                }
                result.append(edge_info)
            return result
        except Exception as e:
            logger.error(f"获取所有边详细信息失败: {str(e)}")
            raise

    def get_all_data(self):
        """获取所有节点和边数据"""
        try:
            result = {}
            nodes = self.get_all_nodes()
            edges = self.get_all_edges_with_details()
            result['nodes'] = nodes
            result['edges'] = edges
            return result
        except Exception as e:
            logger.error(f"获取所有数据失败: {str(e)}")
            raise

    def by_id_get_all_data(self, task_id):
        """根据任务ID获取所有节点和边数据"""
        try:
            result = {}
            nodes = self.by_id_get_all_nodes(task_id)
            edges = self.by_id_get_all_edges(task_id)
            result['nodes'] = nodes
            result['edges'] = edges
            return result
        except Exception as e:
            logger.error(f"根据任务ID获取所有数据失败: {str(e)}")
            raise

    def classify_nodes(self, label):
        """根据标签分类节点"""
        try:
            data = self.g.V().hasLabel(label).elementMap().toList()
            results = []
            for node in data:
                node_info = {
                    'id': node[T.id],
                    'label': node[T.label],
                    'properties': {k: v for k, v in node.items() if k not in [T.id, T.label]}
                }
                results.append(node_info)
            return results
        except Exception as e:
            logger.error(f"根据标签分类节点失败: {str(e)}")
            raise

    def classify_edges(self, label):
        """根据标签分类边"""
        try:
            data = self.g.E().where(__.inV().hasLabel(label)).element_map().toList()
            result = []
            for edge in data:
                edge_info = {
                    'id': str(edge[T.id]),
                    'label': edge[T.label],
                    'source': edge[Direction.OUT][T.id],
                    'target': edge[Direction.IN][T.id],
                    'in_label': edge[Direction.IN][T.label],
                    'userId': edge['userID']
                }
                result.append(edge_info)
            return result
        except Exception as e:
            logger.error(f"根据标签分类边失败: {str(e)}")
            raise

    def classify_all_data(self, label):
        """根据标签获取所有节点和边数据"""
        try:
            result = {}
            nodes = self.classify_nodes(label)
            edges = self.classify_edges(label)
            result['nodes'] = nodes
            result['edges'] = edges
            return result
        except Exception as e:
            logger.error(f"根据标签获取所有数据失败: {str(e)}")
            raise

    def delete_node_and_edge(self, task_id):
        """根据任务ID删除节点和边"""
        try:
            self.g.V().has('task_id', task_id).drop().iterate()
            logger.debug(f"根据任务ID删除节点和边成功: task_id={task_id}")
        except Exception as e:
            logger.error(f"根据任务ID删除节点和边失败: {str(e)}")
            raise

    def _vertex_to_dict(self, vertex):
        """将顶点转换为字典格式"""
        if isinstance(vertex, dict):
            return {
                'id': str(vertex[T.id]),
                'label': vertex[T.label],
                'properties': {k: v for k, v in vertex.items() if k not in [T.id, T.label]}
            }
        return {}

    def _edge_to_dict(self, edge: dict):
        """将边转换为字典格式"""
        raw_id = edge.get(T.id)
        edge_id = None

        if isinstance(raw_id, dict) and '@value' in raw_id and 'relationId' in raw_id['@value']:
            edge_id = raw_id['@value']['relationId']
        else:
            edge_id = str(raw_id)

        # 取 from 和 to 的节点id
        from_vertex = edge.get(Direction.OUT)
        to_vertex = edge.get(Direction.IN)

        # 只取节点id
        from_id = from_vertex.get(T.id) if from_vertex else None
        to_id = to_vertex.get(T.id) if to_vertex else None

        return {
            'id': edge_id,
            'label': edge.get(T.label),
            'from': from_id,
            'to': to_id,
            'userId': edge.get('userID')
        }

    def edge_id_dict(self, edge_str):
        """从边字符串中提取边ID"""
        try:
            edge_str = str(edge_str)

            # 用正则提取 relationId
            match = re.search(r"'relationId': '([a-zA-Z0-9\-]+)'", edge_str)
            if match:
                edge_id = match.group(1)
                return edge_id
            else:
                raise ValueError("无法从字符串中提取 relationId")
        except Exception as e:
            logger.error(f"提取边ID失败: {str(e)}")
            return None

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
        return False

    # 以下是同步工具相关的方法
    def _preprocess_data(self, data):
        """
        预处理数据，转换不支持的类型
        """
        if isinstance(data, dict):
            return {k: self._preprocess_data(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._preprocess_data(item) for item in data]
        elif data is None:
            return ""  # 将None转换为空字符串
        elif isinstance(data, datetime):
            return data.isoformat()  # 转换日期时间为字符串
        else:
            return data

    def clear_existing_data(self, course_id, user_id):
        """
        清除JanusGraph中现有的课程结构数据
        """
        try:
            logger.info(f"开始清除课程 {course_id} 用户 {user_id} 的JanusGraph数据")
            self.g.V().has('course_id', course_id).has('user_id', user_id).drop().iterate()
            logger.info(f"已成功清除课程 {course_id} 用户 {user_id} 的JanusGraph数据")
        except Exception as e:
            logger.error(f"清除数据时出错: {e}")
            raise

    def sync_course_structure(self, course_data, user_id, course_id):
        """
        同步课程四级结构到JanusGraph
        """
        try:
            # 开始同步前清除现有数据
            # self.clear_existing_data(course_id, user_id)

            # 预处理数据
            preprocessed_data = self._preprocess_data(course_data)

            for course in preprocessed_data:
                # 创建课程节点
                course_node = self._create_course_node(course, user_id, course_id)

                # 处理章节
                for chapter in course.get('chapters', []):
                    chapter_node = self._create_chapter_node(chapter, user_id, course_id)
                    # 创建课程到章节的边
                    self.g.V(course_node).addE(self.edge_labels['course_to_chapter']) \
                        .property('courseID', course_id) \
                        .property('userID', user_id) \
                        .to(chapter_node).iterate()
                    # 处理章节直接关联的知识点
                    self._process_chapter_knowledge_points(chapter_node, chapter, user_id, course_id)

                    # 处理子章节（节）
                    for section in chapter.get('children', []):
                        section_node = self._create_section_node(section, user_id, course_id)
                        # 创建章节到节的边
                        self.g.V(chapter_node).addE(self.edge_labels['chapter_to_section']) \
                            .property('courseID', course_id) \
                            .property('userID', user_id) \
                            .to(section_node).iterate()

                        # 处理知识点
                        for knowledge_point in section.get('knowledge_points', []):
                            knowledge_node = self._create_knowledge_point_node(knowledge_point, user_id, course_id)
                            # 创建节到知识点的边
                            self.g.V(section_node).addE(self.edge_labels['section_to_knowledge']) \
                                .property('courseID', course_id) \
                                .property('userID', user_id) \
                                .to(knowledge_node).iterate()

            logger.info(f"课程 {course_id} 用户 {user_id} 结构同步完成")
        except Exception as e:
            logger.error(f"同步过程中出错: {e}")
            raise

    def _process_chapter_knowledge_points(self, chapter_node, chapter, user_id, course_id):
        """处理章节直接关联的知识点"""
        for knowledge_point in chapter.get('knowledge_points', []):
            knowledge_node = self._create_knowledge_point_node(knowledge_point, user_id, course_id)
            # 创建章节到知识点的边
            self.g.V(chapter_node).addE(self.edge_labels['chapter_to_knowledge']) \
                .property('courseID', course_id) \
                .property('userID', user_id) \
                .to(knowledge_node).iterate()

    def _create_course_node(self, course, user_id, course_id):
        """
        创建课程节点
        """
        return self.g.addV(self.node_labels['course']) \
            .property('id', course['id']) \
            .property('name', course['title']) \
            .property('description', course.get('description', '')) \
            .property('user_id', user_id) \
            .property('course_id', course_id) \
            .next()

    def _create_chapter_node(self, chapter, user_id, course_id):
        """
        创建章节节点
        """
        return self.g.addV(self.node_labels['chapter']) \
            .property('chapter_id', chapter['id']) \
            .property('name', chapter['title']) \
            .property('content', chapter.get('content', '')) \
            .property('order', chapter.get('order', 0)) \
            .property('parent', chapter.get('parent', '')) \
            .property('user_id', user_id) \
            .property('course_id', course_id) \
            .next()

    def _create_section_node(self, section, user_id, course_id):
        """
        创建节节点
        """
        return self.g.addV(self.node_labels['section']) \
            .property('chapter_id', section['id']) \
            .property('name', section['title']) \
            .property('content', section.get('content', '')) \
            .property('order', section.get('order', 0)) \
            .property('parent', section.get('parent', '')) \
            .property('user_id', user_id) \
            .property('course_id', course_id) \
            .next()

    def _create_knowledge_point_node(self, knowledge_point, user_id, course_id):
        """
        创建知识点节点
        """
        return self.g.addV(self.node_labels['knowledge_point']) \
            .property('knowledge_id', knowledge_point['id']) \
            .property('name', knowledge_point['name']) \
            .property('description', knowledge_point.get('description', '')) \
            .property('knowledge_type', knowledge_point.get('knowledge_type', '')) \
            .property('importance', knowledge_point.get('importance', 0)) \
            .property('user_id', user_id) \
            .property('course_id', course_id) \
            .next()

    def get_course_structure(self, course_id=None):
        """
        从JanusGraph获取课程结构
        """
        try:
            if course_id:
                # 获取特定课程的结构
                course = self.g.V().has(self.node_labels['course'], 'id', course_id).next()
                return self._build_course_tree(course)
            else:
                # 获取所有课程的结构
                courses = self.g.V().hasLabel(self.node_labels['course']).toList()
                return [self._build_course_tree(course) for course in courses]
        except Exception as e:
            logger.error(f"获取课程结构时出错: {e}")
            raise

    def _build_course_tree(self, course_vertex):
        """
        递归构建课程树结构
        """
        course = self.g.V(course_vertex).valueMap().next()
        course = {k: v[0] if isinstance(v, list) and len(v) == 1 else v for k, v in course.items()}

        # 获取章节
        chapters = self.g.V(course_vertex).out(self.edge_labels['course_to_chapter']).toList()
        course['chapters'] = []

        for chapter_vertex in chapters:
            chapter = self.g.V(chapter_vertex).valueMap().next()
            chapter = {k: v[0] if isinstance(v, list) and len(v) == 1 else v for k, v in chapter.items()}

            # 获取章节直接关联的知识点
            chapter_knowledge_points = self.g.V(chapter_vertex).out(self.edge_labels['chapter_to_knowledge']).toList()
            chapter['knowledge_points'] = [
                {k: v[0] if isinstance(v, list) and len(v) == 1 else v
                 for k, v in self.g.V(kp).valueMap().next().items()}
                for kp in chapter_knowledge_points
            ]

            # 获取节
            sections = self.g.V(chapter_vertex).out(self.edge_labels['chapter_to_section']).toList()
            chapter['sections'] = []

            for section_vertex in sections:
                section = self.g.V(section_vertex).valueMap().next()
                section = {k: v[0] if isinstance(v, list) and len(v) == 1 else v for k, v in section.items()}

                # 获取知识点
                knowledge_points = self.g.V(section_vertex).out(self.edge_labels['section_to_knowledge']).toList()
                section['knowledge_points'] = [
                    {k: v[0] if isinstance(v, list) and len(v) == 1 else v
                     for k, v in self.g.V(kp).valueMap().next().items()}
                    for kp in knowledge_points
                ]

                chapter['sections'].append(section)

            course['chapters'].append(chapter)

        return course

    def get_node_details(self, node_type, node_id):
        """
        根据节点类型和ID获取节点详情
        """
        try:
            label = self.node_labels.get(node_type)
            if not label:
                raise ValueError(f"未知的节点类型: {node_type}")

            node = self.g.V().has(label, 'id', node_id).valueMap().next()
            return {k: v[0] if isinstance(v, list) and len(v) == 1 else v for k, v in node.items()}
        except Exception as e:
            logger.error(f"获取节点详情时出错: {e}")
            raise

    def by_course_id_get_all_nodes(self, course_id, user_id):
        """
        根据课程ID和用户ID获取所有节点
        """
        try:
            course = int(course_id)
            user = int(user_id)
            nodes = self.g.V().has('course_id', course).has('user_id', user).element_map().toList()
            results = []
            for node in nodes:
                node_info = {
                    'id': node[T.id],
                    'label': node[T.label],
                    'properties': {k: v for k, v in node.items() if k not in [T.id, T.label]}
                }
                results.append(node_info)
            return results
        except Exception as e:
            logger.error(f"根据任务ID获取节点失败: {str(e)}")
            raise

    def by_course_id_get_all_edges(self, course_id, user_id):
        """
        根据课程ID和用户ID获取所有边
        """
        try:
            course = int(course_id)
            user = int(user_id)
            edges = self.g.E().has('courseID', course).has('userID', user).element_map().to_list()
            result = []
            for edge in edges:
                edge_info = {
                    'id': str(edge[T.id]),
                    'label': edge[T.label],
                    'source': edge[Direction.OUT][T.id],
                    'target': edge[Direction.IN][T.id],
                    'userID': edge['userID'],
                    'courseID': edge['courseID']
                }
                result.append(edge_info)
            return result
        except Exception as e:
            logger.error(f"根据任务ID获取边失败: {str(e)}")
            raise

    def by_course_id_get_all_data(self, course_id, user_id):
        """
        根据课程ID和用户ID获取所有节点和边数据
        """
        try:
            result = {}
            nodes = self.by_course_id_get_all_nodes(course_id, user_id)
            edges = self.by_course_id_get_all_edges(course_id, user_id)
            result['nodes'] = nodes
            result['edges'] = edges
            return result
        except Exception as e:
            logger.error(f"根据任务ID获取所有数据失败: {str(e)}")
            raise

    def by_chapter_id_get_nodes(self, course_id, user_id):
        """
        是否创建课程知识图谱
        """
        try:
            course = int(course_id)
            user = int(user_id)
            course_nodes = self.g.V() \
                .hasLabel(self.node_labels['course']) \
                .has('course_id', course) \
                .has('user_id', user) \
                .toList()
            return len(course_nodes) > 0
        except Exception as e:
            logger.error(f"根据章节ID和课程id查询失败: {str(e)}")
            raise

    def add_chapter(self, course_id, user_id, chapter_data):
        """
        添加章节或节
        如果 parent 为空，则添加章；如果 parent 不为空，则添加节
        """
        try:
            course_id = int(course_id)
            user_id = int(user_id)
            parent_id = chapter_data.get('parent')

            # 查找课程节点
            course_nodes = self.g.V().has(self.node_labels['course'], 'course_id', course_id) \
                .has('user_id', user_id).toList()
            if not course_nodes:
                raise ValueError(f"未找到课程: course_id={course_id}, user_id={user_id}")
            course_node = course_nodes[0]

            if parent_id:
                # 添加节（子章节）
                parent_id = int(parent_id)

                # 查找父章节节点
                parent_nodes = self.g.V().has('chapter_id', parent_id) \
                    .has('course_id', course_id) \
                    .has('user_id', user_id).toList()
                if not parent_nodes:
                    raise ValueError(f"未找到父章节: parent_id={parent_id}, course_id={course_id}, user_id={user_id}")
                parent_node = parent_nodes[0]

                # 创建节节点
                section_node = self._create_section_node(chapter_data, user_id, course_id)

                # 创建章节到节的边
                self.g.V(parent_node).addE(self.edge_labels['chapter_to_section']) \
                    .property('courseID', course_id) \
                    .property('userID', user_id) \
                    .to(section_node).iterate()

                logger.info(f"成功在课程 {course_id} 下的章节 {parent_id} 添加节: {chapter_data['title']}")
                return self.get_node(section_node.id)
            else:
                # 添加章
                chapter_node = self._create_chapter_node(chapter_data, user_id, course_id)

                # 创建课程到章节的边
                self.g.V(course_node).addE(self.edge_labels['course_to_chapter']) \
                    .property('courseID', course_id) \
                    .property('userID', user_id) \
                    .to(chapter_node).iterate()

                logger.info(f"成功在课程 {course_id} 下添加章节: {chapter_data['title']}")
                return self.get_node(chapter_node.id)

        except Exception as e:
            logger.error(f"添加章节失败: {str(e)}")
            raise

    def add_knowledge_point_to_chapter(self, chapter_id, course_id, user_id, knowledge_data):
        """
        在特定章节下添加知识点
        """
        try:
            chapter_id = int(chapter_id)
            course_id = int(course_id)
            user_id = int(user_id)

            # 查找目标章节节点
            chapter_nodes = self.g.V().has('chapter_id', chapter_id) \
                .has('course_id', course_id) \
                .has('user_id', user_id) \
                .toList()

            if not chapter_nodes:
                raise ValueError(f"未找到章节: chapter_id={chapter_id}, course_id={course_id}, user_id={user_id}")

            chapter_node = chapter_nodes[0]

            # 创建知识点节点
            knowledge_node = self._create_knowledge_point_node(knowledge_data, user_id, course_id)

            # 创建章节到知识点的边
            self.g.V(chapter_node).addE(self.edge_labels['chapter_to_knowledge']) \
                .property('courseID', course_id) \
                .property('userID', user_id) \
                .to(knowledge_node).iterate()

            logger.info(f"成功在章节 {chapter_id} 下添加知识点 {knowledge_data['name']}")

            # 返回创建的知识点信息
            return self.get_node(knowledge_node.id)

        except Exception as e:
            logger.error(f"添加知识点到章节失败: {str(e)}")
            raise

    def update_node_from_properties(self, properties, node_type, user_id, course_id):
        """
        通过 properties 字典中的 id 自动识别节点类型并更新内容
        user_id: 用户ID
        course_id: 课程ID
        """
        try:
            if 'id' not in properties:
                raise ValueError("properties 字典必须包含 id 字段")

            node_type = node_type
            id_value = properties['id']

            label = self.node_labels.get(node_type)
            if not label:
                raise ValueError(f"未知的节点类型: {node_type}")

            # 根据节点类型确定ID属性名称
            id_property_mapping = {
                'course': 'id',
                'chapter': 'chapter_id',
                'section': 'chapter_id',
                'knowledge_point': 'knowledge_id'
            }

            id_property = id_property_mapping.get(node_type)
            if not id_property:
                raise ValueError(f"无法确定节点类型 {node_type} 的ID属性名称")

            traversal = self.g.V().has(label, id_property, id_value) \
                .has('user_id', int(user_id)) \
                .has('course_id', int(course_id))

            nodes = traversal.toList()

            if not nodes:
                raise ValueError(
                    f"未找到节点: type={node_type}, {id_property}={id_value}, user_id={user_id}, course_id={course_id}")
            print('获取到的数据', nodes)
            # return nodes

            # 获取底层节点ID
            internal_id = nodes[0].id

            # allowed_props = {'content', 'name'}
            # update_props = {k: v for k, v in properties.items()}
            update_props = {k: v for k, v in properties.items() if k != 'id'}
            if 'title' in update_props:
                update_props['name'] = update_props.pop('title')
            update_props[id_property] = id_value
            print('获取的修改数据', update_props)
            # return internal_id

            # 如果没有可更新的属性，直接返回
            if not update_props:
                logger.info(f"没有需要更新的属性: {id_property}={id_value}")
                return self.get_node(internal_id)

            # with self.transaction():
            for key, value in update_props.items():
                self.g.V(internal_id).property(key, value).next()

            # return internal_id

            logger.info(f"成功更新{node_type}节点: {id_property}={id_value}, 更新属性={list(update_props.keys())}")

            return self.get_node(internal_id)

        except Exception as e:
            logger.error(f"更新节点内容失败: {str(e)}")
            raise


def setup_windows_event_loop():
    """设置Windows系统的事件循环"""
    if hasattr(asyncio, 'WindowsSelectorEventLoopPolicy'):
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# 限制最大工作线程数，防止资源耗尽
MAX_WORKERS = getattr(settings, 'JANUSGRAPH_SYNC_MAX_WORKERS', 5)
sync_executor = ThreadPoolExecutor(max_workers=MAX_WORKERS)


def run_janusgraph_sync_in_thread(course_data, user_id, course_id):
    """在单独线程中运行JanusGraph同步"""
    setup_windows_event_loop()

    def sync_task():
        try:
            sync_tool = JanusGraphClient()
            sync_tool.sync_course_structure(course_data, user_id, course_id)
            return "同步完成"
        except Exception as e:
            logger.error(f"同步过程中出错: {e}")
            return f"同步失败: {str(e)}"

    future = sync_executor.submit(sync_task)
    return future.result()