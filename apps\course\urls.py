from django.urls import path, include
from rest_framework.routers import DefaultRouter

from .views import *

router = DefaultRouter()

router.register('course', CourseViewSet, basename='course')  # 课程
router.register('semester', SemesterViewSet, basename='semester')  # 学期
router.register('course_teacher', CourseTeacherViewSet, basename='course_teacher')  # 课程教师
router.register('class', ClassViewSet, basename='class')  # 班级
router.register('course_semester', CourseSemesterViewSet, basename='course_semester')  # 课程学期
router.register('class_member', ClassMemberViewSet, basename='class_member')  # 班级学生
router.register('chapter', ChapterViewSet, basename='chapter')  # 课程章节
router.register('knowledge_point', KnowledgePointViewSet, basename='knowledge_point')  # 知识点添加
router.register('course_statistical', CourseStatisticalViewSet, basename='course_statistical')  # 知识点添加


urlpatterns = [
    path('', include(router.urls)),
]
