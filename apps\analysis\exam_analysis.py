"""
考试/作业 AI 学情分析服务
"""
import json
import re

from django.conf import settings
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END, START
from typing import Any, Dict, List, TypedDict
from .models import *

class AnalysisState(TypedDict):
    """考试/作业学情分析流程状态结构"""
    user_name: str  # 学生姓名
    user_number: str  # 学生学号
    exam_name: str  # 考试/作业名称
    is_exam: bool  # 考试/作业类型
    analysis_score: float  # 考试/作业成绩
    analysis_exam: str  # 考试/作业学情分析结果
    abstract_exam: str  # 考试/作业学情分析摘要


class AIExamAnalysisService:
    """考试/作业AI学情分析服务"""

    _instance = None

    def __new__(cls):
        """
        单例模式实现
        """
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.llm = ChatOpenAI(
                model=settings.LLM_NAME,
                openai_api_key=settings.LLM_API_KEY,
                openai_api_base=settings.LLM_BASE_URL_LESSON
            )

        return cls._instance

    
    # -------------------- 核心 AI 服务方法 --------------------
    def analyze_exam(self, state):
        """
        考试/作业学情分析-主入口
        """
        #  考试/作业学情分析
        initial_state = AnalysisState(
            user_name=state.get('user_name', ''),
            user_number=state.get('user_number', ''),
            exam_name=state.get('exam_name', ''),
            is_exam=state.get('is_exam', False),
            analysis_score=state.get('analysis_score', 0),
            analysis_exam='',
            abstract_exam=''
        )

        workflow = self.build_scoring_flow()
        final_state = workflow.invoke(initial_state)

        return {
            'analysis_exam': final_state['analysis_exam'],
            'abstract_exam': final_state['abstract_exam'],
        }


    def build_scoring_flow(self):
        """
        构建学情分析流程图
        """
        builder = StateGraph(AnalysisState)

        # 添加节点
        builder.add_node("ExamAnalysis", self._exam_analysis)
        
        # 设置流程路径
        builder.add_edge(START, "ExamAnalysis")
        builder.add_edge("ExamAnalysis", END)

        return builder.compile()
    
    # -------------------- 流程节点 --------------------
    def _exam_analysis(self, state):
        """考试/作业学情分析流程节点"""
        if state['is_exam'] is True:
            prompt = f"""
            /no_think
            你是一个专业的考试学情分析助手，根据学生的考试情况，分析学生的考试情况和成绩。
            请根据以下信息，分析学生的考试情况和成绩：
            1. 学生姓名：{state['user_name']}
            2. 学生学号：{state['user_number']}
            3. 考试名称：{state['exam_name']}
            4. 考试成绩：{state['analysis_score']}
            请根据以上信息，分析学生的考试情况和成绩。
            请返回JSON格式的分析结果
            {{
            "analysis_exam": "考试分析结果",
            "abstract_exam": "考试分析摘要，不超过30个字"
            }}
            """
            try:
                response = self._call_ai(prompt)
                data = self._extract_json(response)

                return {
                    **state,
                    "analysis_exam": data["analysis_exam"],
                    "abstract_exam": data["abstract_exam"]
                }
            
            except Exception as e:
                return  {
                    **state,
                    "analysis_exam": "考试分析结果：继续加油",
                    "abstract_exam": "考试分析摘要：继续加油"
                }
            
        else:
            prompt = f"""
            /no_think
            你是一个专业的作业学情分析助手，根据学生的作业情况，分析学生的作业情况和成绩。
            请根据以下信息，分析学生的作业情况和成绩：
            1. 学生姓名：{state['user_name']}
            2. 学生学号：{state['user_number']}
            3. 作业名称：{state['exam_name']}
            4. 作业成绩：{state['analysis_score']}
            请根据以上信息，分析学生的作业情况和成绩。
            请返回JSON格式的分析结果
            {{
            "analysis_exam": "作业分析结果",
            "abstract_exam": "作业分析摘要， 不超过30个字"
            }}
            """
            try:
                response = self._call_ai(prompt)
                data = self._extract_json(response)

                return {
                    **state,
                    "analysis_exam": data["analysis_exam"],
                    "abstract_exam": data["abstract_exam"]
                }
            
            except Exception as e:
                return  {
                    **state,
                    "analysis_exam": "作业分析结果：继续加油",
                    "abstract_exam": "作业分析摘要：继续加油"
                }

    def _call_ai(self, prompt):
        """调用AI的统一接口"""
        try:
            response = self.llm.invoke(prompt)
            return response.content
        except Exception as e:
            return json.dumps({
                "error": f"调用AI接口失败: {str(e)}",
            })

    def _extract_json(self, text):
        """从文本提取JSON内容"""
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            match = re.search(r"\{.*\}", text, re.DOTALL)
            if match:
                return json.loads(match.group())
            raise ValueError("无法解析JSON响应")
        

        
        
