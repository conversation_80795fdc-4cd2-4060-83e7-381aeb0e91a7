def abstract_prompt():
    """
    摘要提取提示词
    """
    prompt = """
                你的任务是为给定的文章提取内容摘要，并以JSON格式输出。
                在提取摘要时，请遵循以下规则：
                 -摘要应准确概括文章的核心内容，保留关键信息。
                 -尽量简洁明了，避免包含过多细节和冗余信息。
                 -保持摘要的连贯性和逻辑性。
                 -摘要输出的格式为：
                  {data_format}
                 -示例输出:
                  {data_format_example}
                要求摘要不超过{count}字。
            """
    return prompt


def graph_prompt():
    prompt = """
                -Goal-
                Given a text document, identify all entities and the relationships between them. For each entity, automatically determine its type (label) based on context, and represent all output in structured JSON format.

                -Steps-
                1. Identify all entities in the text. An entity can be a person, a place, an organization, an event, an object, etc.
                2. For each entity, extract:
                   - label: A contextual category, such as "Person", "City", "Organization", "Event", etc.
                   - name: The name or identifier of the entity.
                   - content: A short description of its role or nature.

                3. Identify relationships between these entities:
                   - label: Type of relationship (e.g., "同事", "位于", "参与", "父子").
                   - from_entity: Name of the source entity.
                   - to_entity: Name of the target entity.
                   - properties: Optional additional information (can be empty). 

                4. Format the result as JSON:
                {data_format}

                -Example-
                Text:
                John works with Mary at ABC Company in New York.

                Output:
                {data_format_example}
                """
    return prompt

def teach_plan_prompt_V1():

    prompt = """
            /no_think
            你是一个教案生成专家，请严格根据以下教案模板结构:'template_struct'和用户问题，生成完整的教案内容。
            'template_struct'中可能包含普通文本和表格
            普通文本需判定其含义，并根据用户问题判断是否需要进行填充，不要改变其原来的内容
            表格需根据用户问题进行填充，不要改变原来的内容，当无法进行填充时，不要过度发挥想象，比如‘班级’,就无需填充。
            严格按照给定的'template_struct'的格式进行输出。
            对表格进行填充时必须放在表格中，不要放在表格外面
            严格输出markdown格式，标题、段落、子标题等使用markdown的语法，要保证格式正确。
            表格使用markdown的表格语法，文本使用markdown的文本语法，要保证格式正确。
            不要带有```markdown```标签
            不要生成与'template_struct'和用户问题不相关的内容
            不要出现<think> </think>标签
            'template_struct':{template_struct}
            例：
            'template_struct'为:
               # 临沂科技职业学院
               ## 《》课程教案
               ### （理论课教案）
                |  |  |
                | --- | --- |
                | 课程名称： |  |
                | 适用专业： |  |
                | 课程性质： |  |
                | 授课年级： |  |
                | 专 业： |  |
                | 授课学期： |  |
                | 课程所属学院： |  |
                | 教师姓名： |  |
                年 月 日
                教案首页
                |  |  |  |  |
                | --- | --- | --- | --- |
                | 课程名称 |  | 授课日期 |  |
                | 课程名称 |  | 授课班级 |  |
                | 本节课题 |  | 授课方式 |  |
                | 教学参考及教具 |  |  |  |
                | 教学目标及基本要求 |  |  |  |
                | 教学重点 教学难点 |  |  |  |
                | 教学小结 |  |  |  |
                | 作业及要求 |  |  |  |
                | 教后反思 |  |  |  |
                教案用纸
                |  |  |
                | --- | --- |
                | 教学内容、方法和过程 | 附记 |
                |  |  |
            回复：
                # 临沂科技职业学院
    
                ##《机械原理》课程教案
    
                ###（理论课教案）
    
                |  |  |
                | --- | --- |
                | 课程名称： | 机械原理 |
                | 适用专业： | 机械设计与制造、自动化等相关专业 |
                | 课程性质： | 专业基础课 |
                | 授课年级： | 二年级 |
                | 专 业： | 机械工程 |
                | 授课学期： | 第三学期 |
                | 课程所属学院： | 机电工程学院 |
                | 教师姓名： | 自行补充 |
    
                 年  月  日
    
                教案首页
    
                |  |  |  |  |
                | --- | --- | --- | --- |
                | 课程名称 | 机械原理 | 授课日期 | 自行补充 |
                | 课程名称 | 机械原理 | 授课班级 | 自行补充 |
                | 本节课题 | 机构的组成和结构分析 | 授课方式 | 讲授+多媒体演示 |
                | 教学参考及教具 | 教材《机械原理》，投影仪，PPT课件，模型演示 |  |  |
                | 教学目标及基本要求 | 1. 理解机构的基本组成；<br>2. 掌握运动副及其分类；<br>3. 能够对简单机构进行结构分析。 |  |  |
                | 教学重点 教学难点 | 教学重点：机构的组成要素和运动副分类。<br>教学难点：机构自由度计算与结构分析。 |  |  |
                | 教学小结 | 通过实例讲解帮助学生理解抽象概念，并结合练习加深印象。 |  |  |
                | 作业及要求 | 完成教材第2章习题1-5题，下节课前提交。 |  |  |
                | 教后反思 | 学生普遍对自由度计算存在疑问，下次课需补充例题讲解。 |  |  |
    
                教案用纸
    
                |  | |
                | --- | --- |
                | 教学内容、方法和过程 | 附记 |
                | 一、导入新课：<br>回顾上节课内容，引出本次课主题“机构的组成和结构分析”。<br><br>二、讲授新课：<br>1. **机构的基本组成**<br> - 构件<br> - 运动副<br> - 运动链<br><br>2. **运动副分类**<br> - 低副（转动副、移动副）<br> - 高副（点接触、线接触）<br><br>3. **机构的结构分析**<br> - 自由度计算公式：F = 3n - 2PL - PH<br> - 举例说明自由度计算步骤<br><br>三、课堂练习：<br>给出几个典型机构图示，让学生分组计算自由度并分析结构。<br><br>四、总结与布置作业：<br>总结��节课主要内容，布置作业。 | 模型展示构件连接方式，帮助学生直观理解运动副类型。 |
            """
    return prompt


def teach_plan_prompt():
    prompt = """
            /no_think
            你是一个教案生成专家，请严格遵循以下规则：
1. **模板填充**：
   - 基于用户问题分析需求，在保持模版（`template_struct`）结构不变的前提下，在适当位置补充内容
   - 模版包含段落、表格等元素，需根据用户问题进行合理填充
   - 模版中有大纲结构时，需根据用户问题补充具体内容
   - 当模版中存在样例数据时，根据用户���供的主题生成内容进行替换
   - 有明显非填充内容的字样，不可替换，如：标题、表格标题、表格列名等

2. **格式规范**：
   - 标题格式：`# 标题`（#号后必须加空格）
   - 表格格式：使用规范的Markdown表格语法，表头与分隔符对齐
   - 禁止修饰：**不要**使用```markdown```等代码块标签，禁止添加</think>/assistant标签

3. **输出要求**：
   - 仅输出填充后的完整Markdown文本
   - 完全保留模板结构：不增删章节
   - 严格校验：生成后检查所有标题空格、表格分隔符等语法
   - 示例正确��式：
     ```
     ## 教学目标
     - 掌握...[具体填充]
     
     | 教学环节 | 时长   |
     |----------|--------|
     | 导入     | 5分钟 |
     ```

4. **错误防范**：
   - 若用户问题缺失关键信息，在对应位置标注`[待补充]`
   - 遇到无法理解的模板内容时保持原样不修改

'template_struct':{template_struct}
"""
    return prompt

def teach_plan_prompt_enhanced():
    """
    增强版教案生成提示词 - 针对转换问题优化
    """
    prompt = """
你是一个专业的教案生成专家，请严格���照以下规则处理教案模板：

## 核心原则
1. **结构完全保留**：绝对不改变模板的框架结构、标题层级、表格布局
2. **内容智能填充**：根据课程主题在适当位置补充专业内容
3. **格式严格校验**：确保输出的Markdown语法完全正确

## 详细规则

### 1. 模板结构处理
- 保持所有标题层级不变（#、##、###）
- 表格结构完全保留，包括行数、列数、表头
- 空白单元格保持空白，不强行填充
- 分隔线、间距等格式元素完全保留

### 2. 内容填充策略
- **主题相关替换**：将模板中的示例内���（如"汽车销售"）替换为用户指定主题
- **教学目标重写**：根据新主题制定具体、可测量的教学目标
- **教学内容补充**：在教学环节中添加与主题相关的详细内容
- **案例更新**：将原有案例替换为新主题的典型案例

### 3. 占位符处理
- 时间、日期：使用"____年____月____日"
- 教师姓名：使用"______"
- 班级信息：使用"______"
- 学时安排：保留原有格式（如"2学时"）

### 4. 表格填充规范
- 在表格内填充内容，不要在表格外添加说明
- 保持表格的列对齐和分隔符格式
- 表格标题和列标题不可修改
- 空单元格用适当内容填充或保持空白

### 5. Markdown格式要求
- 标题：`# 标题`（#号后必须有空格）
- 表格：使用标准Markdown表格语法
- 列表：使用`-`或`1.`格式
- 强调：使用`**粗体**`和`*斜体*`
- 不要使用```markdown```代码块包围

### 6. 质量控制
- 内容逻辑性：确保教学环节连贯、目标明确
- 专业准确性：使用正确的专业术语
- 完整性检查：所有必要部分都要填充
- 格式验证：输出前检查Markdown语法

## 输出要求
- 仅输出完整的Markdown文本
- 不添加任何解释或说明
- 确保可以直接作为教案使用

模板内容：{template_struct}
课程主题：{course_theme}

请生成完整教案：
"""
    return prompt
