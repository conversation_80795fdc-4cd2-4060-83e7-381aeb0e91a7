import time

from django.conf import settings
from langchain.callbacks.base import BaseCallbackHandler
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import Runnable
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END
from typing import List, Optional, TypedDict, Dict

# ========== 1. 对话状态结构 ==========
class ConversationMessage(TypedDict):
    role: str  # "user" | "assistant"
    content: str

class ConversationState(TypedDict):
    user_id: str
    history: List[ConversationMessage]
    user_input: Optional[str]
    assistant_output: Optional[str]
    last_active: float  # 会话最后活跃时间戳


# ========== 2. 自定义流式回调 ==========
class StreamToStringHandler(BaseCallbackHandler):
    def __init__(self):
        self.chunks = []

    def on_llm_new_token(self, token, **kwargs):
        self.chunks.append(token)

    def get_text(self):
        return ''.join(self.chunks)


# ========== 3. 构建 LLM Chain ==========
def build_llm_chain() -> Runnable:
    prompt = ChatPromptTemplate.from_messages([
        ("system", "/no_think 你是一个有记忆的助手，可以和用户闲聊，并帮助用户完成文案优化任务。"),
        # ("placeholder", "{history}"),
        ("human", "{input}")
    ])
    return prompt  # LLM 延后注入以支持动态 handler


# ========== 4. 节点函数 ==========
MAX_HISTORY = 10  # 最多保留的历史轮数（用户+助手各一条，共 10 条表示 5 轮）
SESSION_TIMEOUT = 3600  # 1 小时未使用视为过期


def receive_user_input(state: ConversationState) -> ConversationState:
    state["last_active"] = time.time()
    state["assistant_output"] = None
    return state

def format_history(history: List[ConversationMessage]) -> str:
    return "\n".join([f"{m['role']}: {m['content']}" for m in history])




def generate_response(state: ConversationState, llm_chain: Runnable, streaming: bool = True) -> ConversationState:
    # formatted_history = format_history(state["history"])
    # 将 dict 格式的历史转换为 BaseMessage 列表
    base_messages = []
    for msg in state["history"]:
        if msg["role"] == "user":
            base_messages.append(HumanMessage(content=msg["content"]))
        elif msg["role"] == "assistant":
            base_messages.append(AIMessage(content=msg["content"]))
        elif msg["role"] == "system":
            base_messages.append(SystemMessage(content=msg["content"]))
    if streaming:
        handler = StreamToStringHandler()
        llm = ChatOpenAI(
        # model="QwQ-think",
        model = settings.LLM_NAME,
        openai_api_key=settings.LLM_API_KEY,
        openai_api_base=settings.LLM_BASE_URL_LESSON,
        max_tokens=4000,
        temperature=1,
        streaming=True,
        callbacks=[handler],
        )
        chain = llm_chain | llm
        chain.invoke({
            "history": base_messages,
            "input": state["user_input"]
        })
        state["assistant_output"] = handler.get_text()
    else:
        llm = ChatOpenAI(
        # model="QwQ-think",
        model = settings.LLM_NAME,
        openai_api_key=settings.LLM_API_KEY,
        openai_api_base=settings.LLM_BASE_URL_LESSON,
        max_tokens=4000,
        temperature=1,
        streaming=False,
        )
        chain = llm_chain | llm
        result = chain.invoke({
            "history": base_messages,
            "input": state["user_input"]
        })
        state["assistant_output"] = result.content if hasattr(result, "content") else str(result)

    return state

def update_history(state: ConversationState) -> ConversationState:
    state["history"].append({"role": "user", "content": state["user_input"]})
    state["history"].append({"role": "assistant", "content": state["assistant_output"]})
    # 限制历史记录长度
    if len(state["history"]) > MAX_HISTORY:
        state["history"] = state["history"][-MAX_HISTORY:]
    return state


# ========== 5. 构建 LangGraph ==========
def build_conversation_graph(llm_chain: Runnable, streaming: bool = True):
    workflow = StateGraph(ConversationState)

    workflow.add_node("receive", receive_user_input)
    workflow.add_node("respond", lambda s: generate_response(s, llm_chain, streaming=streaming))
    workflow.add_node("remember", update_history)

    workflow.set_entry_point("receive")
    workflow.add_edge("receive", "respond")
    workflow.add_edge("respond", "remember")
    workflow.add_edge("remember", END)

    return workflow.compile()


# ========== 6. 用户多会话管理 ==========
user_sessions: Dict[str, ConversationState] = {}

def get_or_create_session(user_id: str) -> ConversationState:
    now = time.time()
    # 清理过期会话
    expired_users = [uid for uid, s in user_sessions.items() if now - s.get("last_active", 0) > SESSION_TIMEOUT]
    for uid in expired_users:
        del user_sessions[uid]

    if user_id not in user_sessions:
        user_sessions[user_id] = {
            "user_id": user_id,
            "history": [],
            "user_input": None,
            "assistant_output": None,
            "last_active": now
        }
    return user_sessions[user_id]

def clear_user_history(user_id: str) -> str:
    session = get_or_create_session(user_id)
    session["history"] = []
    session["assistant_output"] = None
    return "✅ 历史记录已清空。"


# ========== 7. 主函数调用接口 ==========
def run_for_user(user_id: str, user_input: str, graph) -> str:
    state = get_or_create_session(user_id)
    state["user_input"] = user_input
    print(f"state before invoke: {state}")
    result = graph.invoke(state)
    user_sessions[user_id] = result  # 更新会话状态
    return result["assistant_output"]