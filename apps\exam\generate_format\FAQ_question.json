{"1": {"aiid": "1", "answer": "这是一个问答题的答案", "stem": "这是一段问答题的题干", "explanation": "这是一段问答题的解析"}, "2": {"aiid": "2", "answer": "参考答案：TCP（传输控制协议）是面向连接的协议，具有可靠性，通过三次握手建立连接，能确保数据有序、无差错地传输，还具备流量控制和拥塞控制机制。UDP（用户数据报协议）是无连接的协议，传输速度快，开销小，但不保证数据传输的可靠性，也没有流量控制和拥塞控制。在实时性要求高、对数据准确性容忍度相对较高的场景下会优先选择 UDP 协议，如视频直播、在线游戏。在视频直播中，偶尔丢失一些视频帧可能不会对观看体验造成严重影响，但如果采用 TCP 协议，因重传机制导致的延迟会使直播画面卡顿严重。在线游戏中，玩家的操作指令需要快速传输到服务器，UDP 协议的低延迟特性可满足这一需求。", "stem": "TCP 和 UDP 协议的主要特点分别是什么，在哪些场景下会优先选择使用 UDP 协议？", "explanation": "这是一段问答题的解析"}, "3": {"aiid": "3", "answer": "这是一个问答题的答案", "stem": "这是一段问答题的题干", "explanation": "这是一段问答题的解析"}, "...": {"...": "..."}, "n": {"aiid": "n", "...": "..."}}