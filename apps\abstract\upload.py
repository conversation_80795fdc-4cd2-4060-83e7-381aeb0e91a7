import datetime
import os
import random
import time

from django.conf import settings

from abstract.export_excel import get_full_domain


def file_upload(request, dirs):
    """
    request: 请求
    dirs: 要上传到的目录
    """
    files = request.FILES.getlist('file')
    msg = {}
    if not files:
        msg['code'] = 400
        msg['msg'] = "上传的文件不能为空"
        return msg

    invalid_files = []
    valid_files = []
    try:
        # 允许的文件扩展名
        allowed_extensions = (
            # # 图片格式
            # '.jpg', '.jpeg', '.png', '.gif', '.bmp',
            # # 视频格式
            # '.mp4', '.flv', '.avi', '.mov',
            # # 音频格式
            # '.mp3', '.wav', '.ogg',
            # 文档格式
            '.pdf', '.doc', '.docx', '.txt', '.md',
            # '.ppt', '.pptx', '.xls', '.xlsx'
        )

        # 允许的文件类型前缀
        # allowed_content_prefixes = ('image/', 'video/', 'audio/', 'application/', 'text/')
        allowed_content_prefixes = ('application/','text/')
        file_infos=[]
        for file in files:
            filename = file.name
            # 检查文件类型
            if not any(file.content_type.startswith(prefix) for prefix in allowed_content_prefixes):
                msg['code'] = 400
                msg['msg'] = "不支持的文件格式: {}".format(file.content_type)
                return msg

            # 检查文件扩展名
            if not filename.lower().endswith(allowed_extensions):
                invalid_files.append(filename)
                continue

            # 检查文件大小(500MB限制)
            if file.size > 1024 * 500000:
                msg['code'] = 400
                msg['msg'] = "文件大小不能超过500MB: {}".format(filename)
                return msg

            # 生成唯一的文件名
            curr_time = datetime.datetime.now()
            new_filename = rename_upload(filename)  # 假设这个函数能处理普通文件名
            time_path = curr_time.strftime("%Y-%m-%d")

            # 创建存储目录
            file_dir = dirs
            save_path:str = os.path.join(settings.MEDIA_ROOT, file_dir, time_path)
            if not os.path.exists(save_path):
                os.makedirs(save_path)

            # 保存文件
            file_path = os.path.join(save_path, new_filename)
            with open(file_path, 'wb') as f:
                for chunk in file.chunks():
                    f.write(chunk)

            # 生成访问URL
            file_url = f"{get_full_domain(request)}{settings.MEDIA_URL}{file_dir}/{time_path}/{new_filename}"
            valid_files.append(file_url)
            file_infos.append((file_path,new_filename,file.name,file.size,filename[filename.rindex('.')+1:]))

        # 处理无效文件
        if invalid_files:
            msg['code'] = 400
            msg['msg'] = '不支持的文件类型: {}'.format(','.join(invalid_files[:5]))
            return msg

        msg['code'] = 200
        msg['urls'] = valid_files  # 修改返回字段名为更通用的'urls'
        msg['msg'] = '上传成功'
        return msg,file_infos

    except Exception as e:
        msg['code'] = 400
        msg['msg'] = f'上传失败: {str(e)}'
        return msg


#上传图片名自定义
"""
参数为文件的名称
"""
def rename_upload(src)->str:
    # 文件扩展名
    ext = os.path.splitext(src)[1]
    # File names longer than 255 characters can cause problems on older OSes.
    if len(src) > 255:
        ext = ext[:255]
    # 定义文件名，年月日时分秒随机数
    fn = time.strftime('%Y%m%d%H%M%S')
    fn = fn + '_%d' % random.randint(100, 999)
    # 重写合成文件名
    name =  fn + ext
    return name
