# 大模型：AI生成试题提示词话术模板
from langchain_core.output_parsers import JsonOutputParser

# Langchain Packages
from langchain_openai import ChatOpenAI
from langchain_ollama import OllamaLLM as Ollama
from langchain_core.prompts import PromptTemplate
from langchain.chains import <PERSON><PERSON><PERSON><PERSON>

from django.conf import settings


# LLM model setup (replace with environment variable logic if necessary)
# llm = Ollama(
#     model=settings.LLM_NAME,
#     base_url=settings.LLM_BASE_URL_EXAM
# )
llm = ChatOpenAI(
            # model="QwQ-think",
            model=settings.LLM_NAME,
            openai_api_key=settings.LLM_API_KEY,
            openai_api_base=settings.LLM_BASE_URL_LESSON,
        )

# Quiz generation prompt template
single_quiz_template = """
/no_think
Text : {text}
You are an expert single choice question maker. Given the above text, create a quiz of {number}
single choice questions for {knowledge_name} with the following requirements:
- Tone: {tone} (0=simple, 1=medium, 2=complex)
- Difficulty: {difficulty} (0=easy, 1=medium,2=hard)
- Direction: {direction} (记忆=memorization, 理解=understanding, 应用=application, 创造=creation, 分析=analysis, 评价=evaluation)
- Ensure the questions are not repeated and conform to the text provided
- Questions should align with the specified knowledge point, difficulty and direction
- Include detailed explanations for each question
- Return results in Chinese
- Generate exactly {number} questions, no more, no less

Format requirements:
- Strictly follow the JSON format below
- Do not include any additional symbols or text outside of the JSON structure
- The output should be a pure JSON object

Example structure (Chinese output):
- Must strictly follow {response_json} format output
- 'options': The correct answers must be included in the options, and the number of options must be 4

Key explanations:
- 'text': The question content
- 'options': List of choices (mark the correct one)
- 'explanation': Detailed analysis of the question
- 'knowledge_name': Related knowledge point
- 'difficulty': Question difficulty level
- 'direction': The assessment objective (记忆/理解/应用/创造/分析/评价)
"""

# Create quiz generation prompt template
single_choice_quiz_prompt = PromptTemplate(
    template=single_quiz_template,
    input_variables=[
        "text",
        "number",
        "knowledge_name",
        "tone",
        "difficulty",
        "direction",
        "response_json",
    ],
)
# Create the quiz generation chain using RunnableSequence
single_choice_quiz_chain = LLMChain(
    llm=llm,
    prompt=single_choice_quiz_prompt,
    output_key="quiz",
    output_parser=JsonOutputParser(),
    verbose=True,
)

multiple_quiz_template = """
/no_think
Text : {text}
You are an expert multiple choice question maker. Given the above text, create a quiz of {number}
multiple choice questions for {knowledge_name} with the following requirements:
- Tone: {tone} (0=simple, 1=medium, 2=complex)
- Difficulty: {difficulty} (0=easy, 1=medium,2=hard)
- Direction: {direction} (记忆=memorization, 理解=understanding, 应用=application, 创造=creation, 分析=analysis, 评价=evaluation)
- Ensure the questions are not repeated and conform to the text provided
- Questions should align with the specified knowledge point, difficulty and direction
- Include detailed explanations for each question
- Return results in Chinese
- Generate exactly {number} questions, no more, no less

Format requirements:
- Strictly follow the JSON format below
- Do not include any additional symbols or text outside of the JSON structure
- The output should be a pure JSON object

Example structure (Chinese output):
- Must strictly follow {response_json} format output
- 'options': The correct answers must be included in the options, and the number of options must be 4


Key explanations:
- 'text': The question content
- 'options': List of choices (mark the correct one)
- 'explanation': Detailed analysis of the question
- 'knowledge_name': Related knowledge point
- 'difficulty': Question difficulty level
- 'direction': The assessment objective (记忆/理解/应用/创造/分析/评价)
"""
multiple_choice_quiz_template = PromptTemplate(
    template=multiple_quiz_template,
    input_variables=[
        "text",
        "number",
        "knowledge_name",
        "tone",
        "difficulty",
        "direction",
        "response_json",
    ],
)
multiple_choice_quiz_chain = LLMChain(
    llm=llm,
    prompt=multiple_choice_quiz_template,
    output_key="quiz",
    output_parser=JsonOutputParser(),
    verbose=True,
)

# Defining the Prompt Template nad the Chain for the Quiz Generation
boolean_quiz_template = """
/no_think
Text : {text}
You are an expert True/False question maker. Given the above text, create a quiz of {number}
True/False question for {knowledge_name} with the following requirements:
- Tone: {tone} (0=simple, 1=medium, 2=complex)
- Difficulty: {difficulty} (0=easy, 1=medium,2=hard)
- Direction: {direction} (记忆=memorization, 理解=understanding, 应用=application, 创造=creation, 分析=analysis, 评价=evaluation)
- Ensure the questions are not repeated and conform to the text provided
- Questions should align with the specified knowledge point, difficulty and direction
- Include detailed explanations for each question
- Return results in Chinese
- Generate exactly {number} questions, no more, no less

Format requirements:
- Strictly follow the JSON format below
- Do not include any additional symbols or text outside of the JSON structure
- The output should be a pure JSON object

Example structure (Chinese output):
- Must strictly follow {response_json} format output

Key explanations:
- 'text': The question content
- 'options': List of choices (mark the correct one)
- 'explanation': Detailed analysis of the question
- 'knowledge_name': Related knowledge point
- 'difficulty': Question difficulty level
- 'direction': The assessment objective (记忆/理解/应用/创造/分析/评价)
"""
boolean_quiz_prompt = PromptTemplate(
    template=boolean_quiz_template,
    input_variables=[
        "text",
        "number",
        "knowledge_name",
        "tone",
        "difficulty",
        "direction",
        "response_json",
    ],
)
boolean_quiz_chain = LLMChain(
    llm=llm,
    prompt=boolean_quiz_prompt,
    output_key="quiz",
    output_parser=JsonOutputParser(),
    verbose=True,
)


# Defining the Prompt Template nad the Chain for the Quiz Generation
faq_quiz_template = """
/no_think
Text: {text}
You are an expert FAQ maker. Based on the above text, create {number} Frequently Asked Questions (FAQs) with the following requirements:
- Knowledge Point: {knowledge_name}
- Tone: {tone} (0=simple, 1=medium, 2=complex)
- Difficulty: {difficulty} (0=简单/easy, 1=中等/medium, 2=困难/hard)
- Direction: {direction} (记忆=memorization, 理解=understanding, 应用=application, 创造=creation, 分析=analysis, 评价=evaluation)
- Questions must be clear and directly derived from the text
- Answers should be accurate and complete
- Include detailed explanations for each Q&A pair
- Avoid question repetition
- Return results in Chinese
- Generate exactly {number} questions, no more, no less

Difficulty Guidelines:
0 (简单): Basic factual questions
1 (中等): Questions requiring explanation or simple analysis
2 (困难): Complex questions involving synthesis or evaluation

Format Requirements:
- Strictly follow the JSON format below
- No additional text outside JSON structure
- Pure JSON output only

Example Structure (Chinese Output):
- Must strictly follow {response_json} format output

Key Fields Explanation:
- 'text': The question content
- 'options': List of choices (mark the correct one)
- 'explanation': Detailed analysis of the question
- 'knowledge_name': Related knowledge point
- 'difficulty': Question difficulty level
- 'direction': The assessment objective (记忆/理解/应用/创造/分析/评价)
"""
faq_quiz_prompt = PromptTemplate(
    template=faq_quiz_template,
    input_variables=[
        "text",
        "number",
        "knowledge_name",
        "tone",
        "difficulty",
        "direction",
        "response_json",
    ],
)
faq_quiz_chain = LLMChain(
    llm=llm,
    prompt=faq_quiz_prompt,
    output_key="quiz",
    output_parser=JsonOutputParser(),
    verbose=True,
)


blank_quiz_template = """
/no_think
Text : {text}
You are an expert fill in blank questions maker. Given the above text, create a quiz of {number}
fill in blank questions for {knowledge_name} with the following requirements:
- Tone: {tone} (0=simple, 1=medium, 2=complex)
- Difficulty: {difficulty} (0=easy, 1=medium,2=hard)
- Direction: {direction} (记忆=memorization, 理解=understanding, 应用=application, 创造=creation, 分析=analysis, 评价=evaluation)
- Ensure the questions are not repeated and conform to the text provided
- Questions should align with the specified knowledge point, difficulty and direction
- Include detailed explanations for each question
- Return results in Chinese
- Generate exactly {number} questions, no more, no less

Format requirements:
- Strictly follow the JSON format below
- Do not include any additional symbols or text outside of the JSON structure
- The output should be a pure JSON object

Example structure (Chinese output):
- Must strictly follow {response_json} format output

Key explanations:
- 'text': The question content
- 'options': List of choices (mark the correct one)
- 'explanation': Detailed analysis of the question
- 'knowledge_name': Related knowledge point
- 'difficulty': Question difficulty level
- 'direction': The assessment objective (记忆/理解/应用/创造/分析/评价)
"""
blank_quiz_prompt = PromptTemplate(
    template=blank_quiz_template,
    input_variables=[
        "text",
        "number",
        "knowledge_name",
        "tone",
        "difficulty",
        "direction",
        "response_json",
    ],
)

blank_quiz_chain = LLMChain(
    llm=llm,
    prompt=blank_quiz_prompt,
    output_key="quiz",
    output_parser=JsonOutputParser(),
    verbose=True,
)
