import logging

from django.conf import settings
from django.core.management.base import BaseCommand


logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    项目初始化命令: python manage.py init
    """

    def handle(self, *args, **options):

        print(f"正在准备初始化数据")

        for app in settings.INSTALLED_APPS:

            try:
                exec(f"""
from {app}.initialize import main
main()
                """)
            except ModuleNotFoundError as e:
                # print(f'???{str(e)}')
                pass
        print("初始化数据完成！")



