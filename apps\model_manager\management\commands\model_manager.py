"""
Django管理命令：GS代理管理器
"""
from django.core.management.base import BaseCommand
from django.conf import settings
from model_manager.client import model_manager_client
import json

class Command(BaseCommand):
    help = 'model-manager代理管理命令'

    def add_arguments(self, parser):
        parser.add_argument(
            'action',
            choices=['health', 'test', 'config'],
            help='要执行的操作'
        )

    def handle(self, *args, **options):
        action = options['action']
        
        if action == 'health':
            self.check_health()
        elif action == 'test':
            self.test_connection()
        elif action == 'config':
            self.show_config()

    def check_health(self):
        """检查Model Manager服务健康状态"""
        self.stdout.write("🏥 检查Model Manager服务健康状态...")
        
        try:
            response = model_manager_client.sync_request('GET', 'models', params={'page': 1, 'perPage': 1})
            
            if response.get('status_code') == 200:
                self.stdout.write(
                    self.style.SUCCESS('✅ Model Manager服务运行正常')
                )
                data = response.get('data', {})
                if isinstance(data, dict) and 'pagination' in data:
                    total = data['pagination'].get('total', 0)
                    self.stdout.write(f"📊 当前模型数量: {total}")
            else:
                self.stdout.write(
                    self.style.ERROR(f'❌ Model Manager服务异常，状态码: {response.get("status_code")}')
                )
                self.stdout.write(f'错误信息: {response.get("data")}')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ 无法连接到Model Manager服务: {e}')
            )

    def test_connection(self):
        """测试连接"""
        self.stdout.write("🔗 测试Model Manager连接...")
        
        endpoints = [
            'models',
            'model-instances', 
            'model-files',
            'model-sets',
            'model-evaluations'
        ]
        
        results = []
        
        for endpoint in endpoints:
            try:
                response = model_manager_client.sync_request('GET', endpoint, params={'page': 1, 'perPage': 1})
                status_code = response.get('status_code', 500)
                
                if status_code < 400:
                    results.append((endpoint, '✅', status_code))
                    self.stdout.write(f"✅ {endpoint}: {status_code}")
                else:
                    results.append((endpoint, '❌', status_code))
                    self.stdout.write(f"❌ {endpoint}: {status_code}")
                    
            except Exception as e:
                results.append((endpoint, '❌', str(e)))
                self.stdout.write(f"❌ {endpoint}: {e}")
        
        # 总结
        success_count = sum(1 for _, status, _ in results if status == '✅')
        total_count = len(results)
        
        self.stdout.write(f"\n📊 测试结果: {success_count}/{total_count} 成功")

    def show_config(self):
        """显示配置信息"""
        self.stdout.write("⚙️ 当前配置:")
        
        config = getattr(settings, 'MODEL_MANAGER_CONFIG', {})
        
        self.stdout.write(f"🌐 Model Manager地址: {config.get('BASE_URL', '未配置')}")
        self.stdout.write(f"⏱️ 超时时间: {config.get('TIMEOUT', '未配置')}秒")
        self.stdout.write(f"📝 API版本: {config.get('API_VERSION', '未配置')}")
        
        # 显示API Key（脱敏处理）
        api_key = config.get('API_KEY', '未配置')
        if api_key and api_key != '未配置':
            masked_key = api_key[:12] + '*' * (len(api_key) - 16) + api_key[-4:] if len(api_key) > 16 else api_key[:4] + '*' * (len(api_key) - 4)
            self.stdout.write(f"🔑 API Key: {masked_key}")
        else:
            self.stdout.write(f"🔑 API Key: {api_key}")
        
        # 检查配置是否完整
        required_keys = ['BASE_URL', 'TIMEOUT', 'API_VERSION', 'API_KEY']
        missing_keys = [key for key in required_keys if key not in config]
        
        if missing_keys:
            self.stdout.write(
                self.style.WARNING(f"⚠️ 缺少配置项: {', '.join(missing_keys)}")
            )
        else:
            self.stdout.write(
                self.style.SUCCESS("✅ 配置完整")
            )
