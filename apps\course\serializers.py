from rest_framework import serializers
from django.db import transaction

from .models import *
from user.models import UserInfo
from user.serializers import UserInfoSerializer
from ppt.serializers import PptModelSerializer
from teachplan.serializers import TeachPlanModelSerializer


class KnowledgePointSerializer(serializers.ModelSerializer):
    """
    知识点序列化器
    """

    class Meta:
        model = KnowledgePoint
        fields = '__all__'


class KnowledgePointCourseSerializer(serializers.ModelSerializer):
    """
    知识点序列化器-课程知识点列表使用
    """

    class Meta:
        model = KnowledgePoint
        fields = ('id', 'name')


class SemesterSerializer(serializers.ModelSerializer):
    """
    学期序列化器
    """

    class Meta:
        model = Semester
        fields = '__all__'


class CourseBaseSerializer(serializers.ModelSerializer):
    """
    课程序列化器-基础关键字段
    """

    class Meta:
        model = Course
        fields = ('id', 'title')


class CourseSemesterSerializer(serializers.ModelSerializer):
    """
    课程学期序列化器-列表
    """

    class Meta:
        model = CourseSemester
        fields = '__all__'


class CourseSemesterCreateSerializer(serializers.ModelSerializer):
    """
    课程学期序列化器-创建
    """

    class Meta:
        model = CourseSemester
        fields = '__all__'


class CourseSemesterOwnerSerializer(serializers.ModelSerializer):
    """
    课程学期序列化器-owner
    """
    course = CourseBaseSerializer(read_only=True)
    semester = SemesterSerializer(read_only=True)

    class Meta:
        model = CourseSemester
        fields = '__all__'


class CourseTeacherCreateSerializer(serializers.ModelSerializer):
    """
    课程教师序列化器-添加
    """

    class Meta:
        model = CourseTeacher
        fields = '__all__'


class CourseTeacherSerializer(serializers.ModelSerializer):
    """
    课程教师序列化器-列表
    """

    class Meta:
        model = CourseTeacher
        fields = '__all__'


# class CourseRetrieveSerializer(serializers.ModelSerializer):
#     """
#     课程序列化器-详情
#     """


class CourseCreateSerializer(serializers.ModelSerializer):
    """
    课程序列化器-新增
    """
    course_semesters = CourseSemesterCreateSerializer(many=True, read_only=True)  # 只读展示
    course_teachers = CourseTeacherCreateSerializer(many=True, read_only=True)  # 只读展示
    semester = serializers.PrimaryKeyRelatedField(
        queryset=Semester.objects.all(),
        write_only=True  # 只用于写入
    )
    teacher = serializers.PrimaryKeyRelatedField(
        queryset=UserInfo.objects.all(),
        write_only=True  # 只用于写入
    )
    role = serializers.CharField(write_only=True)

    class Meta:
        model = Course
        fields = ['id', 'title', 'description', 'image', 'knowledge_uid', 'course_semesters', 'course_teachers',
                  'semester', 'teacher',
                  'role']

    def validate(self, data):
        semester = data.get('semester', '')
        teacher = data.get('teacher', '')
        role = data.get('role', '')
        if not semester or not teacher or not role:
            raise serializers.ValidationError('学期、教师和角色不能为空')
        # if len(teachers) != len(roles):
        #     raise serializers.ValidationError('用户和角色数量不一致')
        return data

    def create(self, validated_data):
        semester = validated_data.pop('semester', '')
        teacher = validated_data.pop('teacher', '')
        role = validated_data.pop('role', '')
        print('teacher', teacher)
        try:
            with transaction.atomic():
                course = Course.objects.create(**validated_data)

                course_semester = CourseSemester.objects.create(course=course,
                                                                semester=semester,
                                                                user=teacher)

                CourseTeacher.objects.create(course_semester=course_semester,
                                             teacher=teacher,
                                             role=role)
        except Exception as e:
            print(e)
            raise serializers.ValidationError('创建失败')
        return course


class CourseCopySerializer(serializers.ModelSerializer):
    """
    课程序列化器-复制
    """

    class Meta:
        model = Course
        fields = '__all__'


class CourseSerializer(serializers.ModelSerializer):
    """
    课程序列化器
    """

    # semester = CourseSemesterSerializer(many=True, read_only=True)  # 只读展示

    # semester = serializers.SerializerMethodField()
    # teacher = CourseTeacherSerializer(read_only=True)  # 只读展示

    course_semester_id = serializers.SerializerMethodField(read_only=True)

    def get_course_semester_id(self, obj):
        semester_id = self.context['request'].query_params.get('semester_id')
        # print('学期ID', semester_id)
        try:
            # print('课程ID', obj.id)
            # print('学期ID', semester_id)
            course_semester = CourseSemester.objects.get(course_id=obj.id, semester_id=semester_id)
        except CourseSemester.DoesNotExist:
            return None
        return course_semester.id

    class Meta:
        model = Course
        fields = (
            'id', 'title', 'description', 'image', 'knowledge_uid', 'created_at', 'updated_at', 'course_semester_id')


class CourseAndSemesterSerializer(serializers.ModelSerializer):
    """
    课程和学期信息序列化器
    """
    semester = SemesterSerializer(many=True, read_only=True)

    class Meta:
        model = Course
        fields = '__all__'


class CourseOwnerSerializer(serializers.ModelSerializer):
    """
    课程拥有者序列化器
    """

    class Meta:
        model = Course
        fields = ('id', 'title')


class CourseTeacherSerializer(serializers.ModelSerializer):
    """
    课程教师序列化器
    """
    teacher = UserInfoSerializer(read_only=True)

    class Meta:
        model = CourseTeacher
        fields = '__all__'


class CourseTeacherAddSerializer(serializers.ModelSerializer):
    """
    课程教师序列化器
    """

    # teachers = serializers.PrimaryKeyRelatedField(
    #     queryset=UserInfo.objects.all(),
    #     many=True,
    #     write_only=True  # 只用于写入
    # )
    teachers = serializers.ListField(child=serializers.CharField(), write_only=True)
    roles = serializers.ListField(child=serializers.CharField(), write_only=True)

    class Meta:
        model = CourseTeacher
        fields = ['id', 'course_semester', 'teachers', 'roles', 'is_active']

    def create(self, validated_data):
        course_semester = validated_data.pop('course_semester', [])
        teachers = validated_data.pop('teachers', [])
        roles = validated_data.pop('roles', [])
        course_teachers = []

        for teacher, role in zip(teachers, roles):
            # print(course)
            # print(teacher.id)
            # print(type(teacher))
            # print(teacher, role)
            # print(teacher)
            user = UserInfo.objects.get(username=teacher)
            if CourseTeacher.objects.filter(course_semester=course_semester, teacher=user).exists():
                continue
                # raise serializers.ValidationError('该用户已存在')
            else:
                course_teacher = CourseTeacher.objects.create(course_semester=course_semester,
                                                              teacher=user,
                                                              role=role
                                                              )
            # course_teachers.append(course_teacher)
            # print(course_teachers)
        return 0


class ChapterCompletionSerializer(serializers.ModelSerializer):
    """
    章节完成序列化器
    """

    class Meta:
        model = ChapterCompletion
        fields = '__all__'


class ChapterChildSerializer(serializers.ModelSerializer):
    """
    子章节序列化器
    """
    completions = serializers.SerializerMethodField()

    def get_completions(self, obj):
        student = self.context['request'].user
        # print('用户ID', student.id)
        # print('章节ID', obj.id)
        try:
            chapter_completion = ChapterCompletion.objects.get(chapter=obj, student=student)
        except ChapterCompletion.DoesNotExist:
            return None
        return ChapterCompletionSerializer(chapter_completion).data

    class Meta:
        model = Chapter
        fields = ['id', 'title', 'content', 'parent', 'order', 'completions']


class RecursiveField(serializers.Serializer):
    """处理自引用的递归字段"""

    def to_representation(self, value):
        serializer = self.parent.parent.__class__(value, context=self.context)
        return serializer.data


class ChapterViewSerializer(serializers.ModelSerializer):
    """
    章节序列化器-列表
    """
    # children = ChapterChildSerializer(many=True, read_only=True)
    completions = serializers.SerializerMethodField()
    # children = serializers.SerializerMethodField()
    children = RecursiveField(many=True, read_only=True)

    # def get_children(self, obj):
    #     """递归序列化子目录"""
    #     children = obj.children.all()
    #     if children:
    #         return ChapterChildSerializer(children, many=True, context=self.context).data

    def get_completions(self, obj):
        student = self.context['request'].user.id
        try:
            chapter_completion = ChapterCompletion.objects.get(chapter=obj, student_id=student)
        except ChapterCompletion.DoesNotExist:
            return None

        return ChapterCompletionSerializer(chapter_completion).data

    class Meta:
        model = Chapter
        fields = ['id', 'course', 'title', 'content', 'parent', 'order', 'completions', 'children']


class ChapterTablesSerializer(serializers.ModelSerializer):
    """
    指定章节目录序列化器
    """
    # children = ChapterChildSerializer(many=True, read_only=True)
    # children = serializers.SerializerMethodField()
    children = RecursiveField(many=True, read_only=True)

    # def get_children(self, obj):
    #     """递归序列化子目录"""
    #     children = obj.children.all()
    #     print(children)
    #     if children:
    #         return ChapterChildSerializer(children, many=True, context=self.context).data

    class Meta:
        model = Chapter
        fields = ['id', 'course', 'title', 'content', 'parent', 'order', 'children']


class ChapterRetrieveSerializer(serializers.ModelSerializer):
    """
    章节序列化器-详情
    """
    ppts = PptModelSerializer(many=True, read_only=True)
    teach_plans = TeachPlanModelSerializer(many=True, read_only=True)
    knowledge_points = KnowledgePointSerializer(many=True, read_only=True)

    class Meta:
        model = Chapter
        fields = ['id', 'course', 'title', 'content', 'parent', 'order', 'ppts', 'teach_plans', 'knowledge_points']


class ChapterSerializer(serializers.ModelSerializer):
    """
    章节序列化器--增加
    """
    course_semester_id = serializers.PrimaryKeyRelatedField(
        queryset=CourseSemester.objects.all(),
        write_only=True  # 只用于写入
    )

    class Meta:
        model = Chapter
        fields = '__all__'

    def create(self, validated_data):
        # course = validated_data.pop('course', [])
        # print(course)
        course_semester_id = validated_data.pop('course_semester_id', [])
        # print(course_semester_id)
        # 创建章节
        with transaction.atomic():
            chapter = Chapter.objects.create(**validated_data)
            # 查询课程班级
            classes_id = Class.objects.filter(course_semester_id=course_semester_id).values_list('id', flat=True)
            # print('班级主键ID', classes_id)
            # 查询学生信息
            student_ids = ClassMember.objects.filter(class_obj_id__in=classes_id).values_list('student_id', flat=True)
            # print('学生主键ID', student_ids)
            # print(course_semester_id)
            # course_semester = CourseSemester.objects.get(id=course_semester_id)
            # 批量初始化章节完成情况
            for student_id in student_ids:
                # print(123)
                ChapterCompletion.objects.get_or_create(student_id=student_id, chapter_id=chapter.id,
                                                        course_semester=course_semester_id)
            return chapter


class ClassSerializer(serializers.ModelSerializer):
    """
    班级序列化器
    """

    class Meta:
        model = Class
        fields = '__all__'


class ClassMemberSerializer(serializers.ModelSerializer):
    """
    班级成员序列化器
    """
    student = UserInfoSerializer(read_only=True)

    class Meta:
        model = ClassMember
        fields = '__all__'


class ChapterKnowledgeSerializer(serializers.ModelSerializer):
    """
    章节知识点序列化器
    """
    knowledge_points = KnowledgePointSerializer(many=True, read_only=True)

    class Meta:
        model = Chapter
        fields = ('id', 'title', 'knowledge_points')


class CourseKnowledgeSerializer(serializers.ModelSerializer):
    """
    课程知识点序列化器
    """
    # chapters = ChapterKnowledgeSerializer(many=True, read_only=True)
    chapters = serializers.SerializerMethodField()

    def get_chapters(self, obj):
        context = self.context
        # print(context)
        # print('查询头', context)
        fuzzy = context.get('fuzzy', False)
        # print('模糊查询字段', fuzzy)
        if fuzzy:
            chapters = Chapter.objects.filter(course=obj, title__contains=fuzzy).distinct()
            return ChapterKnowledgeSerializer(chapters, many=True).data

        chapters = Chapter.objects.filter(course=obj)
        return ChapterKnowledgeSerializer(chapters, many=True).data

    class Meta:
        model = Course
        fields = ('id', 'title', 'chapters')


class ChapterKnowledgeLevelSerializer(serializers.ModelSerializer):
    """
    章节知识点嵌套序列化器
    """
    # 嵌套序列化子章节
    # children = serializers.SerializerMethodField()
    children = RecursiveField(many=True, read_only=True)
    # 序列化关联的知识点
    knowledge_points = KnowledgePointSerializer(many=True, read_only=True)

    class Meta:
        model = Chapter
        fields = ['id', 'title', 'content', 'order', 'parent', 'knowledge_points', 'children']


class CourseChapterKnowledgeSerializer(serializers.ModelSerializer):
    """
    课程章节知识点序列化器
    """
    chapters = serializers.SerializerMethodField()

    def get_chapters(self, obj):
        """获取课程的顶级章节并序列化"""
        # 过滤出parent为null的章节（顶级章节）
        top_chapters = obj.chapters.filter(parent=None)
        # 使用ChapterSerializer序列化顶级章节
        return ChapterKnowledgeLevelSerializer(top_chapters, many=True, context=self.context).data

    class Meta:
        model = Course
        fields = ('id', 'title', 'description', 'image', 'chapters')


class ChapterOwnershipSerializer(serializers.ModelSerializer):
    """
    章节列化器
    """

    children = RecursiveField(many=True, read_only=True)

    class Meta:
        model = Chapter
        fields = ['id', 'course', 'title', 'content', 'parent', 'order', 'children']


class CourseChapterSerializer(serializers.ModelSerializer):
    """
    课程章节知识点序列化器
    """
    chapters = serializers.SerializerMethodField()

    def get_chapters(self, obj):
        """获取课程的顶级章节并序列化"""
        # 过滤出parent为null的章节（顶级章节）
        top_chapters = obj.chapters.filter(parent=None)
        # 使用ChapterSerializer序列化顶级章节
        return ChapterOwnershipSerializer(top_chapters, many=True, context=self.context).data

    class Meta:
        model = Course
        fields = ('id', 'title', 'description', 'image', 'chapters')
