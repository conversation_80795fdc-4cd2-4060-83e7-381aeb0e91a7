# 信号接收器
from django.db import transaction
from django.dispatch import receiver

from .marking_engine import MarkingEngine
from .signals import exam_submitted
from .models import *


@receiver(exam_submitted)
def handle_ai_marking(sender, **kwargs):
    """
    试卷批阅
    1. 客观题（单选、多选、判断、填空）使用正则匹配方式进行评阅
    2. 主观题（问答）使用AI阅卷
    """
    print(f"收到信号！接收参数：{kwargs}")

    try:
        with transaction.atomic():
            # 获取考试和用户参加记录
            # result = UserExamQuestionFeedback.objects.select_for_update().get(id=feedback_id)
            # exam = KnowledgeExam.objects.select_for_update().get(id=exam_id)

            # 1. 获取所有需要批阅的记录
            feedbacks = UserExamQuestionFeedback.objects.filter(
                id__in=kwargs["feedback_ids"],
                exam_id=kwargs["exam_id"],
                user=kwargs["user"],
            ).select_related("question_version")

            print(f"待批阅题目数：{feedbacks.count()}")

            # 2. 批量批阅并更新
            total_score = 0
            for f in feedbacks:
                try:
                    print(f"处理反馈ID {f.id} | 答案内容： {f.feedback}")  # 验证数据
                    result = MarkingEngine.mark_feedback(f)
                    print(f"result:{result}")
                    # 更新记录
                    f.get_score = result["get_score"]
                    f.is_judged = True
                    f.justification = result.get("justification", "")
                    f.error_types = result.get("error_types", "")
                    f.save()

                    total_score += result["get_score"]
                    print(f"题目[{f.id}] 批阅完成 | 得分：{result['get_score']}]\n")



                except Exception as e:
                    print(f"题目[{f.id}] 阅卷失败: {e}")

            # 3. 更新考试总成绩
            user_exam_record = UserTakePartExam.objects.get(
                exam_id=kwargs["exam_id"], user=kwargs["user"]
            )
            user_exam_record.total_score = total_score
            user_exam_record.take_part_status = "已阅卷"
            user_exam_record.save()

            print(
                f"\n批阅完成 | 用户考试记录ID: {user_exam_record.id} | 总分: {total_score}"
            )

    except Exception as e:
        print(f"批阅流程错误：{str(e)}")
        raise

