
from django_filters import rest_framework
from .models import PptModel, PptTmpModel


class PptFilter(rest_framework.FilterSet):
    author = rest_framework.NumberFilter(field_name='author', lookup_expr='exact') # 关联到用户表的外键字段名
    template = rest_framework.NumberFilter(field_name='template', lookup_expr='exact') # 关联到PPT模板表的外键字段名
    chapter = rest_framework.NumberFilter(field_name='chapter', lookup_expr='exact') # 关联到PPT章节表的外键字段名
    title = rest_framework.CharFilter(field_name='title', lookup_expr='icontains')
    is_deleted = rest_framework.BooleanFilter(field_name='is_deleted', lookup_expr='exact')
    is_ai_generated = rest_framework.BooleanFilter(field_name='is_ai_generated', lookup_expr='exact')
    course_id = rest_framework.NumberFilter(field_name='chapter__course_id', lookup_expr='exact')

    
    class Meta:
        model = PptModel
        fields = [
            'author', 'template', 'chapter',
            'title', 'is_deleted', 'is_ai_generated', 'course_id'
        ]


class PptTmpFilter(rest_framework.FilterSet):
    author = rest_framework.NumberFilter(field_name='author', lookup_expr='exact') # 关联到用户表的外键字段名
    title = rest_framework.CharFilter(field_name='title', lookup_expr='icontains') # 模板标题字段名
    template_type = rest_framework.CharFilter(field_name='template_type', lookup_expr='exact') # 模板类型字段名
    style = rest_framework.CharFilter(field_name='style', lookup_expr='exact') # 主题风格字段名
    is_deleted = rest_framework.BooleanFilter(field_name='is_deleted', lookup_expr='exact') # 是否删除字段名
    is_public = rest_framework.BooleanFilter(field_name='is_public', lookup_expr='exact') # 是否公开PPT模版字段名

    class Meta:
        model = PptTmpModel
        fields = [
            'author', 'title', 'template_type',
            'style', 'is_deleted', 'is_public'     
        ]