from rest_framework import serializers
from .models import GraphTask


class GraphTaskSerializer(serializers.ModelSerializer):
    """知识图谱任务序列化器"""

    status_display = serializers.SerializerMethodField()

    class Meta:
        model = GraphTask
        fields = ['id', 'user', 'name', 'status', 'status_display', 'error_message',
                  'result', 'node_count', 'edge_count', 'created_at', 'update_at']
        read_only_fields = ['status', 'error_message', 'result', 'created_at', 'update_at']

    def get_status_display(self, obj):
        return obj.get_status_display()