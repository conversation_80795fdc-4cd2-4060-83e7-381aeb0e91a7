import json, requests, os
from rest_framework import viewsets, mixins
from rest_framework.response import Response
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
from django.conf import settings
from django.http import StreamingHttpResponse
from rest_framework.decorators import action

from .utils import api_response, pdf_analysis, docx_analysis, txt_analysis, filter_think_tags
from .prompt import abstract_prompt


# Create your views here.

class DocumentAnalysisViewSet(viewsets.GenericViewSet, mixins.CreateModelMixin, mixins.ListModelMixin):
    """
    摘要提取
    create：
        文档解析（txt、pdf、docx）

    """

    def create(self, request, *args, **kwargs):
        """
        文档解析（txt、pdf、docx）
        """

        file = request.FILES['file']

        # 根据文件后缀名指定解析方式
        if file.name.endswith('.pdf'):
            content, flag = pdf_analysis(file)
            if flag == 0:
                return api_response(400, 'pdf解析失败')
        elif file.name.endswith('.docx'):
            content, flag = docx_analysis(file)
        elif file.name.endswith('.txt'):
            content, flag = txt_analysis(file)
        else:
            return api_response(400, '文件格式不支持')

        if flag == 0:
            return api_response(400, '文件解析失败')
        else:
            data = {
                'filename': file.name,
                'size': file.size,  # 文件大小（字节）
                'content_preview': content,
                'total_length': len(content),
                'success': True
            }
            return api_response(200, '文件解析成功', data=data)

    @action(detail=False, methods=['POST'])
    def ai(self, request, *args, **kwargs):
        """
        摘要提取，使用大模型进行摘要提取
        """
        document_content = request.data.get('document_content', '')
        count = request.data.get('count', '')

        llm = ChatOpenAI(
            # model="QwQ-think",
            model=settings.LLM_NAME,
            openai_api_key=settings.LLM_API_KEY,
            openai_api_base=settings.LLM_BASE_URL_LESSON,
            max_tokens=4000,
            temperature=1,
        )

        # 提示词输出格式模版
        with open(
                "./apps/abstract/generate_format/data_format.json", "r", encoding="utf-8"
        ) as f:
            data_format = json.load(f)

        with open(
                "./apps/abstract/generate_format/data_format_example.json", "r", encoding="utf-8"
        ) as f:
            data_format_example = json.load(f)

        prompt = ChatPromptTemplate.from_messages([
            SystemMessagePromptTemplate.from_template(abstract_prompt()),
            HumanMessagePromptTemplate.from_template("请从上述文本中整理文本{theme}相关摘要")
        ])

        # print(prompt)
        messages = prompt.format_messages(
            data_format=json.dumps(data_format, ensure_ascii=False, indent=2),
            data_format_example=json.dumps(data_format_example, ensure_ascii=False, indent=2),
            count=count,
            theme=document_content
        )

        res = llm.stream(messages)  # 流式输出

        # def generate():
        #     for chunk in res:
        #         content = chunk.content
        #         if content:
        #             yield content

        return StreamingHttpResponse(filter_think_tags(res), content_type="text/plain; charset=utf-8")  # 返回响应大纲

    @action(detail=False, methods=['GET'])
    def markify(self, request, *args, **kwargs):
        """
        文档解析（txt、pdf、docx）
        """
        # target_url = "http://***********:20926/api/jobs"
        # uploaded_file = request.FILES['file']
        #
        # files = {
        #     'file': (uploaded_file.name, uploaded_file.read(), uploaded_file.content_type)
        # }
        #
        # # 发送请求到目标服务
        # response = requests.post(
        #     url=target_url,
        #     data=data,
        #     files=files,
        #     headers={'X-Forwarded-For': request.META.get('REMOTE_ADDR', '')}
        # )

        target_url = "http://***********:20926/api/jobs/5a83cb59-08de-4dd1-b03a-6600efe659e2/result"
        response = requests.get(target_url)
        content_disposition = response.headers.get('Content-Disposition')
        # print(content_disposition)
        # if "Content-Disposition" in response.headers:
        #     content_disposition = response.headers["Content-Disposition"]
        #     # 解析形如 "attachment; filename=\"example.pdf\"" 的头部
        #     filename = content_disposition.split("filename=")[-1].strip('\"')
        #     print(filename)

        total_size = int(response.headers.get('content-length', 0))
        block_size = 1024  # 1KB
        # print(total_size)

        # progress_bar = tqdm(
        #     total=total_size,
        #     unit='B',
        #     unit_scale=True,
        #     unit_divisor=1024,
        #     desc=f"下载中: {os.path.basename(save_path)}"
        # )

        # 分块写入文件

        with open('123.md', 'wb') as file:
            for data in response.iter_content(block_size):
                if data:  # 过滤空块
                    file.write(data)
        # print(f"文件已保存至: {}")
        # print(response.Files)
        # print(response.text)

        return Response(response.text, content_type="text/plain; charset=utf-8")
