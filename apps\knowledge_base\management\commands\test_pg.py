from django.core.management.base import BaseCommand
from django.db import connection
from django.core.management import call_command
# from knowledge_base.models import KnowledgeBase, UserKBLink, CourseKBLink


class Command(BaseCommand):
    help = '管理数据库表的创建和删除'

    def add_arguments(self, parser):
        parser.add_argument(
            'action',
            type=str,
            choices=['create', 'drop', 'recreate', 'list'],
            help='操作类型: create, drop, recreate, list'
        )
        parser.add_argument(
            '--app',
            type=str,
            help='指定应用名称'
        )
        parser.add_argument(
            '--table',
            type=str,
            help='指定表名'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制执行（用于删除操作）'
        )

    def handle(self, *args, **options):
        action = options['action']
        app_name = options.get('app')
        table_name = options.get('table')
        force = options.get('force', False)

        if action == 'create':
            self.create_tables(app_name)
        elif action == 'drop':
            self.drop_tables(app_name, table_name, force)
        elif action == 'recreate':
            self.recreate_tables(app_name, force)
        elif action == 'list':
            self.list_tables()

    def create_tables(self, app_name=None):
        """创建表"""
        self.stdout.write("开始创建表...")

        try:
            if app_name:
                # 为特定应用创建表
                call_command('makemigrations', app_name)
                call_command('migrate', app_name)
            else:
                # 创建所有表
                call_command('makemigrations')
                call_command('migrate')

            self.stdout.write(
                self.style.SUCCESS('表创建成功!')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'创建表失败: {e}')
            )

    def drop_tables(self, app_name=None, table_name=None, force=False):
        """删除表"""
        if not force:
            confirm = input("确定要删除表吗？这将删除所有数据！(yes/no): ")
            if confirm.lower() != 'yes':
                self.stdout.write("操作已取消")
                return

        self.stdout.write("开始删除表...")

        try:
            with connection.cursor() as cursor:
                if table_name:
                    # 删除特定表
                    cursor.execute(f"DROP TABLE IF EXISTS {table_name} CASCADE;")
                    self.stdout.write(f"表 {table_name} 已删除")
                elif app_name == 'knowledge_base':
                    # 删除知识库相关表
                    tables = [
                        'course_kb_links',      # 而不是 'knowledge_base_course_kb_link'
                        'user_kb_links',        # 而不是 'knowledge_base_user_kb_link'
                        'knowledge_bases'
                    ]
                    for table in tables:
                        cursor.execute(f"DROP TABLE IF EXISTS {table} CASCADE;")
                        self.stdout.write(f"表 {table} 已删除")
                else:
                    self.stdout.write("请指定 --app 或 --table 参数")
                    return

            self.stdout.write(
                self.style.SUCCESS('表删除成功!')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'删除表失败: {e}')
            )

    def recreate_tables(self, app_name=None, force=False):
        """重新创建表"""
        self.stdout.write("开始重新创建表...")

        # 先删除
        self.drop_tables(app_name, force=force)

        # 再创建
        self.create_tables(app_name)

    def list_tables(self):
        """列出所有表"""
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name;
            """)
            tables = cursor.fetchall()

            self.stdout.write("数据库中的表:")
            for table in tables:
                self.stdout.write(f"  - {table[0]}")