from django.db import transaction
from django.contrib.auth import get_user_model
from guardian.models import UserObjectPermission

from .models import KnowledgeBase, CourseKBLink
from course.models import Course
from django.db.models import Q
User = get_user_model()


def get_course_kb_links(course_id: int):
    """获取指定课程的所有知识库关联"""
    try:
        from .models import CourseKBLink
        links = CourseKBLink.objects.filter(
            course_id=course_id,
            is_active=True
        ).select_related('knowledge_base', 'course')

        print(f"[INFO] 课程(ID:{course_id})关联了 {len(links)} 个知识库")
        return links
    except Exception as e:
        print(f"[ERROR] 获取课程知识库关联失败：{e}")
        return []

def get_knowledge_base_by_id(kb_id: str):
    """根据ID查询知识库"""
    try:
        kb = KnowledgeBase.objects.get(id=kb_id, is_active=True)
        return kb
    except KnowledgeBase.DoesNotExist:
        print(f"[WARN] 未找到ID为 {kb_id} 的知识库")
        return None
    except Exception as e:
        print(f"[ERROR] 查询知识库失败：{e}")
        return None


@transaction.atomic
def add_knowledge_base(kb_id: str, name: str, description: str = None, document_count: int = 0, user_id: int = None, **kwargs) -> bool:
    """新增知识库 - 包含创建者信息"""
    try:
        if KnowledgeBase.objects.filter(id=kb_id).exists():
            print(f"[WARN] 知识库ID '{kb_id}' 已存在，跳过添加")
            return False

        # 验证用户是否存在
        if user_id:
            try:
                user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                print(f"[WARN] 用户ID {user_id} 不存在")
                return False
        else:
            print(f"[WARN] 未提供用户ID，无法创建知识库")
            return False

        kb = KnowledgeBase.objects.create(
            id=kb_id,
            name=name,
            description=description,
            document_count=document_count,
            user=user,
            is_active=True
        )

        print(f"[INFO] 新增知识库成功：ID={kb_id}, 名称={name}, 创建者={user.username}")
        return True
    except Exception as e:
        print(f"[ERROR] 新增知识库失败：{e}")
        return False


@transaction.atomic
def update_knowledge_base(kb_id: str, **update_fields) -> bool:
    """更新知识库信息"""
    try:
        kb = KnowledgeBase.objects.get(id=kb_id, is_active=True)

        for field, value in update_fields.items():
            if hasattr(kb, field) and value is not None:
                setattr(kb, field, value)

        kb.save()
        print(f"[INFO] 更新知识库成功：ID={kb_id}")
        return True
    except KnowledgeBase.DoesNotExist:
        print(f"[WARN] 未找到ID为 {kb_id} 的知识库")
        return False
    except Exception as e:
        print(f"[ERROR] 更新知识库失败：{e}")
        return False


@transaction.atomic
def delete_knowledge_base(kb_id: str, force: bool = False) -> bool:
    """删除知识库（软删除或硬删除）"""
    try:
        kb = KnowledgeBase.objects.get(id=kb_id)

        if force:
            kb.delete()
            print(f"[INFO] 硬删除知识库成功：ID={kb_id}")
        else:
            kb.is_active = False
            kb.save()
            print(f"[INFO] 软删除知识库成功：ID={kb_id}")

        return True
    except KnowledgeBase.DoesNotExist:
        print(f"[WARN] 未找到ID为 {kb_id} 的知识库")
        return False
    except Exception as e:
        print(f"[ERROR] 删除知识库失败：{e}")
        return False


# def get_user_kb_links(user_id: int):
#     """获取用户的所有知识库关联记录"""
#     try:
#         links = UserKBLink.objects.filter(
#             user_id=user_id,
#             is_active=True,
#             knowledge_base__is_active=True
#         ).select_related('knowledge_base')
#
#         print(f"[INFO] 用户(ID:{user_id})关联了 {len(links)} 个知识库")
#         return links
#     except Exception as e:
#         print(f"[ERROR] 查询用户知识库关联失败：{e}")
#         return []

def get_user_created_knowledge_bases(user_id: int):
    """获取用户创建的所有知识库"""
    try:
        kbs = KnowledgeBase.objects.filter(
            user_id=user_id,
            is_active=True
        ).order_by('-created_at')

        print(f"[INFO] 用户(ID:{user_id})创建了 {len(kbs)} 个知识库")
        return kbs
    except Exception as e:
        print(f"[ERROR] 查询用户创建的知识库失败：{e}")
        return []

@transaction.atomic
# def add_user_kb_link(user_id: int, kb_id: str) -> bool:
#     """添加用户-知识库关联"""
#     try:
#         if not User.objects.filter(id=user_id).exists():
#             print(f"[WARN] 用户ID {user_id} 不存在")
#             return False
#
#         if not KnowledgeBase.objects.filter(id=kb_id, is_active=True).exists():
#             print(f"[WARN] 知识库ID {kb_id} 不存在或已禁用")
#             return False
#
#         if UserKBLink.objects.filter(user_id=user_id, knowledge_base_id=kb_id).exists():
#             print(f"[WARN] 用户 {user_id} 与知识库 {kb_id} 的关联已存在")
#             return False
#
#         UserKBLink.objects.create(
#             user_id=user_id,
#             knowledge_base_id=kb_id,
#             is_active=True
#         )
#
#         print(f"[INFO] 用户-知识库关联添加成功: 用户{user_id} -> 知识库{kb_id}")
#         return True
#     except Exception as e:
#         print(f"[ERROR] 添加用户-知识库关联失败：{e}")
#         return False


def get_kb_course_links(kb_id: str):
    """获取知识库的所有课程关联记录"""
    try:
        links = CourseKBLink.objects.filter(
            knowledge_base_id=kb_id,
            is_active=True
        ).select_related('course')

        print(f"[INFO] 知识库(ID:{kb_id})关联了 {len(links)} 个课程")
        return links
    except Exception as e:
        print(f"[ERROR] 查询知识库课程关联记录失败：{e}")
        return []


@transaction.atomic
def add_course_kb_link(course_id: int, kb_id: str) -> bool:
    """添加课程-知识库关联"""
    try:
        if not Course.objects.filter(id=course_id).exists():
            print(f"[WARN] 课程ID {course_id} 不存在")
            return False

        if not KnowledgeBase.objects.filter(id=kb_id, is_active=True).exists():
            print(f"[WARN] 知识库ID {kb_id} 不存在或已禁用")
            return False

        if CourseKBLink.objects.filter(course_id=course_id, knowledge_base_id=kb_id).exists():
            print(f"[WARN] 课程 {course_id} 与知识库 {kb_id} 的关联已存在")
            return False

        CourseKBLink.objects.create(
            course_id=course_id,
            knowledge_base_id=kb_id,
            is_active=True
        )

        print(f"[INFO] 课程-知识库关联添加成功: 课程{course_id} -> 知识库{kb_id}")
        return True
    except Exception as e:
        print(f"[ERROR] 添加课程-知识库关联失败：{e}")
        return False


def get_all_active_knowledge_bases():
    """获取所有活跃知识库"""
    try:
        kbs = KnowledgeBase.objects.filter(is_active=True).select_related('user')
        print(f"[INFO] 共有 {len(kbs)} 个活跃知识库")
        return kbs
    except Exception as e:
        print(f"[ERROR] 查询活跃知识库失败：{e}")
        return []


# @transaction.atomic
# def delete_all_kb_links(kb_id: str) -> int:
#     """删除知识库的所有用户关联"""
#     try:
#         count = UserKBLink.objects.filter(knowledge_base_id=kb_id).count()
#         UserKBLink.objects.filter(knowledge_base_id=kb_id).delete()
#         print(f"[INFO] 删除知识库(ID:{kb_id})的所有 {count} 条用户关联")
#         return count
#     except Exception as e:
#         print(f"[ERROR] 删除知识库用户关联失败：{e}")
#         return 0



@transaction.atomic
def delete_all_kb_course_links(kb_id: str) -> int:
    """删除知识库的所有课程关联"""
    try:
        count = CourseKBLink.objects.filter(knowledge_base_id=kb_id).count()
        CourseKBLink.objects.filter(knowledge_base_id=kb_id).delete()
        print(f"[INFO] 删除知识库(ID:{kb_id})的所有 {count} 条课程关联")
        return count
    except Exception as e:
        print(f"[ERROR] 删除知识库课程关联失败：{e}")
        return 0
# # 创建新的数据库操作文件，使用Django ORM
#
# from django.db import transaction
# from django.contrib.auth import get_user_model
# from .models import KnowledgeBase, UserKBLink, CourseKBLink
# from course.models import Course
# import contextlib
# import json
# from django.conf import settings
#
# import os
#
#
#
# User = get_user_model()
#
# '''根据ID查询知识库'''
# def get_knowledge_base_by_id(kb_id: str):
#     """根据ID查询知识库 - Django ORM版本"""
#     try:
#         kb = KnowledgeBase.objects.get(id=kb_id, is_active=1)
#         print(f"[INFO] 查找到知识库：ID={kb.id}, 名称={kb.name}, 文档数={kb.document_count}")
#         return kb
#     except KnowledgeBase.DoesNotExist:
#         print(f"[WARN] 未找到ID为 '{kb_id}' 的知识库")
#         return None
#     except Exception as e:
#         print(f"[ERROR] 查询知识库（ID: {kb_id}）失败：{e}")
#         return None
#
# '''获取用户的所有知识库关联记录'''
# def get_user_kb_links(user_id: int):
#     """获取用户的所有知识库关联记录 - Django ORM版本"""
#     try:
#         links = UserKBLink.objects.filter(
#             user_id=user_id,
#             is_active=True
#         ).select_related('knowledge_base')
#
#         print(f"[INFO] 用户(ID:{user_id})有 {len(links)} 个知识库关联")
#         for link in links:
#             print(f"> 知识库ID: {link.knowledge_base.id}, 授权时间: {link.authorized_at}")
#         return links
#     except Exception as e:
#         print(f"[ERROR] 查询用户关联记录失败：{e}")
#         return []
#
'''获取知识库的所有课程关联记录'''
def get_kb_course_links(kb_id: str):
    """获取知识库的所有课程关联记录 - Django ORM版本"""
    try:
        links = CourseKBLink.objects.filter(
            knowledge_base_id=kb_id,
            is_active=True
        ).select_related('course')

        print(f"[INFO] 知识库(ID:{kb_id})关联了 {len(links)} 个课程")
        for link in links:
            print(f"> 课程ID: {link.course.id}, 关联时间: {link.linked_at}")
        return links
    except Exception as e:
        print(f"[ERROR] 查询知识库课程关联记录失败：{e}")
        return []
#
'''根据ID查询课程'''
def get_course_by_id(course_id: int):
    """根据ID查询课程 - Django ORM版本"""
    try:
        course = Course.objects.get(id=course_id)
        print(f"[INFO] 查找到课程：ID={course.id}, 标题={course.title}")
        return course
    except Course.DoesNotExist:
        print(f"[WARN] 未找到ID为 {course_id} 的课程")
        return None
    except Exception as e:
        print(f"[ERROR] 查询课程（ID: {course_id}）失败：{e}")
        return None
#
# '''新增知识库'''
# @transaction.atomic
# def add_knowledge_base(kb_id: str, name: str, description: str = None, user_id: int = None,
#                        document_count: int = 0, **external_data) -> bool:
#     """新增知识库 - Django ORM版本"""
#     try:
#         # 检查知识库ID是否已存在
#         if KnowledgeBase.objects.filter(id=kb_id).exists():
#             print(f"[WARN] 知识库ID '{kb_id}' 已存在，跳过添加")
#             return False
#
#         # 检查创建者是否存在
#         if user_id and not User.objects.filter(id=user_id).exists():
#             print(f"[WARN] 创建者用户ID {user_id} 不存在")
#             return False
#
#         # 处理parser_config
#         parser_config = external_data.get("parser_config")
#         if isinstance(parser_config, dict):
#             import json
#             parser_config = json.dumps(parser_config, ensure_ascii=False)
#
#         # 创建知识库
#         kb = KnowledgeBase.objects.create(
#             id=kb_id,
#             name=name,
#             description=description,
#             user_id=user_id,
#             document_count=document_count,
#             # 外部API字段
#             avatar=external_data.get("avatar"),
#             chunk_count=external_data.get("chunk_count", 0),
#             chunk_method=external_data.get("chunk_method", "naive"),
#             create_date=external_data.get("create_date"),
#             create_time=external_data.get("create_time"),
#             created_by=external_data.get("created_by"),
#             embedding_model=external_data.get("embedding_model"),
#             language=external_data.get("language", "Chinese"),
#             pagerank=external_data.get("pagerank", 0),
#             parser_config=parser_config,
#             permission=external_data.get("permission", "me"),
#             similarity_threshold=external_data.get("similarity_threshold", 0.2),
#             status=external_data.get("status", "1"),
#             tenant_id=external_data.get("tenant_id"),
#             token_num=external_data.get("token_num", 0),
#             update_date=external_data.get("update_date"),
#             update_time=external_data.get("update_time"),
#             vector_similarity_weight=external_data.get("vector_similarity_weight", 0.3),
#
#         )
#
#         print(f"[INFO] 新增知识库成功：ID={kb_id}, 名称={name}, 文档数={document_count}")
#         return True
#     except Exception as e:
#         print(f"[ERROR] 新增知识库失败：{e}")
#         return False
# @transaction.atomic
# def link_user_kb(user_id: int, kb_id: str) -> bool:
#     """安全关联用户与知识库 - Django ORM版本"""
#     try:
#         # 检查知识库是否存在
#         try:
#             kb = KnowledgeBase.objects.get(id=kb_id, is_active=True)
#         except KnowledgeBase.DoesNotExist:
#             print(f"[WARN] 知识库(ID:{kb_id})不存在或已禁用，无法关联")
#             return False
#
#         # 检查用户是否存在
#         try:
#             user = User.objects.get(id=user_id)
#         except User.DoesNotExist:
#             print(f"[WARN] 用户(ID:{user_id})不存在，无法关联")
#             return False
#
#         # 检查关联是否已存在
#         if UserKBLink.objects.filter(user_id=user_id, knowledge_base_id=kb_id).exists():
#             print(f"[INFO] 用户(ID:{user_id})已关联知识库(ID:{kb_id})，跳过重复关联")
#             return True
#
#         # 创建关联
#         UserKBLink.objects.create(
#             user_id=user_id,
#             knowledge_base_id=kb_id
#         )
#
#         print(f"[INFO] 用户(ID:{user_id})已成功关联知识库(ID:{kb_id})")
#         return True
#
#     except Exception as e:
#         print(f"[ERROR] 关联用户与知识库失败：{e}")
#         return False
# '''添加用户-知识库关联'''
# @transaction.atomic
# def add_user_kb_link(user_id: int, kb_id: str, permission_level: str = 'read',
#                      authorized_by_id: int = None) -> bool:
#     """添加用户-知识库关联"""
#     try:
#         # 检查用户和知识库是否存在
#         if not User.objects.filter(id=user_id).exists():
#             print(f"[WARN] 用户ID {user_id} 不存在")
#             return False
#
#         if not KnowledgeBase.objects.filter(id=kb_id).exists():
#             print(f"[WARN] 知识库ID {kb_id} 不存在")
#             return False
#
#         # 检查是否已存在关联
#         if UserKBLink.objects.filter(user_id=user_id, knowledge_base_id=kb_id).exists():
#             print(f"[WARN] 用户 {user_id} 与知识库 {kb_id} 的关联已存在")
#             return False
#
#         # 创建关联
#         link = UserKBLink.objects.create(
#             user_id=user_id,
#             knowledge_base_id=kb_id,
#             permission_level=permission_level,
#             authorized_by_id=authorized_by_id,
#             is_active=True
#         )
#
#         print(f"[INFO] 用户-知识库关联添加成功: 用户{user_id} -> 知识库{kb_id}")
#         return True
#     except Exception as e:
#         print(f"[ERROR] 添加用户-知识库关联失败：{e}")
#         return False
#
# '''添加课程-知识库关联'''
# @transaction.atomic
# def add_course_kb_link(course_id: int, kb_id: str, usage_type: str = 'teaching',
#                        priority: int = 0, linked_by_id: int = None) -> bool:
#     """添加课程-知识库关联"""
#     try:
#         # 检查课程和知识库是否存在
#         if not Course.objects.filter(id=course_id).exists():
#             print(f"[WARN] 课程ID {course_id} 不存在")
#             return False
#
#         if not KnowledgeBase.objects.filter(id=kb_id).exists():
#             print(f"[WARN] 知识库ID {kb_id} 不存在")
#             return False
#
#         # 检查是否已存在关联
#         if CourseKBLink.objects.filter(course_id=course_id, knowledge_base_id=kb_id).exists():
#             print(f"[WARN] 课程 {course_id} 与知识库 {kb_id} 的关联已存在")
#             return False
#
#         # 创建关联
#         link = CourseKBLink.objects.create(
#             course_id=course_id,
#             knowledge_base_id=kb_id,
#             usage_type=usage_type,
#             priority=priority,
#             linked_by_id=linked_by_id,
#             is_active=True
#         )
#
#         print(f"[INFO] 课程-知识库关联添加成功: 课程{course_id} -> 知识库{kb_id}")
#         return True
#     except Exception as e:
#         print(f"[ERROR] 添加课程-知识库关联失败：{e}")
#         return False
#
# '''更新知识库信息'''
# @transaction.atomic
# def update_knowledge_base(kb_id: str, **update_fields) -> bool:
#     """更新知识库信息"""
#     try:
#         kb = KnowledgeBase.objects.filter(id=kb_id).first()
#         if not kb:
#             print(f"[WARN] 知识库ID {kb_id} 不存在")
#             return False
#
#         # 更新字段
#         for field, value in update_fields.items():
#             if hasattr(kb, field) and value is not None:
#                 setattr(kb, field, value)
#
#         kb.save()
#         print(f"[INFO] 知识库 {kb_id} 更新成功")
#         return True
#     except Exception as e:
#         print(f"[ERROR] 更新知识库失败：{e}")
#         return False
#
# '''删除知识库（软删除或硬删除）'''
# @transaction.atomic
# def delete_knowledge_base(kb_id: str, force: bool = False) -> bool:
#     """删除知识库（软删除或硬删除）"""
#     try:
#         kb = KnowledgeBase.objects.filter(id=kb_id).first()
#         if not kb:
#             print(f"[WARN] 知识库ID {kb_id} 不存在")
#             return False
#
#         if force:
#             # 硬删除
#             kb.delete()
#             print(f"[INFO] 知识库 {kb_id} 已彻底删除")
#         else:
#             # 软删除
#             kb.is_active = 0
#             kb.save()
#             print(f"[INFO] 知识库 {kb_id} 已禁用（软删除）")
#
#         return True
#     except Exception as e:
#         print(f"[ERROR] 删除知识库失败：{e}")
#         return False
#
#
# '''删除知识库的所有用户关联'''
#
#
@transaction.atomic
def delete_all_kb_links(kb_id: str) -> int:
    """删除知识库的所有用户关联"""
    try:
        deleted_count = KnowledgeBase.objects.filter(knowledge_base_id=kb_id).delete()[0]
        print(f"[INFO] 删除知识库(ID:{kb_id})的所有 {deleted_count} 条用户关联")
        return deleted_count
    except Exception as e:
        print(f"[ERROR] 删除知识库用户关联失败：{e}")
        return 0
#
#
# '''删除知识库的所有课程关联'''
#
#
# @transaction.atomic
# def delete_all_kb_course_links(kb_id: str) -> int:
#     """删除知识库的所有课程关联"""
#     try:
#         deleted_count = CourseKBLink.objects.filter(knowledge_base_id=kb_id).delete()[0]
#         print(f"[INFO] 删除知识库(ID:{kb_id})的所有 {deleted_count} 条课程关联")
#         return deleted_count
#     except Exception as e:
#         print(f"[ERROR] 删除知识库课程关联失败：{e}")
#         return 0
#
#
# '''删除单个用户-知识库关联'''
#
#
# @transaction.atomic
# def delete_user_kb_link(user_id: int, kb_id: str) -> bool:
#     """删除单个用户-知识库关联"""
#     try:
#         deleted_count = UserKBLink.objects.filter(
#             user_id=user_id,
#             knowledge_base_id=kb_id
#         ).delete()[0]
#
#         if deleted_count > 0:
#             print(f"[INFO] 删除用户-知识库关联成功：用户ID={user_id}, 知识库ID={kb_id}")
#             return True
#         else:
#             print(f"[WARN] 未找到用户ID={user_id}和知识库ID={kb_id}的关联记录")
#             return False
#     except Exception as e:
#         print(f"[ERROR] 删除用户-知识库关联失败：{e}")
#         return False
def get_user_knowledge_bases_origin(user_id: int):
    """直接从知识库表获取用户的知识库"""
    try:
        kbs = KnowledgeBase.objects.filter(
            user_id=user_id,
            is_active=True
        ).order_by('-created_at')

        print(f"[INFO] 用户(ID:{user_id})拥有 {len(kbs)} 个知识库")
        return kbs
    except Exception as e:
        print(f"[ERROR] 查询用户知识库失败：{e}")
        return []
#
def get_user_knowledge_bases(user):
    user_id=user.id
    """判断用户是否拥有知识库，创建者，老师，学生"""
    try:
        kbs = KnowledgeBase.objects.filter(
            is_active=True
        ).order_by('-created_at')
        g_names= [group.name for group in  user.groups.all()]
        if '管理员' in g_names:
            return kbs
        create_q=Q(user=user_id) # 知识库的创建者是该用户
        tea_q = Q(
            course_kb_links__course__course_semester__teachers__teacher_id=user_id
        )
        #tea_q=Q(course_kb_links__course__teachers__teacher=user_id)# 跨表查询：知识库 -> 课程知识库关联 -> 课程 -> 课程教师关联 -> 教师
        share_q=Q(course_kb_links__course__course_semester__classes__members__student=user_id)# 跨表查询：知识库 -> 课程知识库关联 -> 课程 -> 课程学期 -> 班级 -> 班级成员 -> 学生
        #union对象权限列表中有查看权限的对象,支持后期对知识库对象单独授权时，注意是union，也表明了只能在固定规则上增加能控制的对象的范围，而不能减少
        object_pk=[UserObjectPermission.objects.filter(user=user,permission__codename='view_knowledgebases').values_list('object_pk',flat=True)]
        object_perm_q=Q(id__in=object_pk)
        kbs=kbs.filter(create_q|share_q|tea_q|object_perm_q).distinct()
        print(f"[INFO] 用户(ID:{user_id})拥有 {len(kbs)} 个知识库")
        return kbs
    except Exception as e:
        print(f"[ERROR] 查询用户知识库失败：{e}")
        return []

def get_user_kb_ids(user_id: int):
    """获取用户的知识库ID列表"""
    try:
        kb_ids = KnowledgeBase.objects.filter(
            user_id=user_id,
            is_active=True
        ).values_list('id', flat=True)

        return list(kb_ids)
    except Exception as e:
        print(f"[ERROR] 获取用户知识库ID失败：{e}")
        return []

'''删除单个课程-知识库关联'''


@transaction.atomic
def delete_course_kb_link(course_id: int, kb_id: str) -> bool:
    """删除单个课程-知识库关联"""
    try:
        deleted_count = CourseKBLink.objects.filter(
            course_id=course_id,
            knowledge_base_id=kb_id
        ).delete()[0]

        if deleted_count > 0:
            print(f"[INFO] 删除课程-知识库关联成功：课程ID={course_id}, 知识库ID={kb_id}")
            return True
        else:
            print(f"[WARN] 未找到课程ID={course_id}和知识库ID={kb_id}的关联记录")
            return False
    except Exception as e:
        print(f"[ERROR] 删除课程-知识库关联失败：{e}")
        return False


'''级联删除知识库及其所有关联'''


@transaction.atomic
def delete_knowledge_base_cascade(kb_id: str, force: bool = False) -> dict:
    """级联删除知识库及其所有关联"""
    try:
        result = {
            "kb_deleted": False,
            "deleted_user_links": 0,
            "deleted_course_links": 0,
            "errors": []
        }

        # 1. 删除所有用户关联
        try:
            result["deleted_user_links"] = delete_all_kb_links(kb_id)
        except Exception as e:
            result["errors"].append(f"删除用户关联失败: {e}")

        # 2. 删除所有课程关联
        try:
            result["deleted_course_links"] = delete_all_kb_course_links(kb_id)
        except Exception as e:
            result["errors"].append(f"删除课程关联失败: {e}")

        # 3. 删除知识库本身
        try:
            result["kb_deleted"] = delete_knowledge_base(kb_id, force=force)
        except Exception as e:
            result["errors"].append(f"删除知识库失败: {e}")

        print(f"[INFO] 级联删除知识库(ID:{kb_id})完成 - "
              f"知识库:{result['kb_deleted']}, "
              f"用户关联:{result['deleted_user_links']}, "
              f"课程关联:{result['deleted_course_links']}")

        return result

    except Exception as e:
        print(f"[ERROR] 级联删除知识库失败：{e}")
        return {
            "kb_deleted": False,
            "deleted_user_links": 0,
            "deleted_course_links": 0,
            "errors": [str(e)]
        }

#
# '''获取所有活跃知识库'''
#
#
# def get_all_active_knowledge_bases():
#     """获取所有活跃知识库"""
#     try:
#         kbs = KnowledgeBase.objects.filter(is_active=1)
#         print(f"[INFO] 共找到 {len(kbs)} 个活跃知识库")
#         return kbs
#     except Exception as e:
#         print(f"[ERROR] 查询活跃知识库失败：{e}")
#         return []
#
#
'''批量删除知识库及其关联'''


@transaction.atomic
def batch_delete_knowledge_bases_cascade(kb_ids: list[str], force: bool = False) -> dict:
    """批量级联删除知识库及其关联"""
    try:
        total_result = {
            "total_processed": 0,
            "successful_deletions": 0,
            "failed_deletions": 0,
            "total_deleted_user_links": 0,
            "total_deleted_course_links": 0,
            "errors": [],
            "details": []
        }

        for kb_id in kb_ids:
            try:
                result = delete_knowledge_base_cascade(kb_id, force=force)
                total_result["total_processed"] += 1

                if result["kb_deleted"]:
                    total_result["successful_deletions"] += 1
                else:
                    total_result["failed_deletions"] += 1

                total_result["total_deleted_user_links"] += result["deleted_user_links"]
                total_result["total_deleted_course_links"] += result["deleted_course_links"]
                total_result["errors"].extend(result["errors"])

                total_result["details"].append({
                    "kb_id": kb_id,
                    "result": result
                })

            except Exception as e:
                total_result["total_processed"] += 1
                total_result["failed_deletions"] += 1
                error_msg = f"处理知识库 {kb_id} 时异常: {e}"
                total_result["errors"].append(error_msg)
                print(f"[ERROR] {error_msg}")

        print(f"[INFO] 批量删除完成 - 总计:{total_result['total_processed']}, "
              f"成功:{total_result['successful_deletions']}, "
              f"失败:{total_result['failed_deletions']}")

        return total_result

    except Exception as e:
        print(f"[ERROR] 批量删除知识库失败：{e}")
        return {
            "total_processed": 0,
            "successful_deletions": 0,
            "failed_deletions": len(kb_ids),
            "total_deleted_user_links": 0,
            "total_deleted_course_links": 0,
            "errors": [str(e)],
            "details": []
        }

'''获取所有知识库'''
def get_all_knowledge_bases(include_inactive: bool = False):
    """获取所有知识库"""
    try:
        if include_inactive:
            kbs = KnowledgeBase.objects.all()
        else:
            kbs = KnowledgeBase.objects.filter(is_active=1)

        print(f"[INFO] 共找到 {len(kbs)} 个知识库")
        for kb in kbs:
            print(f"> ID: {kb.id}, 名称: {kb.name}, 状态: {kb.is_active}")
        return kbs
    except Exception as e:
        print(f"[ERROR] 查询所有知识库失败：{e}")
        return []


# 批量查询优化版本
'''批量获取知识库的课程信息'''
def get_kb_courses_batch(kb_ids: list[str]) -> dict[str, list[dict]]:
    """批量获取知识库的课程信息"""
    try:
        # 一次查询获取所有知识库的课程关联
        links = CourseKBLink.objects.filter(
            knowledge_base_id__in=kb_ids,
            is_active=True
        ).select_related('course', 'knowledge_base')

        # 按知识库ID分组
        kb_courses = {kb_id: [] for kb_id in kb_ids}

        for link in links:
            kb_id = link.knowledge_base_id
            course_info = {
                "course_id": link.course.id,
                "course_code": getattr(link.course, 'course_code', ''),
                "course_name": link.course.title,
                "description": link.course.description,
                "linked_at": link.linked_at.isoformat() if link.linked_at else None,
                "usage_type": link.usage_type,
                "priority": link.priority,
            }
            kb_courses[kb_id].append(course_info)

        return kb_courses

    except Exception as e:
        print(f"[ERROR] 批量获取知识库课程信息失败: {e}")
        return {kb_id: [] for kb_id in kb_ids}


# # 在文件末尾添加测试函数
# def run_tests():
#     """运行测试"""
#     print("开始测试...")
#
#     # 测试基本操作
#     kb = get_knowledge_base_by_id("test_kb_001")
#     print(f"查询结果: {kb}")
#
#     # 添加更多测试...
#
#
# if __name__ == "__main__":
#     run_tests()