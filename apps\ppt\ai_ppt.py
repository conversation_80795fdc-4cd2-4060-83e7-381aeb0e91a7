"""
AI 生成PPT服务
"""

import json
import re

from django.conf import settings
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END, START
from langchain_core.runnables import Runnable
from langchain_core.messages import BaseMessage, HumanMessage
from typing import TypedDict, List, Dict, Any, Generator, Optional


class PPTState(TypedDict):
    """PPT生成流程状态结构"""

    outline: str  # PPT大纲
    content: List[Dict[str, Any]]  # PPT内容列表
    slide_count: int  # 幻灯片总数量
    current_slide: int  # 当前幻灯片索引
    slide_types: List[str]  # 幻灯片类型列表
    last_generated: Optional[Dict[str, Any]]  # 上次生成的幻灯片内容


class AIPPTService:
    """AI PPT服务"""

    _instance = None

    def __new__(cls):
        """
        单例模式实现
        """
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.llm = ChatOpenAI(
                model=settings.LLM_NAME,
                openai_api_key=settings.LLM_API_KEY,
                openai_api_base=settings.LLM_BASE_URL_LESSON,
                max_tokens=8000,
                temperature=1,
                streaming=True,  # 启用流式响应
            )

        return cls._instance

    # --------------------核心AI服务方法--------------------
    def generate_ppt_stream(
        self, outline: str
    ) -> Generator[Dict[str, Any], None, None]:
        """
        PPT内容流式生成 - 主入口
        返回生成器，每次生成一页PPT内容
        """
        initial_state = PPTState(
            outline=outline,
            content=[],
            slide_count=0,
            current_slide=0,
            slide_types=[],
            last_generated=None,
        )

        # 构建工作流
        workflow = self.build_generating_flow()

        # 流式执行工作流
        for step in workflow.stream(initial_state):
            # 获取当前状态
            current_state = next(iter(step.values()))

            # 如果有新生成的幻灯片，返回给调用方
            if current_state.get("last_generated"):
                yield current_state["last_generated"]

                # 重置last_generated，避免重复发送
                current_state["last_generated"] = None

        # 返回最终结果
        # yield {
        #     "type": "final",
        #     "data": {
        #         "outline": outline,
        #         "content": current_state["content"],
        #         "slide_count": current_state["slide_count"],
        #         "total_slides": len(current_state["content"])
        #     }
        # }

    def build_generating_flow(self) -> Runnable:
        """
        构建PPT内容生成流程图
        """
        builder = StateGraph(PPTState)

        # 添加节点
        builder.add_node("InitialCount", self._counting_node)
        builder.add_node("ContentGeneration", self._content_generation_node)
        # 设置流程路径
        builder.add_edge(START, "InitialCount")
        builder.add_edge("InitialCount", "ContentGeneration")

        # 添加条件循环
        builder.add_conditional_edges(
            "ContentGeneration",
            self._should_continue,
            {
                "continue": "ContentGeneration",  # 继续生成下一张幻灯片
                "end": END,  # 结束流程
            },
        )

        return builder.compile()

    # --------------------流程节点--------------------
    def _counting_node(self, state: PPTState) -> PPTState:
        """幻灯片数量流程节点"""
        prompt = f"""
        /no_think
        ### 任务：
        根据以下大纲内容，分析并确定PPT幻灯片的总数量和每张幻灯片的类型（按顺序）：
        
        ### 大纲：
        {state["outline"]}
        
        ### 要求：
        1. 考虑大纲内容的深度和广度，合理评估所需幻灯片数量
        2. 必须包含以下类型的幻灯片：
           - cover: 封面页（必须放在第一张）
           - contents: 目录页（必须放在第二张）
           - transition: 过渡页（至少需要2张，放在目录页之后，内容页之间）
           - content: 内容页（占总数的70%左右）
           - end: 结束页 (必须放在最后一张)
        3. 总幻灯片数量应在8-30张之间
        4. 返回JSON格式结果，包含两个字段
           - slide_count: 幻灯片的总页数（整数）
           - slide_types: 一个列表，按顺序列出每张幻灯片的类型（例如：["cover", "contents", "transition", "content", ..., "end"]）
        
        ### 返回格式示例：
        {{
            "slide_count": 12,
            "slide_types": [
                "cover",
                "contents",
                "transition",
                "content",
                "content",
                "content",
                "transition",
                "content",
                "content",
                "content",
                "content",
                "end",
            ]
        }}
        """

        try:
            response = self._call_ai(prompt)
            print(f"幻灯片数量和类型response:{response}")
            data = self._extract_json(response)

            # 确保slide_count是数值类型
            slide_count = self._ensure_int(data.get("slide_count", 10))
            slide_types = data.get("slide_types", [])

            # 验证并修正类型列表
            if not slide_types or len(slide_types) != slide_count:
                # 如果类型列表无效，重新生成
                slide_types = self._generate_default_types(slide_count)
            else:
                # 确保第一个是封面页，最后是结束页
                if slide_types[0] != "cover":
                    slide_types[0] = "cover"
                if slide_types[-1] != "end":
                    slide_types[-1] = "end"

            # 确保数量在合理范围内
            slide_count = len(slide_types)
            slide_count = max(8, min(30, slide_count))

            return {
                **state,
                "slide_types": slide_types,
                "slide_count": slide_count,
            }

        except Exception as e:
            print(f"幻灯片数量节点错误: {str(e)}")
            # 发生错误时，返回默认值

            return {
                **state,
                "slide_types": self._generate_default_types(12),
                "slide_count": 12,
            }
        
    def _generate_default_types(self, count: int) -> list:
        """生成默认幻灯片类型"""
        types = ["cover", "contents"]


        # 计算内容页数量（总数减去固定页）
        content_count = max(0, count - 5)

        # 添加过渡页和内容页
        types.append("transition")
        types.append("transition")
        types.append(["content"] * content_count)
        types.append("end")

        # 确保总数正确
        if len(types) > count:
            types = types[:count]
            types[-1] = "end"
        elif len(types) < count:
            # 在内容页中插入更多内容页
            extra_count = count - len(types)
            # 在第一个过渡页后插入额外内容页
            insert_index = types.index("transition") + 1 if "transition" in types else 2
            types[insert_index:insert_index] = ["content"] * extra_count
        
        return types

    def _content_generation_node(self, state: PPTState) -> PPTState:
        """幻灯片内容生成 循环流程节点"""
        if state.get("error"):
            return state
        # 安全获取当前索引
        current_idx = state.get("current_slide", 0)

        # 确保索引在有效范围内
        if current_idx >= len(state.get("slide_types", [])):
            # 如果索引超出范围，结束流程
            return {
                **state,
                "error": f"索引越界：{current_idx}/{len(state.get('slide_types', []))}",
                "last_generated": {
                    "type":"end",
                    "data": {
                        "title": "幻灯片索引超出范围"
                    }
                }
            }
        slide_type = state["slide_types"][current_idx]
        previous_content = state["content"][-1] if state["content"] else None
        # print(f"之前生成的幻灯片内容参考:{previous_content}")

        prompt = f"""
        /no_think
        ### 任务：
        根据给定的大纲和当前幻灯片类型，生成一张PPT幻灯片的内容。在生成过程中，要确保过渡页和内容页的分配更加合理，
        以增强PPT整体逻辑的连贯性和流畅性。

        ### 大纲：
        {state["outline"]}

        ### 当前幻灯片信息：
        - 幻灯片序号: {current_idx + 1}/{state["slide_count"]}
        - 幻灯片类型: {slide_type}

        ### 所有已生成幻灯片内容汇总（参考）:
        {json.dumps(previous_content, ensure_ascii=False, indent=2) if previous_content else "无"}

        ### 生成要求：
        1. 根据幻灯片类型生成对应内容：
           - cover: 封面页，包含标题和副标题
           - contents: 目录页，包含目录列表
           - transition: 过渡页，包含章节标题和简介
           - content: 内容页，包含标题和多个内容点
           - end: 结束页，结束语
        2. 内容页丰富全面，内容页要根据大纲内容进行生成
        3. 与上一张幻灯片内容保持逻辑连贯
        4. 返回格式必须是严格的JSON对象
        5. 幻灯片类型的分配要合理，过渡页要严格遵循目录页生成的目录条数与之对应，内容页要根据大纲内容进行生成，过渡页要在合适的内容页进行穿插并与内容对应

        ### 返回格式示例，请严格遵循以下的格式示例：
        1. 封面页格式示例，生成的数据严格按照以下格式：
        {{
            "type": "cover",
            "data": {{
                "title": "主标题",
                "text": "副标题"
            }}
        }}
        2. 目录页格式示例，生成的数据严格按照以下格式：
        {{
            "type": "contents",
            "data": {{
                "items": [
                "职业生涯规划概述",
                "自我认知与职业兴趣",
                "职业环境分析",
                "职业目标设定",
                "职业规划实施",
                "职业规划评估与调整",
                "职业生涯规划案例分享"
                ]
            }}
        }}
        3. 过渡页格式示例，生成的数据严格按照以下格式：
        {{
            "type": "transition",
            "data": {{
                "title": "过渡标题",
                "text": "过渡内容"
            }}
        }}
        4. 内容页格式示例，如果内容只有一个内容点，也需要用"items"来进行包裹，不要直接返回items内部的title和text，生成的数据严格按照以下格式：
        {{
            "type": "content",
            "data": {{
                "title": "内容页标题",
                "items": [
                    {{
                        "title": "内容1",
                        "text": "内容1的详细内容"
                    }},
                    {{
                        "title": "内容2",
                        "text": "内容2的详细内容"
                    }}
                ]
            }}
        }}
        5. 结束页格式示例，生成的数据严格按照以下格式：
        {{
            "type": "end"
        }}

        """
        try:
            # 循环内部非流式：直接完整调用AI生成单页内容（不使用llm.stream）
            response = self._call_ai(prompt)
            slide_data = self._extract_json(response)
            print(f"生成的单页PPT内容:{slide_data}")

            # 验证生成的幻灯片类型
            if slide_data.get("type") != slide_type:
                slide_data["type"] = slide_type

            # 添加幻灯片序号信息
            slide_data["slide_number"] = current_idx + 1
            slide_data["total_slide"] = state["slide_count"]

            # 更新状态
            new_content = state["content"] + [slide_data]

            return {
                **state,
                "content": new_content,
                "current_slide": current_idx + 1,
                "last_generated": slide_data,  # 单页生成完成，准备流式返回
            }

        except Exception as e:
            print(f"内容生成节点错误: {str(e)}")
            # 创建空幻灯片占位
            error_slide = {
                "type": slide_type,
                "data": {"title": f"幻灯片 {current_idx + 1}", "text": "内容生成失败"},
                "slide_number": current_idx + 1,
                "total_slide": state["slide_count"],
            }
            return {
                **state,
                "content": state["content"] + [error_slide],
                "current_slide": current_idx + 1,
                "last_generated": error_slide,
            }

    def _should_continue(self, state: PPTState) -> bool:
        """判断是否继续生成下一个幻灯片"""
        if state["current_slide"] >= state["slide_count"]:
            return "end"

        # 检查是否有错误
        if state.get("error"):
            return "end"

        return "continue"

    def _call_ai(self, prompt):
        """调用AI的统一接口"""
        try:
            response = self.llm.invoke(prompt)
            return response.content
        except Exception as e:
            return json.dumps(
                {
                    "error": f"调用AI接口失败: {str(e)}",
                }
            )

    def _extract_json(self, text):
        """从文本提取JSON内容"""
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            match = re.search(r"\{.*\}", text, re.DOTALL)
            if match:
                return json.loads(match.group())
            raise ValueError("无法解析JSON响应")

    def _ensure_int(self, value):
        """确保值为整数类型"""
        if isinstance(value, int):
            return value
        try:
            return int(value)
        except (ValueError, TypeError):
            print(f"无法将值转换为整数: {value}")
            return 0
