import json
import re
import requests
import time

from docx import Document
from django.http import JsonResponse


def api_response(code, message, data=None, status=200):
    """
    封装 API 返回格式
    :param code: 状态码（自定义业务状态码）
    :param message: 返回信息
    :param data: 返回数据
    :param status: HTTP 状态码
    :return: JsonResponse
    """
    response_data = {
        'code': code,
        'message': message,
        'data': data if data is not None else {}
    }
    return JsonResponse(response_data, status=status)


def call_llm_retry(url, json_data, max_retries=5, retry_delay=1):
    """
    调用大模型API重试请求方法
    :param url: 大模型 API 地址
    :param json_data: 请求数据
    :param max_retries: 最大重试次数
    :param retry_delay: 重试延迟（秒）
    :return: 大模型返回的结果或错误信息
    """
    retries = 0
    while retries < max_retries:
        try:
            # 发送请求
            response = requests.post(url, json=json_data)
            response.raise_for_status()  # 检查 HTTP 错误
            return response.json()  # 返回解析后的 JSON 数据

        except requests.RequestException as e:
            print(f"请求失败，重试中... ({retries + 1}/{max_retries})")
            retries += 1
            if retries < max_retries:
                time.sleep(retry_delay)
            else:
                raise Exception(f"请求失败，重试次数用尽: {str(e)}")

def generate_stream_outline(url, json_data, output_format):
    """
    发送流式POST请求并生成流式响应大纲
    :param url: 请求的URL
    :param json_data: 要发送的JSON数据
    :param output_format: 输出格式，可选值为'json'、'text'、'content'
    :return: 生成的流式响应内容
    """
    try:
        # 发送流式 POST 请求
        session = requests.Session()
        response = session.post(url, json=json_data, headers={'Content-Type': 'application/json'}, stream=True)
        response.raise_for_status()
        print(response)
        for raw_line in response.iter_lines():
            if raw_line:
                try:
                    line = raw_line.decode('utf-8').strip()  # 解码字节流
                    if not line:
                        continue
                    chunk = json.loads(line)  # 解析JSON数据

                    if output_format == 'json':  # 输出为JSON格式
                        yield chunk # 返回完整的json对象
                    elif output_format == 'content':
                        chunk = json.loads(line)
                        content = chunk.get('message', {}).get('content', '')  # 提取message下的content输出
                        if content:
                            yield content # 返回message.content字段
                    elif output_format == 'text':  # 默认返回整个文本
                        yield line # 返回完整的文本
                    else:
                        raise ValueError(f"Invalid output format: {output_format}")
                except json.JSONDecodeError as e:
                    print(f"Json decode error: {e}")
                    continue
    except requests.RequestException as e:
        yield f"Error: {str(e)}"


def generate_stream_content(response_data):
    """
    生成流式响应内容
    :param response_data: 响应数据
    :return: 生成的流式响应内容
    """
    llm_response = _extract_json(response_data)
    print(f"llm_response:{llm_response}")
    # 遍历 llm_response 字典中 "data" 键对应的列表
    for item in llm_response["data"]:
        # 将每个元素转换为 JSON 字符串并添加换行符
        json_str = json.dumps(item, ensure_ascii=False)  # item 转换为 JSON 字符串，确保非 ASCII 字符正确编码
        sse_data = f"{json_str}\n\n"  # 添加换行符
        print(f"sse_data:{sse_data}") # 打印响应内容
        # time.sleep(1)  # 延时处理
        yield sse_data.encode('utf-8') # 将json字符串编码为 UTF-8 字节流

def _extract_json(text):
        """从文本提取JSON内容"""
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            match = re.search(r"\{.*\}", text, re.DOTALL)
            if match:
                return json.loads(match.group())
            raise ValueError("无法解析JSON响应")

def upload_file_docx(document):
    doc = Document(document)
    # full_text = '\n'.join([para.text for para in doc.paragraphs])
    structured_content = []
    # 标题级别和对应的文本内容
    heading_levels = {
        'Heading 1': 1, '标题 1': 1,
        'Heading 2': 2, '标题 2': 2,
        'Heading 3': 3, '标题 3': 3,
        'Heading 4': 4, '标题 4': 4,
    }
    for para in doc.paragraphs:
        text = para.text.strip()
        if not text: # 跳过空行
            continue
        # 获取段落样式名称
        style_name = para.style.name if para.style else None
        # 检查是否为标题
        if style_name and style_name in heading_levels: 
            level = heading_levels[style_name]
            structured_content.append({
                'type': str(level) + '级标题',
                'text': text
            })
        else: # 正文
            structured_content.append({
                'type': '正文',
                'text': text
            })
    # 将结构化内容转换为Markdown格式的字符串
    if structured_content:
        document_content = "\n\n".join([
            f"# {item['text']}" if item['type'] == '1级标题' else
            f"## {item['text']}" if item['type'] == '2级标题' else
            f"### {item['text']}" if item['type'] == '3级标题' else
            f"#### {item['text']}" if item['type'] == '4级标题' else
            f"{item['text']}"
            for item in structured_content
        ])      
    return document_content