from django.db import models
from django.utils import timezone

from exam.models import KnowledgeExam
from user.models import UserInfo
from course.models import CourseSemester



# Create your models here.
class ExamAnalysis(models.Model):
    user = models.ForeignKey(
        UserInfo,
        on_delete=models.CASCADE,
        verbose_name='用户ID'
    )  # 关联到用户模型的外键id
    exam_id = models.ForeignKey(
        KnowledgeExam,
        on_delete=models.CASCADE,
        verbose_name='考试ID'
    )  # 关联到考试模型的外键id
    course_semester = models.ForeignKey(
        CourseSemester,
        on_delete=models.CASCADE,
        verbose_name='课程学期',
        null=True,
        blank=True
    )  # 关联到课程学期关联表的主键id
    analysis_score = models.FloatField(verbose_name='总分数', default=0, null=True, blank=True)  # 对应考试分数total_score
    analysis_exam = models.TextField(verbose_name='考试分析', null=True, blank=True)  # 每位学生单独的考试分析
    abstract_exam = models.TextField(verbose_name='考试分析摘要', null=True, blank=True)  # 每位学生单独的考试分析摘要
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    is_exam = models.BooleanField(verbose_name='是否为考试', default=True)  # 区分考试/作业

    

    class Meta:
        db_table = 'exam_analysis'  # 数据库表名
        verbose_name = '考试分析'  # 模型名称
        verbose_name_plural = verbose_name  # 模型复数名称

class CourseAnalysis(models.Model):
    user = models.ForeignKey(
        UserInfo,
        on_delete=models.CASCADE,
        verbose_name='用户ID',
        null=True,
        blank=True
    )  # 关联到用户模型的外键id
    course_semester = models.ForeignKey(
        CourseSemester,
        on_delete=models.CASCADE,
        verbose_name='课程学期',
        null=True,
        blank=True
    )  # 关联到课程学期关联表的主键id
    analysis_status = models.CharField(max_length=20, verbose_name='课程学习状态', null=True, blank=True)  # 每位同学的学习状态分析
    learn_progress = models.TextField(verbose_name='学习进度', null=True, blank=True)  # 每位学生单独的学习进度分析
    homework_situation = models.TextField(verbose_name='作业情况', null=True, blank=True)  # 每位学生单独的作业情况分析
    exam_grade = models.TextField(verbose_name='考试成绩', null=True, blank=True)  # 每位学生单独的考试成绩分析
    course_comment = models.TextField(verbose_name='课程评价', null=True, blank=True)  # 每位学生单独的课程评价分析
    abstract_course = models.TextField(verbose_name='课程分析摘要', null=True, blank=True)  # 每位学生单独的课程分析摘要（预留）
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    

    class Meta:
        db_table = 'course_analysis'  # 数据库表名
        verbose_name = '课程分析'  # 模型名称
        verbose_name_plural = verbose_name  # 模型复数名称
