"""
考试/作业 AI 学情分析服务
"""
import json
import re

from django.conf import settings
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END, START
from typing import Any, Dict, List, TypedDict
from .models import *

class AnalysisState(TypedDict):
    """考试/作业学情分析流程状态结构"""
    class_name: str  # 班级名称
    exam_name: str  # 考试/
    course_name: str  # 课程名称
    summary_class_exam: str  # 以班级为单位 学生的考试/作业情况分析摘要汇总
    summary_classes_exam: str  # 课程下某次作业/考试 班级的考试/作业情况分析摘要汇总
    analysis_exams: str  # 以班级为单位 考试/作业学情分析结果
    analysis_course_exams: str  # 以班级为单位 班级的考试/作业 整体学情分析结果
    


class AIClassAnalysisService:
    """考试/作业AI学情分析服务【班级、课程】"""

    _instance = None

    def __new__(cls):
        """
        单例模式实现
        """
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.llm = ChatOpenAI(
                model=settings.LLM_NAME,
                openai_api_key=settings.LLM_API_KEY,
                openai_api_base=settings.LLM_BASE_URL_LESSON
            )

        return cls._instance

    
    # -------------------- 核心 AI 服务方法 --------------------
    def analyze_class_exam(self, state):
        """
        以班级为单位 考试/作业学情分析-主入口
        """
        #  考试/作业学情分析
        initial_state = AnalysisState(
            class_name=state.get('class_name', ''),
            exam_name=state.get('exam_name', ''),
            course_name=state.get('course_name', ''),
            summary_class_exam=state.get('summary_class_exam', ''),
            summary_classes_exam=state.get('summary_classes_exam', ''),
            analysis_exams='',
            analysis_course_exams='',
        )

        workflow = self.build_analyzing_flow(is_course_level=not initial_state['class_name'])
        final_state = workflow.invoke(initial_state)

        return {
            'analysis_exams': final_state['analysis_exams'],
            'analysis_course_exams': final_state['analysis_course_exams'],
        }


    def build_analyzing_flow(self, is_course_level=False):
        """
        构建学情分析流程图
        :params is_course_level: 是否以课程为单位分析
        """
        builder = StateGraph(AnalysisState)

        # 添加节点
        builder.add_node("ClassExamAnalysis", self._class_exam_analysis)
        builder.add_node("ClassesExamAnalysis", self._classes_exam_analysis)
        
        # 设置流程路径
        builder.add_edge(START, "ClassExamAnalysis")
        builder.add_edge("ClassExamAnalysis", "ClassesExamAnalysis")
        builder.add_edge("ClassesExamAnalysis", END)

        return builder.compile()
    
    # -------------------- 流程节点 --------------------
    def _class_exam_analysis(self, state):
        """班级 考试/作业学情分析流程节点"""
        prompt = f"""
        /no_think
        你是一个专业的班级考试/作业学情分析助手，根据班级的考试/作业情况，分析班级的考试/作业情况和成绩。
        请根据以下信息，分析班级的考试/作业情况和成绩：
        1. 班级名称：{state['class_name']}
        2. 考试/作业名称：{state['exam_name']}
        3. 课程名称：{state['course_name']}
        4. 以班级为单位的每位学生考试/作业情况分析摘要：{state['summary_class_exam']}
        请根据以上信息，分析班级中对应某次考试/作业的情况。
        请返回JSON格式的分析结果
        {{
        "analysis_exams": "班级的考试/作业的整体情况分析"
        }}
        """
        try:
            response = self._call_ai(prompt)
            data = self._extract_json(response)

            return {
                **state,
                "analysis_exams": data["analysis_exams"],
                "analysis_course_exams": state["analysis_course_exams"],  # 保持原值
            }
        
        except Exception as e:
            return  {
                **state,
                "analysis_exams": "作业整体情况分析：继续加油",
            }
        
    def _classes_exam_analysis(self, state):
        """班级 考试/作业学情分析流程节点"""

        prompt = f"""
        /no_think
        你是一个专业的班级考试/作业学情分析助手，根据课程下面每个班级的考试/作业情况，分析班级的考试/作业情况和成绩。
        请根据以下信息，分析班级中所有考试/作业的情况。
        1. 课程名称：{state['course_name']}
        2. 考试/作业名称：{state['exam_name']}
        3. 每个班级的考试/作业情况分析摘要汇总：{state['summary_classes_exam']}
        请根据以上信息，分析{state['course_name']}中所有班级的考试/作业的情况。
        请返回JSON格式的分析结果
        {{
        "analysis_course_exams": "某个课程下所有班级的某次考试/作业的整体情况分析"

        }}
        """
        try:
            response = self._call_ai(prompt)
            data = self._extract_json(response)
            return {
                **state,
                "analysis_course_exams": data["analysis_course_exams"],
                "analysis_exams": state["analysis_exams"]  # 保持原值
            }
        except Exception as e:
            return  {
                **state,
                "analysis_course_exams": "课程下的整体作业分析：整体学习情况良好，继续保持",
            }
            
    def _call_ai(self, prompt):
        """调用AI的统一接口"""
        try:
            response = self.llm.invoke(prompt)
            return response.content
        except Exception as e:
            return json.dumps({
                "error": f"调用AI接口失败: {str(e)}",
            })

    def _extract_json(self, text):
        """从文本提取JSON内容"""
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            match = re.search(r"\{.*\}", text, re.DOTALL)
            if match:
                return json.loads(match.group())
            raise ValueError("无法解析JSON响应")
        

        
        
