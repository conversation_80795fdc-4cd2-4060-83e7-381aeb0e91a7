from django.shortcuts import render
from django_filters.rest_framework import Django<PERSON><PERSON>erB<PERSON>end
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination

from .filters import ExamAnalysisFilter, CourseAnalysisFilter, ClassKnowledgeExamFilter
from .models import CourseAnalysis, ExamAnalysis
from .serializers import *
from .signals import course_learn_analysis

from course.models import Class, Course, Semester, CourseSemester
from exam.models import KnowledgeExam, CourseSemesterExam, ClassKnowledgeExam
from exam.utils import api_response


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 10  # 每页的数据量
    page_size_query_param = (
        "page_size"  # 允许客户端通过 `page_size` 参数自定义每页数据量
    )
    max_page_size = 100  # 每页最大数据量

    def get_paginated_response(self, data):
        """
        重写分页返回数据格式
        :param data:
        :return:
        """
        data = {
            "count": self.page.paginator.count,
            "next": self.get_next_link(),
            "previous": self.get_previous_link(),
            "results": data,
        }
        return data


# Create your views here.
class ExamAnalysisViewSet(viewsets.ModelViewSet):
    """
    考试/作业学情分析视图集
    list:
        获取学生分析报告列表
    analysis_whole_exams:
        获取以班级/课程为单位的考试/作业分析数据
    exam_class_list:
        获取考试/作业关联的班级列表
    """
    queryset = ExamAnalysis.objects.all()
    serializer_class = ExamAnalysisSerializers
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend]
    filterset_class = ExamAnalysisFilter

    def list(self, request, *args, **kwargs):
        """考试/作业 获取学生分析报告列表"""
        queryset = self.filter_queryset(self.get_queryset()).order_by("-id")
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = ExamAnalysisListSerializers(page, many=True)
            data = self.get_paginated_response(serializer.data)
            return api_response(200, "查询成功", data)

        serializer = ExamAnalysisListSerializers(queryset, many=True)
        return api_response(200, "查询成功", serializer.data)

    @action(methods=["GET"], detail=False, url_path="whole")
    def analysis_whole_exams(self, request, *args, **kwargs):
        """
        获取以班级/课程为单位的考试/作业分析数据
        """
        exam_id = request.query_params.get("exam_id")
        class_id = request.query_params.get("class_id")
        print(f"exam_id:{exam_id}")
        try:
            exam_obj = KnowledgeExam.objects.get(id=exam_id)
        except KnowledgeExam.DoesNotExist:
            return api_response(400, "考试不存在")
        
        response_data = {
            "exam_id": exam_id,
            "analysis_exams": "",
        }

        if class_id and class_id not in [None, "", "null", "undefined"]:
            try:
                class_obj = Class.objects.get(id=class_id)
                queryset = ClassKnowledgeExam.objects.filter(
                    exam_id=exam_obj,
                    class_id=class_obj
                )
                if not queryset.exists():
                    return api_response(400, "班级未关联此内容")
                serializer = ExamAnalysisWholeClassSerializers(queryset, many=True)

                # 更新响应数据
                response_data.update({
                    "analysis_exams": serializer.data[0].get("analysis_exams", ""),
                    "exam_id": exam_id
                })
            except (ValueError, Class.DoesNotExist):
                return api_response(400, "班级不存在")

        else:
            # 如果没有传class_id，默认展示整个课程下 该考试/作业 所有班级的分析结果
            try:
                queryset = CourseSemesterExam.objects.get(exam_id=exam_obj)

                serializer = ExamAnalysisWholeCourseSerializers(queryset)
                response_data.update({
                    "analysis_exams": serializer.data.get("analysis_course_exams", ""),
                    "exam_id": exam_id
                })
            except CourseSemesterExam.DoesNotExist:
                return api_response(400, "没有课程级别下的整体学情分析数据")

        return api_response(200, "查询成功", response_data)

    
    @action(methods=['GET'], detail=False, url_path='class_list')
    def exam_class_list(self, request, *args, **kwargs):
        """
        获取考试关联的班级列表
        """
        exam_id = request.query_params.get('exam_id', None)
        if not exam_id:
            return api_response(400, '请提供考试ID')
        
        try:
            exam_obj = KnowledgeExam.objects.get(id=exam_id)
        except KnowledgeExam.DoesNotExist:
            return api_response(400, '考试不存在')
        print(f"exam_obj:{exam_obj}")
        class_list_relations = ClassKnowledgeExam.objects.filter(exam_id=exam_obj)
        classes = Class.objects.filter(
            id__in=class_list_relations.values_list('class_id', flat=True)
        )
        print(f"classes:{classes}")
        if not classes.exists():
            return api_response(400, '班级不存在')
        serializer = ExamAnalysisClassListSerializers(classes, many=True)
        return api_response(200, '查询成功', serializer.data)


class CourseAnalysisViewSet(viewsets.ModelViewSet):
    """
    课程学情分析视图集
    list:
        以课程为单位 获取学生学情分析报告列表
    retrieve:
        课程 获取学生学情分析报告详情
    course_class_list:
        获取课程关联的班级列表
    """
    queryset = CourseAnalysis.objects.all()
    # serializer_class = ExamAnalysisSerializers
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend]
    filterset_class = CourseAnalysisFilter

    def list(self, request, *args, **kwargs):
        """课程 获取学生学情分析报告列表"""
        course_id = request.query_params.get('course', None)
        if not course_id:
            return api_response(400, '请提供课程ID')
        semester_id = request.query_params.get('semester', None)
        if not semester_id:
            return api_response(400, '请提供学期ID')
        
        queryset = self.filter_queryset(self.get_queryset()).order_by("-id")
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = CourseAnalysisListSerializers(page, many=True)
            data = self.get_paginated_response(serializer.data)
            return api_response(200, "查询成功", data)

        serializer = CourseAnalysisListSerializers(queryset, many=True)
        return api_response(200, "查询成功", serializer.data)

    def retrieve(self, request, *args, **kwargs):
        """课程 获取学生学情分析报告详情"""
        try:
            instance = self.get_object()
            serializer = CourseAnalysisDetailSerializers(instance)
            return api_response(200, "查询成功", serializer.data)
        except Exception as e:
            return api_response(400, "查询失败")

    @action(methods=['GET'], detail=False, url_path='class_list')
    def course_class_list(self, request, *args, **kwargs):
        """获取课程关联的班级列表"""
        course_id = request.query_params.get('course', None)
        if not course_id:
            return api_response(400, '请提供课程ID')
        semester_id = request.query_params.get('semester', None)
        if not semester_id:
            return api_response(400, '请提供学期ID')
        try:
            course_obj = Course.objects.get(id=course_id)
        except Course.DoesNotExist:
            return api_response(400, '课程不存在')
        try:
            semester_obj = Semester.objects.get(id=semester_id)
        except Semester.DoesNotExist:
            return api_response(400, '学期不存在')
        try:
            class_list = Class.objects.filter(course_semester__course=course_obj, course_semester__semester=semester_obj)
        except Class.DoesNotExist:
            return api_response(400, '班级不存在')
        serializer = CourseAnalysisClassListSerializers(class_list, many=True)
        return api_response(200, '查询成功', serializer.data)
    
    # @action(methods=['POST'], detail=False, url_path='learn_situation')
    # def learn_situation(self, request, *args, **kwargs):
    #     """
    #     课程 每位学生的学习进度 学情分析  【教师手动输入学习进度等情况】
    #     """
    #     course_id = request.data.get('course')
    #     semester_id = request.data.get('semester')
    #     user_id = request.data.get('user_id')
    #     student_attendance = request.data.get('student_attendance')  # 学生课程出勤情况
    #     class_performance = request.data.get('class_performance')  # 学生课堂表现情况


    #     if not user_id:
    #         return api_response(400, '请提供用户ID')
    #     if not course_id:
    #         return api_response(400, '请提供课程ID')
    #     if not semester_id:
    #         return api_response(400, '请提供学期ID')
    #     course_semester = CourseSemester.objects.get(course=course_id, semester=semester_id)
    #     course_learn_analysis.send(
    #         sender=self,
    #         course_id=course_id,
    #         semester_id=semester_id,
    #         course_semester = course_semester
    #     )


        




