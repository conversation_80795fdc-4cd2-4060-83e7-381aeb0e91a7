import json
from django.conf import settings
from functools import cached_property
from typing import TypedDict, Annotated, Sequence, Any, Union, List, Callable, override
from uuid import uuid4

import requests
from django_redis import get_redis_connection
from langchain_community.chat_models import Cha<PERSON><PERSON><PERSON><PERSON>

from langchain_core.messages import BaseMessage, trim_messages, SystemMessage, HumanMessage, message_to_dict
from langchain_core.messages.utils import count_tokens_approximately
from langchain_openai import Chat<PERSON>penA<PERSON>

from langgraph.checkpoint.redis import RedisSaver
from langgraph.constants import START, END
from langgraph.graph import add_messages, StateGraph
from pydantic import BaseModel, Field
from typing_extensions import NamedTuple

from chat.agents.base import ChatDocumentSpliter, reranker
from chat.chat_models import CustomChatQwQ
from chat.constants import CHUNK_DETAIL, CHUNK_DETAIL_REDIS_KEY, REMOTE_CHUNK_EXPIRE_TIME
from chat.models import Conversation
from chat.prompts import knowledge_system_prompt, \
    knowledge_human_template, common_chat_human_template, common_chat_system_prompt, \
    question_rewrite_template
from chat.streams import Retrieve<PERSON>huckRespData
from th_dsep_backend.celery import app

# 模型支持的最大token数
MODEL_SUPPORT_MAX_TOKEN = 40 * 1024
# 聊天模式下 用户输入占比
THRESHOLD_COMMON_CHAT = 0.5
# 预留token比例
THRESHOLD_RESERVED = 0.1
NO_THINK = '/no_think'


# COMMON_CHAT_MAX_TOKEN = int(16*1024*0.5)


def overwrite_question(
        left: str,
        right: str,
) -> str:
    return right


class ChatState(TypedDict):
    messages: Annotated[Union[Sequence[BaseMessage] | List[BaseMessage]], add_messages]
    retrieve_base_url: str
    headers: dict
    retrieve_content: str
    conversation_id: str
    stream_protocol: str
    # question: Annotated[str,overwrite_question]
    question: str
    dataset_ids: list[str]
    document_ids: list[str]
    file_path: str
    # 可选普通、知识库对话、文件对话，common/knowledge/file
    chat_type: str
    history_info: dict  # 历史会话的额外信息
    rewrite: bool
    enable_thinking: bool
    model_name: str


class ChatAgent:
    @staticmethod
    def custom_token_counter(messages):
        return sum(len(message.content) for message in messages)

    def re_write_question(self, state: ChatState):
        if not state['rewrite']:
            return {}
        # 根据上下文，提炼出用户的问题
        trimed_history_question = self.trimmer().invoke(state['messages'])
        history_question = []
        for message in trimed_history_question:
            dict = {'role': message.type, 'message': message.content}
            history_question.append(dict)
        history = json.dumps(history_question, ensure_ascii=False)

        prompt = question_rewrite_template.format(history=history, query=state['question'])

        question = self.llm.invoke(prompt).content
        print(f'根据{len(history_question)}条:重写后的问题：{question}')
        return {"question": question}

    def selector_node(self, state: ChatState):
        self.conversation_id = state['conversation_id']
        if state['dataset_ids'] or state['document_ids']:
            chat_type = 'knowledge'
            node = 'retrieve_remote_knowledge'
        elif state['file_path']:
            chat_type = 'file'
            node = 'retrieve_file'
        else:
            # 判断之前是否上传过文档
            history_info = state['history_info']
            if history_info:
                chat_type = 'file'
                node = 'retrieve_file'
            else:
                chat_type = 'common'
                node = 'format_common_prompt'
        # return {"chat_type":chat_type,"node":node}
        # state['chat_type'] = chat_type
        setattr(self, 'chat_type', chat_type)
        # print(f'本次会话类型：{chat_type}')
        return node

    def __init__(self, max_tokens=MODEL_SUPPORT_MAX_TOKEN, model=settings.LLM_NAME, api_key=settings.LLM_API_KEY,
                 base_url=settings.LLM_BASE_URL_LESSON,
                 redis_url=settings.REDIS_BASE_URL):

        self.max_tokens = max_tokens
        self.model_max_token = int(self.max_tokens * (1 - THRESHOLD_RESERVED) * (1 - THRESHOLD_COMMON_CHAT))
        self.chat_max_token = int(self.max_tokens * (1 - THRESHOLD_RESERVED) * THRESHOLD_COMMON_CHAT)
        # print(self.max_tokens)
        # print(self.model_max_token)
        # print(self.chat_max_token)

        self.model = model if model else settings.LLM_NAME
        self.api_key = api_key
        self.base_url = base_url
        self.redis_url = redis_url
        self.graph_builder = StateGraph(state_schema=ChatState)
        self.graph_builder.add_node('format_knowledge_prompt', self.format_knowledge_prompt)
        self.graph_builder.add_node('format_common_prompt', self.format_common_prompt)
        self.graph_builder.add_node('retrieve_remote_knowledge', self.retrieve_remote_knowledge)
        self.graph_builder.add_node('retrieve_file', self.retrieve_file)
        self.graph_builder.add_node('model', self.call_model)
        self.graph_builder.add_node('re_write_question', self.re_write_question)

        self.graph_builder.add_edge(START, 're_write_question')
        self.graph_builder.add_edge('retrieve_remote_knowledge', 'format_knowledge_prompt')
        self.graph_builder.add_edge('format_knowledge_prompt', 'model')
        self.graph_builder.add_edge('format_common_prompt', 'model')
        self.graph_builder.add_edge('retrieve_file', 'format_knowledge_prompt')
        self.graph_builder.add_edge('model', END)
        self.graph_builder.add_conditional_edges('re_write_question', self.selector_node,
                                                 {
                                                     'retrieve_remote_knowledge': 'retrieve_remote_knowledge',
                                                     'format_common_prompt': 'format_common_prompt',
                                                     'retrieve_file': 'retrieve_file'}
                                                 )

        self.graph = self.graph_builder.compile(checkpointer=self.check_pointer)
        # show(self.graph)
        self.init_llm()
        self.fake_id_map = {}
        self.retrieve_data = {}
        self.doc_id_map_name = {}
        self.chunk_id_map_doc = {}

    def init_llm(self):
        # self.llm = ChatOpenAI(
        #     model=self.model,
        #     api_key=self.api_key,
        #     base_url=self.base_url,
        #     max_tokens=self.max_tokens, )
        # self.llm=CustomChatQwQ(max_tokens=int(self.model_max_token),model=self.model,api_key=self.api_key,base_url=self.base_url)
        self.llm = CustomChatQwQ(max_tokens=int(self.model_max_token), model=self.model, api_key=self.api_key,
                                 base_url=self.base_url, temperature=0.6, top_p=0.95)

        self.title_llm = self.llm
        # ChatTongyi(model="qwen-plus", api_key='sk-d5d356caae2e417e98b289f3e86903b1')

        # self.llm = ChatTongyi(model="qwen-plus", api_key='sk-d5d356caae2e417e98b289f3e86903b1')

        # self.llm=langsmith_llm

    # @cached_property
    def trimmer(self):
        return trim_messages(
            max_tokens=self.chat_max_token,
            strategy="last",
            token_counter=self.custom_token_counter,
            # token_counter=self.llm,
            include_system=True,
            allow_partial=False,
            start_on="human",
        )

    @cached_property
    def check_pointer(self):
        with RedisSaver.from_conn_string(self.redis_url, ttl={'default_ttl': REMOTE_CHUNK_EXPIRE_TIME}) as checkpointer:
            checkpointer.setup()
            return checkpointer

    # if redis_url:=getattr(self, "redis_url", None):
    #     with RedisSaver.from_conn_string(redis_url) as checkpointer:
    #         checkpointer.setup()
    #         return checkpointer
    # else:
    #     return InMemorySaver()

    @staticmethod
    def format_knowledge_prompt(state: ChatState):
        """
        知识库 prompt
        """
        # prompt_value = knowledge_human_template.invoke(state)
        # new_messages = [HumanMessage(content=prompt_value.to_string())]
        prompt_value = knowledge_human_template.invoke(state)
        new_messages = prompt_value.to_string()
        return {'messages': new_messages}

    @staticmethod
    def format_common_prompt(state: ChatState):
        """
        普通对话
        """
        prompt_value = common_chat_human_template.invoke(state)
        new_messages = prompt_value.to_string()
        return {'messages': new_messages}

    def call_model(self, state: ChatState):
        """call llm node"""
        # 添加系统提示词 只加一次
        messages = state['messages']
        if messages and not messages[0].type == 'system':
            # sys_tp=common_chat_human_template if state['chat_type']=='common' else knowledge_system_prompt
            enable_thinking = '' if state['enable_thinking'] else NO_THINK
            sys_tp = common_chat_system_prompt.format(enable_thinking=enable_thinking) \
                if getattr(self, 'chat_type',None) == 'common'\
                else knowledge_system_prompt.format(enable_thinking=enable_thinking,source='文档' if getattr(self, 'chat_type')=='file' else '知识库')
            messages = [SystemMessage(sys_tp)] + messages
        trimed_messages = self.trimmer().invoke(messages)
        # print(f'生成回复前消息数量：{len(trimed_messages)}')
        # print(f'长度：{sum([len(msg.content) for msg in  trimed_messages])}')
        response = self.llm.invoke(trimed_messages)
        return {'messages': response}

    def retrieve_remote_knowledge(self, state: ChatState):

        """retrieve knowledge"""
        retrieve_base_url = state['retrieve_base_url']
        url = f"{retrieve_base_url}/api/v1/retrieval"
        headers = state['headers']

        request_data = {
            "question": state['question'],
            "dataset_ids": state['dataset_ids'],  # 根据指定的数据集
            # "dataset_ids": ['f9b8981658b311f0a05d4ad5a33f453a'],  # 根据指定的数据集
            "document_ids": state["document_ids"],  # 选择指定的document,可不指定
            "similarity_threshold": 0.1,
            "rerank_id": "bge-reranker-v2-m3",
            "keyword": True,
            "highlight": False,
        }
        resp = requests.post(url, headers=headers, json=request_data)
        text = json.loads(resp.text)
        code = text['code']

        if code != 0:
            raise Exception(f'检索知识库异常,code:{code},message:{text.get("message", "")}')

        data = text['data']
        self.retrieve_data: RetrieveChuckRespData = RetrieveChuckRespData(**data)
        redis_conn = get_redis_connection(CHUNK_DETAIL)
        contents = []
        self.doc_id_map_name = {doc['doc_id']: doc['doc_name'] for doc in self.retrieve_data['doc_aggs']}
        chunks = self.retrieve_data["chunks"][:4]  # 取前4条
        for idx, chunk in enumerate(chunks):
            chunk_id = chunk['id']
            uuid = str(uuid4())
            doc_id = chunk['document_id']
            self.fake_id_map[str(idx + 1)] = (chunk_id, uuid)
            contents.append(f"[{idx + 1}]:{chunk['content']}")
            self.chunk_id_map_doc[chunk_id] = doc_id
            redis_key = CHUNK_DETAIL_REDIS_KEY.format(doc_id, chunk_id, uuid)
            # 加入缓存，保证文档被change后，缓存有效
            if redis_conn.set(redis_key, chunk['content']):
                redis_conn.expire(redis_key, REMOTE_CHUNK_EXPIRE_TIME)
        retrieve_content = '<br;next>'.join(contents)
        return {'retrieve_content': retrieve_content}
        # return state

    class FileQueryInstruction(BaseModel):
        """
        description:用于区分用户问题的分类,请严格按下方json结构输出：
        {"type":"normal"}
        """
        type: str = Field(str, description='查询问题的分类，可分为普通问答和总结两类:"normal","summary"')

    def retrieve_file(self, state: ChatState):

        # 先根据用户问题，结构化输出
        # struct_llm=self.llm.with_structured_output(self.FileQueryInstruction)
        # struct_system_message = "请将这段话{question}进行分类，仅提取'FileQueryInstruction'中定义的属性"
        # system_prompt = struct_system_message.format(question=state['messages']+[HumanMessage(state['question'])])
        # struct_msg= struct_llm.invoke(system_prompt)
        # print(f'结构化输出结构：{struct_msg}')
        # question_type= getattr(struct_msg,'type','normal')
        #
        question_type = 'normal'  # todo 临时调整为normal取消总结的分支,以上代码注释掉，减少消耗
        """Retrieve information related to a query."""
        conversation_id = state['conversation_id']
        question = state['question']
        is_normal = question_type == 'normal'
        vector_store = ChatDocumentSpliter.get_split_vector_store(
            conversation_id) if is_normal else ChatDocumentSpliter.get_all_summary_vector_store(conversation_id)
        # retrieved_docs = vector_store.similarity_search(question, k=4 if is_normal else 2)
        retrieved_docs = vector_store.similarity_search(question, k=10)
        # retrieved_docs=self.rerank(question,retrieved_docs,5)
        # print(f'检索的本间数据的相似度：{';'.join([str(s) for s in retrieved_docs])})
        redis_conn = get_redis_connection(CHUNK_DETAIL)
        contents = []

        for idx, chunk in enumerate(retrieved_docs):
            idx = idx + 1
            doc_id = chunk.metadata['document_id']
            source_name = chunk.metadata['source_name']
            chunk_id = chunk.metadata['chunk_id']
            self.doc_id_map_name[doc_id] = source_name
            self.fake_id_map[str(idx)] = (chunk_id, chunk_id)  # 这里没有新创建uuid，为了讲解ragflow的检索功能
            self.chunk_id_map_doc[chunk_id] = doc_id

            contents.append(f"[{idx}]:{chunk.page_content}")
            redis_key = CHUNK_DETAIL_REDIS_KEY.format(doc_id, chunk_id, chunk_id)
            # 加入缓存，保证文档被change后，缓存有效
            if redis_conn.setnx(redis_key, chunk.page_content):
                redis_conn.expire(redis_key, REMOTE_CHUNK_EXPIRE_TIME)
        retrieve_content = '<br;next>'.join(contents)
        return {'retrieve_content': retrieve_content}
    def rerank(self,query,retrieved_docs,top_n=5):

        if not retrieved_docs:
            return []

            # 准备模型输入：query 和每个文档内容的组合
        model_input = [(query, doc.page_content) for doc in retrieved_docs]

        # 调用 reranker 获取相关性分数
        scores = reranker.predict(model_input)

        # 将文档和分数配对并排序（降序）
        scored_docs = sorted(zip(retrieved_docs, scores), key=lambda x: x[1], reverse=True)

        # 只保留前 top_n 个文档
        top_docs = [doc for doc, _ in scored_docs[:top_n]]

        # 将分数存入文档的 metadata（可选）
        for doc, score in zip(top_docs, scores[:top_n]):
            doc.metadata['relevance_score'] = float(score)

        return top_docs

@app.task
def create_title_with_llm(messages, conversation_id):
    system_prompt = """
    /no_think
    你擅长根据用户的提问'question'生成标题，要求标题不超过20字
    注意：生成的内容中不要含有'标题:'
    """
    human_promp = "'question:{input}'"
    messages = human_promp.format(input=messages)
    agent = ChatAgent()
    title = agent.title_llm.invoke([SystemMessage(system_prompt), HumanMessage(messages)])
    if '<think>' in title.content and '</think>' in title.content:
        title = title.content[title.content.index('</think>') + 9:]
    if getattr(title, 'content', None):
        title = title.content.lstrip('\n').rstrip('\n')
    c = Conversation.objects.get(pk=conversation_id)
    c.title = title[:50]
    c.save(update_fields=['title'])
    print(f'生成标题:{title}')
