import logging
from rest_framework import viewsets, permissions, status, mixins
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from django.forms.models import model_to_dict
from rest_framework.response import Response

from django.conf import settings
from user.permissions import TablePermission
from user.models import UserInfo
from ppt.models import PptModel
from teachplan.models import TeachPlanModel
from exam.models import QuestionKnowledgePoint
from .serializers import *
from .models import *
from .utils import api_response, duplicate_course_with_relations
from ppt.models import PptModel
from teachplan.models import TeachPlanModel
from exam.models import *
from graph.janusgraph_client import JanusGraphClient

logger = logging.getLogger(__name__)


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 10  # 每页的数据量
    page_size_query_param = 'page_size'  # 允许客户端通过 `page_size` 参数自定义每页数据量
    max_page_size = 100  # 每页最大数据量


class CourseStatisticalViewSet(viewsets.GenericViewSet, mixins.ListModelMixin):
    """
    课程统计视图集
    """
    permission_classes = (permissions.IsAuthenticated,)  # 教师、学生、管理员都可以访问

    # serializer_class = CourseStatisticalSerializer

    def list(self, request, *args, **kwargs):
        course_id = request.query_params.get('course_id')
        if not course_id:
            return api_response(200, '课程ID参数不能为空')

        # 获取当前课程的章节ID数组
        chapter_ids = Chapter.objects.filter(course_id=course_id).values_list('id', flat=True)
        # print('章节ID数组', chapter_ids)
        # 获取当前课程的知识点ID数组
        knowledge_point_ids = KnowledgePoint.objects.filter(chapter_id__in=chapter_ids).values_list('id', flat=True)
        knowledge_point_count = KnowledgePoint.objects.filter(chapter_id__in=chapter_ids).count()
        # 获取当前课程的PPT数量
        ppt_count = PptModel.objects.filter(chapter__id__in=chapter_ids).count()
        # 获取当前课程的教案数量
        teachplan_count = TeachPlanModel.objects.filter(chapter__id__in=chapter_ids).count()
        # 获取当前课程的所有题目
        question_count = QuestionKnowledgePoint.objects.filter(knowledge_point_id__in=knowledge_point_ids).count()

        data = {
            'resource_count': None,
            'knowledge_point_count': knowledge_point_count,
            'ppt_count': ppt_count,
            'teachplan_count': teachplan_count,
            'question_count': question_count
        }
        return api_response(200, '获取课程统计信息成功', data)


class SemesterViewSet(viewsets.GenericViewSet, mixins.ListModelMixin):
    """
    学期视图集
    list:
        获取学期列表
    """
    queryset = Semester.objects.all()
    serializer_class = SemesterSerializer

    permission_classes = [permissions.IsAuthenticated]

    def list(self, request, *args, **kwargs):
        """
        获取学期列表
        """
        queryset = self.filter_queryset(self.get_queryset()).filter(is_active=1)

        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            response_data = self.get_paginated_response(serializer.data).data
        else:
            serializer = self.get_serializer(queryset, many=True)
            response_data = serializer.data

        return api_response(200, '获取学期列表成功', response_data)


class CourseSemesterViewSet(viewsets.GenericViewSet, mixins.ListModelMixin):
    """
    课程学期视图集
    list:
        获取课程学期列表
    owner:
        用户获取课程学期列表
    """
    queryset = CourseSemester.objects.all()
    serializer_class = CourseSemesterSerializer

    permission_map = {
        'owner': ['course.owner_course_semester'],
    }

    def list(self, request, *args, **kwargs):
        semester_id = request.query_params.get('semester_id')
        if not semester_id:
            return api_response(200, '学期ID参数不能为空')
        course_id = request.query_params.get('course_id')
        if not course_id:
            return api_response(200, '课程ID参数不能为空')
        queryset = self.filter_queryset(self.get_queryset()).filter(semester=semester_id,
                                                                    course=course_id)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return api_response(200, '获取课程学期信息成功', serializer.data)

    @action(methods=['get'], detail=False)
    def owner(self, request, *args, **kwargs):
        """
        用户获取课程学期列表, 仅返回用户可以看到的课程及学期
        """
        user_id = request.user.id
        print(request.user.role)
        queryset = self.filter_queryset(self.get_queryset()).filter(user_id=user_id)
        serializer = CourseSemesterOwnerSerializer(queryset, many=True)
        return api_response(200, '获取课程学期信息成功', serializer.data)


class CourseViewSet(viewsets.ModelViewSet):
    """
    课程视图集
    create:
        创建课程
    retrieve:
        获取课程详情
    update:
        更新课程
    destroy:
        删除课程
    list:
        获取课程列表
    owner:
        获取课程列表(个人空间)
    knowledge:
        获取课程知识点列表
    semester:
        获取所有课程及学期信息
    reopen:
        重开课程
    copy:
        课程复制
    """
    queryset = Course.objects.all()
    serializer_class = CourseSerializer
    pagination_class = StandardResultsSetPagination

    graph_client = JanusGraphClient(settings.JANUSGRAPH_URL)  # 图数据库客户端

    permission_map = {
        'owner': ['course.owner_course'],
        'knowledge': ['course.knowledge_course'],
        'semester': ['course.semester_course'],
        'reopen': ['course.reopen_course'],
        'copy': ['course.copy_course']
    }

    def create(self, request, *args, **kwargs):
        """
        创建课程
        """
        # print('用户名', request.user.username)
        # print('用户ID', request.user.id)
        serializer = CourseCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return api_response(200, '课程创建成功', serializer.data)

    def perform_create(self, serializer):
        obj = serializer.save()
        return obj

    def update(self, request, *args, **kwargs):
        """
        修改课程
        """
        user_id = request.user.id
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        # 判断课程是否创建章节知识点知识图谱
        graph_res = self.graph_client.by_chapter_id_get_nodes(course_id=instance.id, user_id=user_id)
        if graph_res:
            # 课程已创建章节知识点知识图谱，更新章节知识点
            node_type = 'course'

            properties = {
                "id": serializer.data['id'],
                "title": serializer.data['title'],
                "description": serializer.data['description']
            }

            self.graph_client.update_node_from_properties(properties=properties, node_type=node_type,
                                                          user_id=user_id,
                                                          course_id=instance.id)

        return api_response(200, '修改课程成功', serializer.data)

    def destroy(self, request, *args, **kwargs):
        """
        删除课程
        """
        instance = self.get_object()
        self.perform_destroy(instance)
        return api_response(200, '课程删除成功')

    def list(self, request, *args, **kwargs):
        """
        获取课程列表
        """
        # print(request.user.id)
        semester_id = request.query_params.get('semester_id')

        if not semester_id:
            return api_response(200, '课程学期ID参数不能为空')
        user_id = request.user.id
        # user_id = request.query_params.get('user_id')
        # print(user_id)
        if not user_id:
            # return api_response(200, '用户ID参数不能为空')
            return api_response(200, '未正常传输用户Session')

        title = request.query_params.get('title', '')
        # Semester.objects.get(id=semester_id)

        # print(request.user.role)
        # 判断是否学生角色
        if request.user.role == 2:
            # 获取班级ID列表
            class_ids = ClassMember.objects.filter(student_id=user_id,
                                                   course_semester__semester_id=semester_id).values_list('class_obj_id',
                                                                                                         flat=True)
            course_ids = Class.objects.filter(id__in=class_ids).values_list('course_semester__course_id', flat=True)
        else:
            # 获取教师课程学期ID数组
            teacher_course_semester_ids = CourseTeacher.objects.filter(teacher_id=user_id).values_list(
                "course_semester_id",
                flat=True)
            # 获取课程ID数组
            course_ids = CourseSemester.objects.filter(id__in=teacher_course_semester_ids,
                                                       semester=semester_id).values_list("course_id", flat=True)

        if title:
            queryset = self.filter_queryset(self.get_queryset()).filter(id__in=course_ids,
                                                                        title__icontains=title)
        else:
            queryset = self.filter_queryset(self.get_queryset()).filter(id__in=course_ids)

        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            response_data = self.get_paginated_response(serializer.data).data
        else:
            serializer = self.get_serializer(queryset, many=True)
            response_data = serializer.data

        return api_response(200, '获取课程列表成功', response_data)

    @action(methods=['GET'], detail=False, url_path='owner')
    def owner(self, request, *args, **kwargs):
        """
        获取课程列表(个人空间)
        """

        user_id = request.user.id
        print(user_id)
        if not user_id:
            return api_response(200, '用户Session未正常传输')
        course_semester_ids = CourseTeacher.objects.filter(teacher_id=user_id).values_list('course_semester_id',
                                                                                           flat=True)
        course_ids = CourseSemester.objects.filter(id__in=course_semester_ids).values_list("course_id", flat=True)
        queryset = self.filter_queryset(self.get_queryset()).filter(id__in=course_ids)
        serializer = CourseOwnerSerializer(queryset, many=True)
        return api_response(200, '获取用户课程列表成功', serializer.data)

    @action(methods=['GET'], detail=False, url_path='knowledge')
    def knowledge(self, request, *args, **kwargs):
        """
        获取课程知识点列表
        """
        user_id = request.user.id
        if not user_id:
            return api_response(200, '用户Session未正常传输')
        course_id = request.query_params.get('course_id')

        fuzzy = request.query_params.get('fuzzy')  # 模糊查询字段

        if not course_id:
            # 获取用户所有课程
            user_course_ids = CourseTeacher.objects.filter(teacher_id=user_id).values_list("course_id", flat=True)
            # print('用户所有课程id：', user_course_ids)
            courses = Course.objects.filter(id__in=user_course_ids)

        else:
            courses = Course.objects.filter(id=course_id)

        serializers = CourseKnowledgeSerializer(courses, many=True, context={'fuzzy': fuzzy})

        return api_response(200, '获取课程知识点列表成功', serializers.data)

    @action(methods=['GET'], detail=False, url_path='semester')
    def semester(self, request, *args, **kwargs):
        """
        获取所有课程及学期信息
        """
        queryset = self.filter_queryset(self.get_queryset())
        serializer = CourseAndSemesterSerializer(queryset, many=True)
        return api_response(200, '获取所有课程及学期信息成功', serializer.data)

    @action(methods=['POST'], detail=False, url_path='reopen')
    def reopen(self, request, *args, **kwargs):
        """
        课程重开
        """

        serializer = CourseSemesterSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        # print('重开课程学期ID', serializer.data.get('id'))
        CourseTeacher.objects.create(
            course_semester_id=serializer.data.get('id'),
            teacher_id=request.data.get('user'),
            role=1
        )
        headers = self.get_success_headers(serializer.data)

        return api_response(200, '课程重开成功', serializer.data)

    @action(methods=['POST'], detail=False, url_path='copy')
    def copy(self, request, *args, **kwargs):
        """
        课程复制
        """
        user = request.user
        course_id = request.data.get('course_id')  # 旧课程ID
        semester_id = request.data.get('semester_id')  # 旧学期ID
        new_semester_id = request.data.get('new_semester_id')  # 新学期ID
        new_course_name = request.data.get('new_title')  # 新课程名称

        # 调用复制课程工具方法
        new_course, new_semester = duplicate_course_with_relations(course_id, semester_id, new_course_name,
                                                                   new_semester_id)

        data = {
            'new_course': CourseCopySerializer(new_course).data,
            'new_semester': CourseSemesterSerializer(new_semester).data,
        }

        return api_response(200, '课程复制成功', data)

    @action(methods=['GET'], detail=False, url_path='chapter')
    def chapter(self, request, *args, **kwargs):
        """
        获取用户课程章节列表
        """
        user_id = request.user.id
        if not user_id:
            return api_response(200, '用户Session未正常传输')
        # courses = Course.objects.filter(teacher_courses__teacher_id=user_id).distinct()
        # print(courses)
        course_semester_ids = CourseTeacher.objects.filter(teacher_id=user_id).values_list("course_semester_id",
                                                                                           flat=True)
        course_ids = CourseSemester.objects.filter(id__in=course_semester_ids).values_list("course_id",
                                                                                           flat=True).distinct()
        courses = Course.objects.filter(id__in=course_ids)
        # print(courses)
        serializer = CourseChapterSerializer(courses, many=True)
        return api_response(200, '获取课程章节列表成功', serializer.data)


class CourseTeacherViewSet(viewsets.GenericViewSet, mixins.CreateModelMixin, mixins.ListModelMixin):
    """
    课程教师视图集
    create:
        添加课程教师(批量添加)
    list:
        获取课程教师列表
    batch_destroy:
        批量删除课程教师
    """
    queryset = CourseTeacher.objects.all()
    serializer_class = CourseTeacherSerializer
    pagination_class = StandardResultsSetPagination

    # permission_classes = [permissions.IsAuthenticated]
    permission_map = {
        'batch_destroy': ['course.batch_destroy_course_teacher'],
    }

    def create(self, request, *args, **kwargs):
        """
        添加课程教师(批量添加)
        """
        # course_id = request.data.get('course_id')  # 课程ID
        # semester_id = request.data.get('semester_id')  # 学期ID
        # try:
        #     course_semester = CourseSemester.objects.get(course_id=course_id, semester_id=semester_id)
        # except CourseSemester.DoesNotExist:
        #     return api_response(200, '课程学期不存在')
        teachers = request.data.get('teachers')  # 教师ID列表, ['02002', '02003', '02004']
        # print(len(teachers))
        roles = request.data.get('roles')  # 教师ID列表, [teacher, assistant, teacher]
        # print(len(roles))
        if not teachers:
            return api_response(200, '教师ID列表不能为空')
            # return Response({'error': '教师ID列表不能为空'}, status=status.HTTP_400_BAD_REQUEST)
        if not roles:
            return api_response(200, '角色列表不能为空')
            # return Response({'error': '角色列表不能为空'}, status=status.HTTP_400_BAD_REQUEST)
        if len(teachers) != len(roles):
            return api_response(200, '教师ID列表和角色列表长度不一致')
            # return Response({'error': '教师ID列表和角色列表长度不一致'}, status=status.HTTP_400_BAD_REQUEST)
        serializer = CourseTeacherAddSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return api_response(200, '教师添加成功')

    @action(methods=['delete'], detail=False, url_path='batch_destroy')
    def batch_destroy(self, request, *args, **kwargs):
        """
        批量删除课程教师
        """
        # course_semester_id = request.data.get('course_semester_id')
        # if not course_semester_id:
        #     return api_response(200, '课程学期ID参数不能为空')
        ids = request.data.get('ids')  # 课程教师ID列表, ["02002", "02003", "02004"]
        if not ids:
            return api_response(200, '课程教师ID列表未传参')
        for id in ids:
            instance = CourseTeacher.objects.get(id=id)
            # print(instance)
            instance.delete()
        return api_response(200, '课程教师删除成功')

    def list(self, request, *args, **kwargs):
        """
        获取课程教师列表
        """
        course_semester_id = request.query_params.get('course_semester_id')
        fuzzy = request.query_params.get('fuzzy')  # 模糊查询字段
        if not course_semester_id:
            return api_response(200, '课程学期ID参数不能为空')

        if fuzzy:
            queryset = self.filter_queryset(self.get_queryset()).filter(course_semester_id=course_semester_id,
                                                                        teacher__username__icontains=fuzzy)
        else:
            queryset = self.filter_queryset(self.get_queryset()).filter(course_semester_id=course_semester_id)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            response_data = self.get_paginated_response(serializer.data).data
        else:
            serializer = self.get_serializer(queryset, many=True)
            response_data = serializer.data
        # print(response_data)

        return api_response(200, '获取课程教师列表成功', response_data)


class ChapterViewSet(viewsets.ModelViewSet):
    """
    章节视图集
    create:
        创建章节
    retrieve:
        获取章节详情
    update:
        更新章节
    destroy:
        删除章节
    list:
        获取章节列表
    tables:
        获取指定章节目录
    owner:
        获取用户所有课程章节目录
    """
    queryset = Chapter.objects.all()
    serializer_class = ChapterSerializer
    graph_client = JanusGraphClient(settings.JANUSGRAPH_URL)  # 图数据库客户端

    permission_map = {
        'tables': ['course.tables_chapter'],
        'owner': ['course.owner_chapter']
    }

    # permission_classes = [permissions.IsAuthenticated]

    def create(self, request, *args, **kwargs):
        """
        创建章节
        """
        user_id = request.user.id
        course_id = request.data.get('course')
        serializer = ChapterSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)

        # 判断课程是否创建章节知识点知识图谱
        graph_res = self.graph_client.by_chapter_id_get_nodes(course_id=course_id, user_id=user_id)
        if graph_res:
            # 课程已创建章节知识点知识图谱，创建章节知识点
            chapter_data = {
                "id": serializer.data['id'],
                "title": serializer.data['title'],
                "content": serializer.data['content'],
                "order": serializer.data['order'],
                "parent": serializer.data['parent']
            }
            self.graph_client.add_chapter(course_id=course_id, user_id=user_id, chapter_data=chapter_data)

        return api_response(200, '章节创建成功', serializer.data)

    def retrieve(self, request, *args, **kwargs):
        """
        章节详情获取
        """
        user_id = request.user.id
        if not user_id:
            return api_response(200, '用户Session未正常传输')
        instance = self.get_object()
        serializer = ChapterRetrieveSerializer(instance)
        ChapterCompletion.objects.filter(student_id=user_id, chapter_id=instance.id).update(completed=True,
                                                                                            percentage=100)
        return api_response(200, '章节详情获取成功', serializer.data)

    def update(self, request, *args, **kwargs):
        """
        修改章节
        """
        user_id = request.user.id
        course_id = request.data.get('course')

        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        # 判断课程是否创建章节知识点知识图谱
        graph_res = self.graph_client.by_chapter_id_get_nodes(course_id=course_id, user_id=user_id)
        if graph_res:
            # 课程已创建章节知识点知识图谱，更新章节知识点
            parent = serializer.data['parent']
            if parent is None:
                node_type = 'chapter'
            else:
                node_type = 'section'

            properties = {
                "id": 2,
                "title": "1.1 什么是因特网",
                "content": "主要对于因特网的概念进行讲解?",
                "order": 0,
                "parent": None
            }

            self.graph_client.update_node_from_properties(properties=properties, node_type=node_type, user_id=user_id,
                                                          course_id=course_id)

        return api_response(200, '修改章节成功', serializer.data)

    def destroy(self, request, *args, **kwargs):
        """
        删除章节
        """

        instance = self.get_object()
        self.perform_destroy(instance)
        return api_response(200, '章节删除成功')

    def list(self, request, *args, **kwargs):
        """
        获取章节列表
        """

        course_id = request.query_params.get('course_id')
        if not course_id:
            return api_response(200, '课程ID参数不能为空')

        title = request.query_params.get('title', '')
        if title:
            queryset = self.filter_queryset(self.get_queryset()).filter(course=course_id, parent__isnull=True,
                                                                        title__icontains=title)
        else:
            queryset = self.filter_queryset(self.get_queryset()).filter(course=course_id, parent__isnull=True)
        # print(len(queryset))

        page = self.paginate_queryset(queryset)
        if page is not None:
            print('进入分页')
            serializer = ChapterViewSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = ChapterViewSerializer(queryset, many=True, context={'request': request})
        return api_response(200, '获取章节列表成功', serializer.data)

    @action(methods=['GET'], detail=False, url_path='tables')
    def tables(self, request, *args, **kwargs):
        """
        获取指定章节目录
        """
        # print(request.user.id)
        chapter_id = request.query_params.get('chapter_id')
        if not chapter_id:
            return api_response(200, '章节ID参数不能为空')
        try:
            # instance = Chapter.objects.get(id=chapter_id)
            instance = self.filter_queryset(self.get_queryset()).get(id=chapter_id)
        except Chapter.DoesNotExist:
            return api_response(200, '章节ID不存在')
        serializer = ChapterTablesSerializer(instance, context={'request': request})
        return api_response(200, '获取章节列表成功', serializer.data)

    @action(methods=['GET'], detail=False, url_path='owner')
    def owner(self, request, *args, **kwargs):
        """
        获取用户课程列表
        """
        user_id = request.user.id
        if not user_id:
            return api_response(200, '用户Session未正常传输')

        # 获取课程学期ID列表
        course_semester_ids = CourseTeacher.objects.filter(teacher_id=user_id).values_list('course_semester_id',
                                                                                           flat=True)
        # 获取课程ID列表
        course_ids = CourseSemester.objects.filter(id__in=course_semester_ids).values_list('course_id',
                                                                                           flat=True).distinct()
        print('课程ID列表', course_ids)
        title = request.query_params.get('title', '')
        if title:
            queryset = self.filter_queryset(self.get_queryset()).filter(parent__isnull=True,
                                                                        title__icontains=title,
                                                                        course_id__in=course_ids)
        else:
            queryset = self.filter_queryset(self.get_queryset()).filter(parent__isnull=True, course_id__in=course_ids)
        # print(len(queryset))

        # page = self.paginate_queryset(queryset)
        # if page is not None:
        #     # print('进入分页')
        #     serializer = ChapterViewSerializer(page, many=True, context={'request': request})
        #     return self.get_paginated_response(serializer.data)
        # print(queryset)
        serializer = ChapterTablesSerializer(queryset, many=True, context={'request': request})
        return api_response(200, '获取用户课程章节列表成功', serializer.data)


class ClassViewSet(viewsets.ModelViewSet):
    """
    班级视图集
    create:
        创建班级
    retrieve:
        获取班级详情
    update:
        更新班级
    destroy:
        删除班级
    list:
        获取班级列表
    batch_destroy:
        批量删除班级
    """
    queryset = Class.objects.all()
    serializer_class = ClassSerializer
    pagination_class = StandardResultsSetPagination
    permission_map = {
        'batch_destroy': ['course.batch_destroy_class'],
    }

    # permission_classes = [permissions.IsAuthenticated]

    def create(self, request, *args, **kwargs):
        """
        创建班级
        """
        # user_id = request.user.id
        # print(user_id)
        # request.data['user'] = user_id
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return api_response(200, '班级创建成功', serializer.data)

    def list(self, request, *args, **kwargs):
        """
        获取班级列表
        """
        course_semester_id = request.query_params.get('course_semester')
        if not course_semester_id:
            return api_response(200, '课程学期ID参数不能为空')

        queryset = self.filter_queryset(self.get_queryset()).filter(course_semester=course_semester_id)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            response_data = self.get_paginated_response(serializer.data).data
        else:
            serializer = self.get_serializer(queryset, many=True)
            response_data = serializer.data

        return api_response(200, '获取班级列表成功', response_data)

    def update(self, request, *args, **kwargs):
        """
        修改班级
        """
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        return api_response(200, '班级修改成功', serializer.data)

    @action(methods=['post'], detail=False, url_path='batch_destroy')
    def batch_destroy(self, request, *args, **kwargs):
        """
        删除班级
        """
        ids = request.data.get('ids')  # 课程教师ID列表, [1, 2, 3]
        # print(ids)
        for id in ids:
            try:
                instance = Class.objects.get(id=id)
            except Class.DoesNotExist:
                return api_response(200, f'班级ID为{id}不存在')

        Class.objects.filter(id__in=ids).delete()
        return api_response(200, '班级删除成功')


class ClassMemberViewSet(viewsets.GenericViewSet, mixins.ListModelMixin, mixins.CreateModelMixin):
    """
    班级成员视图集
    create:
        创建班级成员
    list:
        获取班级成员列表
    batch_destroy:
        批量删除班级成员
    """
    queryset = ClassMember.objects.all()
    serializer_class = ClassMemberSerializer
    pagination_class = StandardResultsSetPagination

    permission_map = {
        'batch_destroy': ['course.batch_destroy_class_member'],
    }

    # permission_classes = [permissions.IsAuthenticated]

    def create(self, request, *args, **kwargs):
        """
        创建班级成员
        """
        # course_id = request.data.get('course_id')  # 课程ID
        # if not course_id:
        #     return api_response(200, '课程ID参数不能为空')
        course_semester_id = request.data.get('course_semester_id')  # 课程学期ID
        if not course_semester_id:
            return api_response(200, '课程学期ID参数不能为空')

        try:
            course_semester = CourseSemester.objects.get(id=course_semester_id)
        except CourseSemester.DoesNotExist:
            return api_response(200, f'课程学期ID为{course_semester_id}不存在')

        class_obj = request.data.get('class_obj')  # 班级ID
        if not class_obj:
            return api_response(200, '班级ID参数不能为空')

        students = request.data.get('students')  # 学生学号["202409315190","202409315187","202409315186"
        if not students:
            return api_response(200, '学生ID列表不能为空')

        try:
            # 判断班级是否存在
            Class.objects.get(id=class_obj)
        except Class.DoesNotExist:
            return api_response(200, f'班级ID为{class_obj}不存在')

        for student in students:
            try:
                UserInfo.objects.get(username=student)
            except UserInfo.DoesNotExist:
                return api_response(200, f'学号为{student}的学生不存在')

        chapter_ids = Chapter.objects.filter(course_id=course_semester.course_id).values_list('id', flat=True)

        class_members = []
        for student in students:
            user = UserInfo.objects.get(username=student)
            # try:
            #     user = UserInfo.objects.get(username=student)
            # except UserInfo.DoesNotExist:
            #     return api_response(200, f'学号为{student}的学生不存在')
            class_member = ClassMember.objects.get_or_create(student_id=user.id, course_semester_id=course_semester_id,
                                                             defaults={'class_obj_id': class_obj,
                                                                       'student_id': user.id,
                                                                       'course_semester_id': course_semester.id
                                                                       })
            class_members.append(model_to_dict(class_member[0]))

            # 循环章节ID列表，创建学生章节完成记录
            if len(chapter_ids) > 0:
                for chapter_id in chapter_ids:
                    ChapterCompletion.objects.get_or_create(
                        student_id=user.id, chapter_id=chapter_id, course_semester_id=course_semester_id,
                        defaults={'student_id': user.id, 'chapter_id': chapter_id,
                                  'course_semester_id': course_semester_id}
                    )
        return api_response(200, '学生添加成功', class_members)

    @action(methods=['post'], detail=False, url_path='batch_destroy')
    def batch_destroy(self, request, *args, **kwargs):
        """
        删除班级成员(批量)
        """
        ids = request.data.get('ids')  # 课程教师ID列表, [1, 2, 3]
        # print(ids)
        for id in ids:
            try:
                instance = ClassMember.objects.get(id=id)
            except ClassMember.DoesNotExist:
                return api_response(200, f'班级成员ID为{id}不存在')
        ClassMember.objects.filter(id__in=ids).delete()
        return api_response(200, '班级成员删除成功')

    def list(self, request, *args, **kwargs):
        """
        获取班级成员列表
        """
        class_obj = request.query_params.get('class_obj_id')
        stu_name = request.query_params.get('student_name')
        if not class_obj:
            return api_response(200, '班级ID参数不能为空')

        if stu_name:
            queryset = self.filter_queryset(self.get_queryset()).filter(class_obj=class_obj,
                                                                        student__username__icontains=stu_name)
        else:
            queryset = self.filter_queryset(self.get_queryset()).filter(class_obj=class_obj)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            response_data = self.get_paginated_response(serializer.data).data
        else:
            serializer = self.get_serializer(queryset, many=True)
            response_data = serializer.data

        return api_response(200, '获取班级成员', response_data)


class KnowledgePointViewSet(viewsets.GenericViewSet, mixins.UpdateModelMixin, mixins.CreateModelMixin,
                            mixins.DestroyModelMixin, mixins.ListModelMixin):
    """
    知识点视图集
    create:
        创建知识点
    retrieve:
        获取知识点详情
    update:
        更新知识点
    destroy:
        删除知识点
    list:
        获取知识点列表
    """
    queryset = KnowledgePoint.objects.all()
    serializer_class = KnowledgePointSerializer
    graph_client = JanusGraphClient(settings.JANUSGRAPH_URL)  # 图数据库客户端

    # permission_classes = [permissions.IsAuthenticated]

    def list(self, request, *args, **kwargs):
        """
        获取知识点列表
        """
        user_id = request.user.id
        # print(user_id)
        if not user_id:
            return api_response(200, '未正常传输用户Session')

        course_id = request.query_params.get('course_id')
        if not course_id:
            # 获取用户所有课程
            user_course_ids = CourseTeacher.objects.filter(teacher_id=user_id).values_list("course_id", flat=True)
            print('用户所有课程id：', user_course_ids)
            # 获取课程所有章节
            user_course_chapter_ids = Chapter.objects.filter(course_id__in=user_course_ids).values_list("id", flat=True)
            print('课程所有章节id：', user_course_chapter_ids)
            # 获取章节所有知识点
            queryset = KnowledgePoint.objects.filter(chapter_id__in=user_course_chapter_ids)
        else:
            course_chapter_ids = Chapter.objects.filter(course_id=course_id).values_list("id", flat=True)
            queryset = KnowledgePoint.objects.filter(chapter_id__in=course_chapter_ids)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return api_response(200, '获取知识点列表成功', serializer.data)

    def create(self, request, *args, **kwargs):
        """
        创建知识点
        """
        user_id = request.user.id
        chapter_id = request.data.get('chapter')

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)

        # 判断课程是否创建章节知识点知识图谱
        try:
            chapter = Chapter.objects.get(id=chapter_id)
            graph_res = self.graph_client.by_chapter_id_get_nodes(course_id=chapter.course_id, user_id=user_id)

            if graph_res:
                # 课程已创建章节知识点知识图谱，同步创建知识点
                knowledge_data = {
                    "id": serializer.data['id'],
                    "importance": serializer.data['importance'],
                    "knowledge_type": serializer.data['knowledge_type'],
                    "name": serializer.data['name'],
                    "description": serializer.data['description']
                }
                self.graph_client.add_knowledge_point_to_chapter(chapter_id=chapter_id, course_id=chapter.course_id,
                                                                 user_id=user_id, knowledge_data=knowledge_data)
        except Chapter.DoesNotExist:
            logger.error(f'章节ID为{chapter_id}不存在, 知识点同步创建知识图谱失败')

        return api_response(200, '知识点创建成功', serializer.data)

    def update(self, request, *args, **kwargs):
        """
        更新知识点
        """
        user_id = request.user.id

        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        # 判断课程是否创建章节知识点知识图谱
        chapter_id = instance.chapter_id
        try:
            chapter = Chapter.objects.get(id=chapter_id)
            graph_res = self.graph_client.by_chapter_id_get_nodes(course_id=chapter.course_id, user_id=user_id)
            if graph_res:
                # 课程已创建章节知识点知识图谱，更新章节知识点
                node_type = 'knowledge_point'

                properties = {
                    "id": serializer.data['id'],
                    "name": serializer.data['name'],
                    "description": serializer.data['description'],
                    "knowledge_type": serializer.data['knowledge_type'],
                    "importance": serializer.data['importance']
                }

                self.graph_client.update_node_from_properties(properties=properties, node_type=node_type,
                                                              user_id=user_id,
                                                              course_id=chapter.course_id)
        except Chapter.DoesNotExist:
            logger.error(f'章节ID为{chapter_id}不存在, 知识点更新知识图谱失败')

        return api_response(200, '更新知识点成功', serializer.data)

    def destroy(self, request, *args, **kwargs):
        """
        删除知识点
        """
        instance = self.get_object()
        self.perform_destroy(instance)
        return api_response(200, '删除知识点成功')
