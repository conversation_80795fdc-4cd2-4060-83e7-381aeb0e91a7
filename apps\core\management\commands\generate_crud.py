import os
from django.core.management.base import BaseCommand
from django.apps import apps
from django.template import Template, Context
from django.conf import settings

CRUD_TEMPLATES = {
	'serializer': '''
from rest_framework import serializers
from {{ app }}.models import {{ model }}

class {{ model }}Serializer(serializers.ModelSerializer):
    class Meta:
        model = {{ model }}
        fields = '__all__'
''',

	'viewset': '''
from rest_framework import viewsets
from {{ app }}.models import {{ model }}
from .serializers import {{ model }}Serializer

class {{ model }}ViewSet(viewsets.ModelViewSet):
    queryset = {{ model }}.objects.all()
    serializer_class = {{ model }}Serializer
''',

	'urls': '''
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import {{ model }}ViewSet

router = DefaultRouter()
router.register(r'{{ model|lower }}', {{ model }}ViewSet)

urlpatterns = [
    path('', include(router.urls)),
]
'''
}

# 默认排除的 Django 内置应用
DEFAULT_EXCLUDE_APPS = [
	'admin', 'auth', 'contenttypes', 'sessions',
	'messages', 'staticfiles', 'django_celery_beat',
	'django_celery_results', 'debug_toolbar'  # 根据你的项目添加需排除的应用
]


class Command(BaseCommand):
	help = 'Generate CRUD code for specific apps/models (avoid overwriting existing files)'

	def add_arguments(self, parser):
		parser.add_argument(
			'--apps',
			nargs='+',
			help='Specify app names to generate (e.g. --apps book author)'
		)
		parser.add_argument(
			'--models',
			nargs='+',
			help='Specify model names to generate (e.g. --models Book Author)'
		)
		parser.add_argument(
			'--force',
			action='store_true',
			help='Overwrite existing files'
		)

	def handle(self, *args, **options):
		target_apps = options.get('apps')
		target_models = options.get('models')
		force = options.get('force')

		for model in apps.get_models():
			app_name = model._meta.app_label
			model_name = model.__name__

			# 排除内置应用和非目标应用
			if app_name in DEFAULT_EXCLUDE_APPS:
				continue
			if target_apps and app_name not in target_apps:
				continue
			if target_models and model_name not in target_models:
				continue

			self.generate_files(app_name, model_name, force)

	def generate_files(self, app_name, model_name, force=False):
		app_dir = os.path.join(settings.BASE_DIR, 'apps', app_name)
		os.makedirs(app_dir, exist_ok=True)

		# 生成 serializers.py（仅当文件不存在或强制覆盖时）
		self._render_template(
			os.path.join(app_dir, 'serializers.py'),
			CRUD_TEMPLATES['serializer'],
			{'app': f'apps.{app_name}', 'model': model_name},
			force
		)

		# 生成 views.py
		self._render_template(
			os.path.join(app_dir, 'views.py'),
			CRUD_TEMPLATES['viewset'],
			{'app': f'apps.{app_name}', 'model': model_name},
			force
		)

		# 生成 urls.py
		self._render_template(
			os.path.join(app_dir, 'urls.py'),
			CRUD_TEMPLATES['urls'],
			{'app': f'apps.{app_name}', 'model': model_name},
			force
		)

	def _render_template(self, file_path, template_str, context, force=False):
		if os.path.exists(file_path) and not force:
			self.stdout.write(f'Skipped (exists): {file_path}')
			return

		os.makedirs(os.path.dirname(file_path), exist_ok=True)
		with open(file_path, 'w') as f:
			template = Template(template_str)
			f.write(template.render(Context(context)))
		self.stdout.write(f'{"Overwritten" if os.path.exists(file_path) else "Created"}: {file_path}')
