{"1": {"aiid": "1", "answer": [{"blank": "title"}, {"blank": "head"}], "stem": "在HTML中，定义网页的标题的标签是______，它位于______标签内。", "explanation": "在 HTML（超文本标记语言）中，<title>标签用于定义网页的标题，它位于<head>标签内，显示在浏览器的标题栏或标签页上，帮助用户识别网页的主题内容。"}, "2": {"aiid": "2", "answer": [{"blank": "365"}, {"blank": "24"}, {"blank": "木星"}], "stem": "地球围绕太阳公转一周的时间大约是 ______ 天，地球自转一周的时间大约是 ______ 小时，太阳系中体积最大的行星是 ______。", "explanation": "地球公转指绕太阳的运动，周期约为 365 天（精确值为 365.24 天，因此每 4 年设一次闰年）。地球自转是自身绕地轴的旋转，周期约为 24 小时，形成昼夜交替现象。木星是太阳系中质量和体积最大的行星，其体积约为地球的 1300 倍，主要由氢和氦组成"}, "3": {"aiid": "3", "answer": [{"blank": "cd"}], "stem": "Linux 系统中，用于切换目录的命令是 __________", "explanation": "cd /path 切换目录，cat file 显示文件全部内容，less file 支持分页查看"}, "...": {"...": "..."}, "n": {"aiid": "n", "...": "..."}}