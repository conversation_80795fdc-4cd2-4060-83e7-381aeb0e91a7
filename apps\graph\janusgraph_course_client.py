import asyncio
import re
import sys
from typing import List, Dict, Any, Optional
from contextlib import contextmanager

# if sys.platform == "win32":
#     asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
# else:
#     asyncio.set_event_loop_policy(asyncio.SelectorEventLoopPolicy())
import logging
from gremlin_python.structure.graph import Graph
from gremlin_python.driver.driver_remote_connection import DriverRemoteConnection
from gremlin_python.process.anonymous_traversal import traversal
from gremlin_python.process.graph_traversal import __
from gremlin_python.driver.serializer import GraphSONSerializersV3d0
from gremlin_python.driver.serializer import GraphSONMessageSerializer
from gremlin_python.process.traversal import T, Direction
from datetime import datetime
from django.conf import settings
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import local
import threading

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('janusgraph_sync')

# 线程本地存储，为每个线程提供独立的JanusGraph连接,避免共用造成阻塞
thread_local = local()

class JanusGraphSyncTool:
    def __init__(self, janusgraph_url=settings.JANUSGRAPH_URL):
        """初始化JanusGraph连接"""
        self.janusgraph_url = janusgraph_url
        self.graph = Graph()
        # 指定GraphSON3序列化器
        self.serializer = GraphSONMessageSerializer()
        # self.g = None
        self.node_labels = {
            'course': 'course',
            'chapter': 'chapter',
            'section': 'section',
            'knowledge_point': 'knowledge_point'
        }
        self.edge_labels = {
            'course_to_chapter': 'has_chapter',
            'chapter_to_section': 'has_section',
            'section_to_knowledge': 'has_knowledge'
        }
        # 初始化连接
        # self._init_connection()
        self._g = None

    @property
    def g(self):
        """获取线程本地的JanusGraph遍历对象"""
        if not hasattr(thread_local, 'g'):
            self._init_connection()
        return thread_local.g

    def _init_connection(self):
        """初始化JanusGraph连接"""
        try:
            thread_local.g = self.graph.traversal().withRemote(
                DriverRemoteConnection(self.janusgraph_url, 'g', message_serializer=self.serializer)
            )
            logger.info(f"线程 {threading.get_ident()} 成功连接到JanusGraph")
        except Exception as e:
            logger.error(f"线程 {threading.get_ident()} 连接JanusGraph失败: {e}")
            raise

    def _preprocess_data(self, data):
        """预处理数据，转换不支持的类型"""
        if isinstance(data, dict):
            return {k: self._preprocess_data(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._preprocess_data(item) for item in data]
        elif data is None:
            return ""  # 将None转换为空字符串
        elif isinstance(data, datetime):
            return data.isoformat()  # 转换日期时间为字符串
        else:
            return data

    def clear_existing_data(self, course_id, user_id):
        """清除JanusGraph中现有的课程结构数据"""
        try:
            logger.info(f"开始清除课程 {course_id} 用户 {user_id} 的JanusGraph数据")
            # self.g.V().hasLabel(*self.node_labels.values()).drop().iterate()
            self.g.V().has('course_id', course_id).has('user_id', user_id).drop().iterate()
            logger.info(f"已成功清除课程 {course_id} 用户 {user_id} 的JanusGraph数据")
        except Exception as e:
            logger.error(f"清除数据时出错: {e}")
            raise

    def sync_course_structure(self, course_data, user_id, course_id):
        """同步课程四级结构到JanusGraph"""
        try:
            # 开始同步前清除现有数据
            self.clear_existing_data(course_id, user_id)

            # 预处理数据
            preprocessed_data = self._preprocess_data(course_data)

            for course in preprocessed_data:
                # 创建课程节点
                course_node = self._create_course_node(course, user_id, course_id)

                # 处理章节
                for chapter in course.get('chapters', []):
                    chapter_node = self._create_chapter_node(chapter, user_id, course_id)
                    # 创建课程到章节的边
                    # self.g.V(course_node).addE(self.edge_labels['course_to_chapter']).to(chapter_node).iterate()
                    self.g.V(course_node).addE(self.edge_labels['course_to_chapter']) \
                        .property('courseID', course_id) \
                        .property('userID', user_id) \
                    .to(chapter_node).iterate()

                    # 处理子章节（节）
                    for section in chapter.get('children', []):
                        section_node = self._create_section_node(section, user_id, course_id)
                        # 创建章节到节的边
                        # self.g.V(chapter_node).addE(self.edge_labels['chapter_to_section']).to(section_node).iterate()
                        self.g.V(chapter_node).addE(self.edge_labels['chapter_to_section']) \
                            .property('courseID', course_id) \
                            .property('userID', user_id) \
                        .to(section_node).iterate()

                        # 处理知识点
                        for knowledge_point in section.get('knowledge_points', []):
                            knowledge_node = self._create_knowledge_point_node(knowledge_point, user_id, course_id)
                            # 创建节到知识点的边
                            # self.g.V(section_node).addE(self.edge_labels['section_to_knowledge']).to(
                            #     knowledge_node).iterate()
                            self.g.V(section_node).addE(self.edge_labels['section_to_knowledge']) \
                                .property('courseID', course_id) \
                                .property('userID', user_id) \
                            .to(knowledge_node).iterate()

            logger.info(f"课程 {course_id} 用户 {user_id} 结构同步完成")
        except Exception as e:
            logger.error(f"同步过程中出错: {e}")
            raise

    def _create_course_node(self, course, user_id, course_id):
        """创建课程节点"""
        return self.g.addV(self.node_labels['course']) \
            .property('id', course['id']) \
            .property('name', course['title']) \
            .property('description', course.get('description', '')) \
            .property('user_id', user_id) \
            .property('course_id', course_id) \
            .next()

    def _create_chapter_node(self, chapter, user_id, course_id):
        """创建章节节点"""
        return self.g.addV(self.node_labels['chapter']) \
            .property('id', chapter['id']) \
            .property('name', chapter['title']) \
            .property('content', chapter.get('content', '')) \
            .property('order', chapter.get('order', 0)) \
            .property('user_id', user_id) \
            .property('course_id', course_id) \
            .next()

    def _create_section_node(self, section, user_id, course_id):
        """创建节节点"""
        return self.g.addV(self.node_labels['section']) \
            .property('id', section['id']) \
            .property('name', section['title']) \
            .property('content', section.get('content', '')) \
            .property('order', section.get('order', 0)) \
            .property('user_id', user_id) \
            .property('course_id', course_id) \
            .next()

    def _create_knowledge_point_node(self, knowledge_point, user_id, course_id):
        """创建知识点节点"""
        return self.g.addV(self.node_labels['knowledge_point']) \
            .property('id', knowledge_point['id']) \
            .property('name', knowledge_point['name']) \
            .property('description', knowledge_point.get('description', '')) \
            .property('knowledge_type', knowledge_point.get('knowledge_type', '')) \
            .property('importance', knowledge_point.get('importance', 0)) \
            .property('user_id', user_id) \
            .property('course_id', course_id) \
            .next()

    def get_course_structure(self, course_id=None):
        """从JanusGraph获取课程结构"""
        try:
            if course_id:
                # 获取特定课程的结构
                course = self.g.V().has(self.node_labels['course'], 'id', course_id).next()
                return self._build_course_tree(course)
            else:
                # 获取所有课程的结构
                courses = self.g.V().hasLabel(self.node_labels['course']).toList()
                return [self._build_course_tree(course) for course in courses]
        except Exception as e:
            logger.error(f"获取课程结构时出错: {e}")
            raise

    def _build_course_tree(self, course_vertex):
        """递归构建课程树结构"""
        course = self.g.V(course_vertex).valueMap().next()
        course = {k: v[0] if isinstance(v, list) and len(v) == 1 else v for k, v in course.items()}

        # 获取章节
        chapters = self.g.V(course_vertex).out(self.edge_labels['course_to_chapter']).toList()
        course['chapters'] = []

        for chapter_vertex in chapters:
            chapter = self.g.V(chapter_vertex).valueMap().next()
            chapter = {k: v[0] if isinstance(v, list) and len(v) == 1 else v for k, v in chapter.items()}

            # 获取节
            sections = self.g.V(chapter_vertex).out(self.edge_labels['chapter_to_section']).toList()
            chapter['sections'] = []

            for section_vertex in sections:
                section = self.g.V(section_vertex).valueMap().next()
                section = {k: v[0] if isinstance(v, list) and len(v) == 1 else v for k, v in section.items()}

                # 获取知识点
                knowledge_points = self.g.V(section_vertex).out(self.edge_labels['section_to_knowledge']).toList()
                section['knowledge_points'] = [
                    {k: v[0] if isinstance(v, list) and len(v) == 1 else v
                     for k, v in self.g.V(kp).valueMap().next().items()}
                    for kp in knowledge_points
                ]

                chapter['sections'].append(section)

            course['chapters'].append(chapter)

        return course

    def get_node_details(self, node_type, node_id):
        """根据节点类型和ID获取节点详情"""
        try:
            label = self.node_labels.get(node_type)
            if not label:
                raise ValueError(f"未知的节点类型: {node_type}")

            node = self.g.V().has(label, 'id', node_id).valueMap().next()
            return {k: v[0] if isinstance(v, list) and len(v) == 1 else v for k, v in node.items()}
        except Exception as e:
            logger.error(f"获取节点详情时出错: {e}")
            raise

    def by_id_get_all_nodes(self, course_id, user_id):
        """根据课程ID和用户ID获取所有节点"""
        try:
            course = int(course_id)
            user = int(user_id)
            nodes = self.g.V().has('course_id', course).has('user_id', user).element_map().toList()
            results = []
            for node in nodes:
                node_info = {
                    'id': node[T.id],
                    'label': node[T.label],
                    'properties': {k: v for k, v in node.items() if k not in [T.id, T.label]}
                }
                results.append(node_info)
            return results
        except Exception as e:
            logger.error(f"根据任务ID获取节点失败: {str(e)}")
            raise

    def by_id_get_all_edges(self, course_id, user_id):
        """根据课程ID和用户ID获取所有边"""
        try:
            course = int(course_id)
            user = int(user_id)
            edges = self.g.E().has('courseID', course).has('userID', user).element_map().to_list()
            result = []
            for edge in edges:
                edge_info = {
                    'id': str(edge[T.id]),
                    'label': edge[T.label],
                    'source': edge[Direction.OUT][T.id],
                    'target': edge[Direction.IN][T.id],
                    'userID': edge['userID'],
                    'courseID': edge['courseID']
                }
                result.append(edge_info)
            return result
        except Exception as e:
            logger.error(f"根据任务ID获取边失败: {str(e)}")
            raise


    def by_course_id_get_all_data(self, course_id, user_id):
        """根据课程ID和用户ID获取所有节点和边数据"""
        try:
            result = {}
            nodes = self.by_id_get_all_nodes(course_id, user_id)
            edges = self.by_id_get_all_edges(course_id, user_id)
            result['nodes'] = nodes
            result['edges'] = edges
            return result
        except Exception as e:
            logger.error(f"根据任务ID获取所有数据失败: {str(e)}")
            raise


# Windows系统事件循环配置
def setup_windows_event_loop():
    """设置Windows系统的事件循环"""
    if hasattr(asyncio, 'WindowsSelectorEventLoopPolicy'):
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# 限制最大工作线程数，防止资源耗尽
MAX_WORKERS = getattr(settings, 'JANUSGRAPH_SYNC_MAX_WORKERS', 5)
sync_executor = ThreadPoolExecutor(max_workers=MAX_WORKERS)


# 在单独线程中运行JanusGraph同步
def run_janusgraph_sync_in_thread(course_data, user_id, course_id):
    """在单独线程中运行JanusGraph同步"""
    setup_windows_event_loop()

    def sync_task():
        try:
            sync_tool = JanusGraphSyncTool()
            sync_tool.sync_course_structure(course_data, user_id, course_id)
            return "同步完成"
        except Exception as e:
            logger.error(f"同步过程中出错: {e}")
            return f"同步失败: {str(e)}"

    future = sync_executor.submit(sync_task)
    return future.result()

    # with ThreadPoolExecutor() as executor:
    #     future = executor.submit(sync_task)
    #     return future.result()