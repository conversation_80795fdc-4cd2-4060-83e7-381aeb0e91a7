import json
from typing import Dict, Any, <PERSON>, override, <PERSON><PERSON>

from typing import List, Dict, Any, TypedDict

from PyPDF2.generic import Bookmark
from django.http import StreamingHttpResponse

from chat.constants import SENSITIVE_RESPONSE_CONTENT
from chat.safety.utils import safety_filter


def get_proto_content(part: Dict[str, Any]) -> str:
    """获取不同类型part的内容"""
    part_type = part.get("type")
    if part_type == "text":
        return part.get("text", "")
    elif part_type == "reasoning":
        return part.get("reasoning", "")
    elif part_type == "source":
        source = part.get("source", {})
        return source.get("title", "")
    return ""


class ProtoTextUIPart(TypedDict):
    text: str
    type: str  # 'text'


class ProtoReasoningUIPart(TypedDict):
    reasoning: str
    # detail:list[ProtoTextUIPart]
    type: str  # 'reasoning'


class ProtoLanguageModelV1Source(TypedDict):
    id: str
    url: str
    title: str
    sourceType: str  # url


class ProtoSourceUIPart(TypedDict):
    source: ProtoLanguageModelV1Source
    type: str  # source


def create_proto_instance(instance: Union[ProtoTextUIPart, ProtoReasoningUIPart, ProtoSourceUIPart]):
    """创建ProtoTextUIPart、ProtoReasoningUIPart或ProtoSourceUIPart的实例"""
    if not isinstance(instance, dict):
        raise ValueError("Instance must be a dictionary")

    instance_type = instance.get("type")

    if instance_type == "text":
        return {"text": instance.get("text", ""), "type": "text"}
    elif instance_type == "reasoning":
        return {"reasoning": instance.get("reasoning", ""), "type": "reasoning"}
    elif instance_type == "source":
        source = instance.get("source", {})
        return {
            "source": {
                "id": source.get("id", ""),
                "url": source.get("url", ""),
                "title": source.get("title", ""),
                "sourceType": source.get("sourceType", "url")
            },
            "type": "source"
        }
    else:
        raise ValueError(f"Unsupported type '{instance_type}' for Proto instance creation")


def append_proto_instance(last_instance, item: Union[ProtoTextUIPart, ProtoReasoningUIPart]):
    """将新的item添加到last_instance中"""

    item_type = item.get("type")

    if item_type == "text":
        last_instance["text"] += item.get("text", "")
    elif item_type == "reasoning":
        last_instance["reasoning"] += item.get("reasoning", "")
    else:
        raise ValueError(f"Unsupported type '{item_type}' for appending to Proto instance")
    return last_instance


class ChuckData(TypedDict):
    content: str
    content_ltks: str
    dataset_id: str
    document_id: str
    document_keyword: str  # test_pdf_loader.pdf
    highlight: str  # <em>xxxx</em>
    id: str
    image_id: str  # e2255cc2450611f0b5ad8e1f51ec9198-52ca19ff728908ef
    important_keywords: list
    positions: list
    similarity: float  # 0.7142960975205777
    term_similarity: float  # 0.8280383374747217
    vector_similarity: float  # 0.4488975376275752


class DocAggData(TypedDict):
    count: int  # 11
    doc_id: str
    doc_name: str  # test_pdf_loader.pdf


class RetrieveChuckRespData(TypedDict):
    chunks: list[ChuckData]
    doc_aggs: list[DocAggData]
    total: int


class PartsHandler:
    """处理ChatMessage模型中parts字段的TypedDict处理"""

    @staticmethod
    def create_text_part(text: str) -> ProtoTextUIPart:
        """创建文本类型的part"""
        return {"text": text, "type": "text"}

    @staticmethod
    def create_reasoning_part(reasoning: str) -> ProtoReasoningUIPart:
        """创建推理类型的part"""
        return {"reasoning": reasoning, "type": "reasoning"}

    @staticmethod
    def create_source_part(id: str, url: str, title: str, sourceType: str = "url") -> ProtoSourceUIPart:
        """创建源引用类型的part"""
        source: ProtoLanguageModelV1Source = {
            "id": id,
            "url": url,
            "title": title,
            "sourceType": sourceType
        }
        return {"source": source, "type": "source"}

    @staticmethod
    def create_source2text_part(text: str) -> ProtoTextUIPart:
        """创建文本类型的part"""
        return {"text": text, "type": "text"}

    @staticmethod
    def validate_parts_structure(parts: List[Dict[str, Any]]) -> bool:
        """验证parts数据结构是否有效"""
        if not isinstance(parts, list):
            return False

        valid_types = {"text", "reasoning", "source"}

        for part in parts:
            if not isinstance(part, dict):
                return False

            part_type = part.get("type")
            if part_type not in valid_types:
                return False

            # 验证每种类型的必需字段
            if part_type == "text" and "text" not in part:
                return False
            elif part_type == "reasoning" and "reasoning" not in part:
                return False
            elif part_type == "source":
                source_data = part.get("source")
                if not isinstance(source_data, dict):
                    return False
                required_source_fields = {"id", "url", "title"}
                if not all(field in source_data for field in required_source_fields):
                    return False

        return True


class StreamChunk:

    @staticmethod
    def text(content: str) -> str:
        """文本块 0:string\n (Type ID=0)"""
        return f'0:{json.dumps(content, ensure_ascii=False)}\n'

    @staticmethod
    def source(content: dict) -> bytes:
        # 'h:{"sourceType": "url", "id": "source-id", "url": "http://www.baidu.com", "title": "Example"}\n'.encode('utf-8')
        rect = f'h:{json.dumps(content, ensure_ascii=False)}\n'.encode('utf-8')
        return rect

    def source_to_text(content: str) -> str:
        return f'0:{json.dumps(content, ensure_ascii=False)}\n'

    @staticmethod
    def reasoning(content: str) -> bytes:
        """推理块 g:string\\n"""
        # return f'g:{json.dumps(content,ensure_ascii=False)}\n'
        return f'g:{json.dumps(content, ensure_ascii=False)}\n'.encode('utf-8')

    @staticmethod
    def redacted_reasoning(data: str) -> bytes:
        """脱敏推理块 i:{"data": string}\\n"""
        # return f'i:{json.dumps({"data": data})}\n'.encode('utf-8')

        # return f'i:{json.dumps({"data": data},  ensure_ascii=False)}\n'

        return f'i:{json.dumps({"data": data}, ensure_ascii=False)}\n'.encode('utf-8')

    @staticmethod
    def finish_message(finish_reason: str, usage: Dict[str, int]) -> bytes:
        """结束消息块 d:{finishReason, usage}\\n"""
        # return f'd:{json.dumps({"finishReason": finish_reason, "usage": usage})}\n'.encode('utf-8')
        return f'd:{json.dumps({"finishReason": finish_reason, "usage": usage}, ensure_ascii=False)}\n'.encode('utf-8')

    # 其他类型块按同样模式实现...
    @staticmethod
    def error(message: str) -> bytes:
        """错误块 3:string\\n"""
        return f'3:{json.dumps(message, ensure_ascii=False)}\n'.encode('utf-8')

    @staticmethod
    def tool_call(tool_call_id: str, tool_name: str, args: Dict[str, Any]) -> bytes:
        """工具调用块 9:{toolCallId, toolName, args}\\n"""
        return f'9:{json.dumps({"toolCallId": tool_call_id, "toolName": tool_name, "args": args}, ensure_ascii=False)}\n'.encode(
            'utf-8')

    @staticmethod
    def finish_step(
            finish_reason: str,
            usage: Dict[str, int],
            is_continued: bool
    ) -> bytes:
        """结束步骤块 e:{finishReason, usage, isContinued}\\n"""
        return f'e:{json.dumps({
            "finishReason": finish_reason,
            "usage": usage,
            "isContinued": is_continued
        }, ensure_ascii=False)}\n'.encode('utf-8')

    @staticmethod
    def title(title: str):
        return f'2:{json.dumps([{"title": title}], ensure_ascii=False)}\n'


def stream_finished(chunk):
    """
    流结束 判断
    """
    return 'finish_reason' in chunk.response_metadata and chunk.response_metadata['finish_reason'] == 'stop'


class StreamProcessor:
    """流式处理器，解决ai-sdk流式协议问题"""

    def __init__(self, agent, stream, callback, enable_thinking, request):
        self.agent = agent
        self.stream = stream
        self.callback = callback
        self.enable_thinking = enable_thinking
        self.start_thinking = not enable_thinking  # 解决大模型在思考之前返回正文信息的问题
        self.ai_messages_chunk = []
        self.full_content = ''  # 储存大模型无协议格式的 内容
        self.request = request
        self.sensitive_interrupt = False  # 敏感中断

    @property
    def conversation_id(self):
        return self.agent.conversation_id

    @property
    def config(self):
        return self.agent.config

    # @property
    # def retrieve_data(self):
    #     return self.agent.retrieve_data

    @property
    def fake_id_map(self):
        return self.agent.fake_id_map

    @property
    def chunk_id_map_doc(self):
        return self.agent.chunk_id_map_doc

    @property
    def doc_id_map_name(self):
        return self.agent.doc_id_map_name

    def on_finish(self):
        pass

    def append_full_content(self, text) -> Tuple[str, bool]:
        self.full_content += text
        find_keyword, match = safety_filter.filter_text(self.full_content, self.request.user.id)
        self.sensitive_interrupt = match
        return find_keyword, match

    def process_with_thinking_mix(self):
        """处理模型响应流，处理thinking在content中时或者thinking��reasoning_content中的情况
        """
        is_data_type = True  # 假设总是使用data协议

        # 状态机
        state = "normal"
        buffer = ""
        ref_content = ""
        ref_count = 0

        # 定义匹配模式
        ref_start = "<ref>"
        ref_end = "</ref>"
        think_start = "<think>"
        think_end = "</think>"
        # 状态类型
        # in_match_start='in_match_start'
        in_pre_match = 'in_pre_match'
        in_ref_start = 'in_ref_start'
        in_ref_content = 'in_ref_content'
        in_think_start = 'in_think_start'
        in_think_content = 'in_think_content'
        in_think_end = 'in_think_end'
        normal = 'normal'

        think_content = ''

        def yield_text(text):
            # print(f'正文{text}')
            # if  not text or not text.strip():
            #     return None
            if self.sensitive_interrupt:
                return StreamChunk.text(text) if is_data_type else text
            self.append_full_content(text)
            if not self.start_thinking:
                return None
            pd = PartsHandler.create_text_part(text=text)
            self.ai_messages_chunk.append(pd)
            return StreamChunk.text(text) if is_data_type else text

        def yield_source2text(text: str, uuid: str, fake_id: str):
            # print(f'引用{text}')
            self.append_full_content(text)
            order = f'[{fake_id}]'
            url = f'/api/v1/chat/view_chunk_detail/{self.chunk_id_map_doc[text]}/{text}/{uuid}/'
            title = self.doc_id_map_name[self.chunk_id_map_doc[text]]
            combined_text = f'{order}({url} "{title}")'

            pd = PartsHandler.create_source2text_part(combined_text)
            self.ai_messages_chunk.append(pd)
            return StreamChunk.source_to_text(combined_text)

        def yield_source(text: str, uuid: str):
            # print(f'引用{text}')
            combined_text = {
                "sourceType": "url",
                "id": f'{text}🔗',  # todo 临时加标，之后根据前端要求再改 或直接去掉，下方地址需要修改为相对路径
                "url": f'/api/v1/chat/view_chunk_detail/{self.chunk_id_map_doc[text]}/{text}/{uuid}/',
                "title": self.doc_id_map_name[self.chunk_id_map_doc[text]]
            }
            pd = PartsHandler.create_source_part(**combined_text)
            self.ai_messages_chunk.append(pd)
            return StreamChunk.source(combined_text)

        def yield_thinking(text: str):
            # print(f'think: {text}')
            self.append_full_content(text)
            if not self.start_thinking:
                self.start_thinking = True
            pd = PartsHandler.create_reasoning_part(reasoning=text)
            self.ai_messages_chunk.append(pd)
            return StreamChunk.reasoning(text)

        for chunk, meta_data in self.stream:
            if meta_data['langgraph_node'] != 'model':
                continue
            # 敏感中断
            if self.sensitive_interrupt:
                yield yield_text(SENSITIVE_RESPONSE_CONTENT)
                #提前结束
                break

            # ai_messages_chunk.append(chunk.content)
            chunk_text = chunk.content

            # 检查流结束信号
            if stream_finished(chunk):
                self.on_finish()
                title = self.callback(self.ai_messages_chunk)
                if title:
                    yield StreamChunk.title(title)
                yield StreamChunk.finish_message('stop', {'input_tokens': 0, 'output_tokens': 0})
                # break #不能解开，后边还有一空块

            # 将当前chunk添加到总缓冲区
            buffer += chunk_text

            # print(f'{state}:{buffer}')
            # print(chunk)
            def inner():
                nonlocal state, buffer, ref_content, ref_count, think_content
                pre_buffer = ""
                # 思考部分
                if chunk.additional_kwargs:
                    think_content = chunk.additional_kwargs['reasoning_content']
                    chunk.additional_kwargs = {}  # 删除，保证不出现在检查点中，减少token消耗
                    return yield_thinking(think_content)
                # print(f'{state}:{buffer}')
                if state == normal:
                    if '<' in buffer:
                        state = in_pre_match
                        return None
                    tmp_buf = buffer
                    buffer = ''
                    return yield_text(tmp_buf)
                elif state == in_pre_match:
                    if len(buffer) < 2:  # 避免当前buffer还是：‘<’的情况
                        return None
                    # 尝试匹配<ref>标签
                    ref_match = False
                    match_i = 1
                    for i in range(2, len(ref_start)):
                        if ref_start[:i] in buffer:
                            ref_match = True
                            match_i = i
                    if ref_match:
                        state = in_ref_start
                        pre_buffer = buffer[:buffer.index(ref_start[:match_i])]
                        buffer = buffer[len(pre_buffer):]
                        return yield_text(pre_buffer)
                    # 尝试匹配<think>标签
                    think_match = False
                    for i in range(2, len(think_start)):
                        if think_start[:i] in buffer:
                            think_match = True
                            match_i = i
                    if think_match:
                        state = in_think_start
                        pre_buffer = buffer[:buffer.index(think_start[:match_i])]
                        buffer = buffer[len(pre_buffer):]
                        return yield_text(pre_buffer)
                    # 如果虽然匹配了<,但既不是ref也不是think，则回到普通模式
                    tmp_buf = buffer
                    buffer = ''
                    state = normal
                    return yield_text(tmp_buf)
                elif state == in_ref_start:
                    if buffer.startswith(ref_start):
                        state = in_ref_content
                    elif buffer not in ref_start:  # 校验是否并不是<ref>，可能是<reff> /<reg /<ra ....
                        tmp_buffer = buffer
                        buffer = ''
                        state = normal
                        return yield_text(tmp_buffer)
                    return None
                elif state == in_ref_content:
                    if ref_end in buffer:
                        ref_content = buffer[:buffer.index(ref_end)].replace(ref_start, '').replace('[', '').replace(
                            ']', '')
                        buffer = buffer[buffer.index(ref_end) + len(ref_end):]  # 截取掉</ref>之前的内容，也包括</ref>
                        state = normal

                        # 验证大模型是否有错误的处理
                        if (ref_content in self.fake_id_map and
                                (trust_trunk_id := self.fake_id_map[ref_content][0]) in self.chunk_id_map_doc):
                            ref_count += 1
                            # buffer += f'☯️{ref_count}' #todo 先不返回给前端这个符号了
                            buffer += ''
                            # print(f'=------- {ref_content}')
                            uuid = self.fake_id_map[ref_content][1]
                            return yield_source2text(trust_trunk_id, uuid, ref_content)
                        else:
                            print(f'大模型处理数据时错误修改了thunk_id：{ref_content},按普通文本处理，不再作为引用部分')
                            return yield_text(ref_content)
                    return None
                elif state == in_think_start:
                    if buffer.startswith(think_start):
                        state = in_think_content
                        buffer = buffer.replace(think_start, '')
                        think_content = buffer
                        return yield_thinking(buffer)
                    elif buffer not in think_start:
                        tmp_buffer = buffer
                        buffer = ''
                        state = normal
                        return yield_text(tmp_buffer)
                    return None
                elif state == in_think_content:
                    think_content += buffer
                    # 可能要触发标签结尾阶段了
                    if '<' in buffer:
                        state = in_think_end
                        # 把<之前的内容先返回
                        tmp_buffer = buffer[:buffer.index('<')]
                        return yield_thinking(tmp_buffer)
                    tmp_buffer = buffer
                    buffer = ''
                    return yield_thinking(tmp_buffer)
                elif state == in_think_end:
                    if len(buffer) < len(think_end):
                        return None
                    if think_end in think_content:
                        buffer = buffer.replace(think_end, '')
                        state = normal
                        tmp_buffer = buffer
                        buffer = ''
                        return yield_thinking(tmp_buffer)
                    else:
                        state = in_think_content
                        tmp_buffer = buffer
                        buffer = ''
                        return yield_thinking(tmp_buffer)
                else:
                    print('---------测试不可达--------')
                    tmp_buf = buffer
                    buffer = ''
                    return yield_text(tmp_buf)

            result = inner()
            if result:
                yield result

    def process(self):
        """处理模型响应流， 后续用不上的话就删了，没有处理思考的部分"""

        is_data_type = True  # 假设总是使用data协议

        # 状态机
        state = "normal"
        buffer = ""
        ref_content = ""
        ref_count = 0

        # 定义匹配模式
        ref_start = "<ref>"
        ref_end = "</ref>"

        def yield_text(text):
            # print(f'正文{text}')
            return StreamChunk.text(text) if is_data_type else text

        def yield_source(text: str, uuid: str):
            # print(f'引用{text}')
            combined_text = {
                "sourceType": "url",
                "id": f'{text}🔗',  # todo 临时加标，之后根据前端要求再改 或直接去掉，下方地址需要修改为相对路径
                "url": f'http://localhost:8001/api/v1/chat/view_chunk_detail/{self.chunk_id_map_doc[text]}/{text}/{uuid}/',
                "title": self.doc_id_map_name[self.chunk_id_map_doc[text]]
            }
            return StreamChunk.source(combined_text)

        for chunk, meta_data in self.stream:
            if meta_data['langgraph_node'] != 'model':
                continue
            self.ai_messages_chunk.append(chunk.content)
            chunk_text = chunk.content

            # 检查流结束信号
            if stream_finished(chunk):
                self.callback(self.ai_messages_chunk)
                yield StreamChunk.finish_message('stop', {'input_tokens': 0, 'output_tokens': 0})
                # break #不能解开，后边还有一空块

            # 将当前chunk添加到总缓冲区
            buffer += chunk_text

            def inner():
                nonlocal state, buffer, ref_content, ref_count
                pre_buffer = ""

                if state == 'normal':
                    match = False
                    match_i = 1
                    for i in range(1, len(ref_start)):
                        if ref_start[:i] in buffer:
                            match = True
                            match_i = i
                    if match:
                        state = "in_ref_start"
                        pre_buffer = buffer[:buffer.index(ref_start[:match_i])]
                        buffer = buffer[len(pre_buffer):]
                        return yield_text(pre_buffer)
                    tmp_buf = buffer
                    buffer = ''
                    return yield_text(tmp_buf)
                elif state == "in_ref_start":
                    if buffer.startswith(ref_start):
                        state = "in_ref_content"
                    elif buffer not in ref_start:
                        tmp_buffer = buffer
                        buffer = ''
                        return yield_text(tmp_buffer)
                    return None
                elif state == "in_ref_content":
                    if ref_end in buffer:
                        ref_content = buffer[:buffer.index(ref_end)].replace(ref_start, '').replace('[', '').replace(
                            ']', '')
                        buffer = buffer[buffer.index(ref_end) + len(ref_end):]
                        state = "normal"

                        # 验证大模型是否有错误的处理
                        if (ref_content in self.fake_id_map and
                                (trust_trunk_id := self.fake_id_map[ref_content][0]) in self.chunk_id_map_doc):
                            ref_count += 1
                            # buffer += f'☯️{ref_count}' #todo 先不返回给前端这个符号了
                            buffer += ''
                            # print(f'=------- {ref_content}')
                            uuid = self.fake_id_map[ref_content][1]
                            return yield_source(trust_trunk_id, uuid)
                        else:
                            print(f'大模型处理数据时错误修改了thunk_id：{ref_content}')
                            return yield_text(ref_content)
                    return None
                else:
                    print('---------测试不可达--------')
                    tmp_buf = buffer
                    buffer = ''
                    return yield_text(tmp_buf)

            result = inner()

            if result:
                yield result

    def process_with_thinking(self):
        """处理模型响应流,处理thinking部分【处理 think内容在reason中的情况】，只能结合CustomChatQwQ使用"""

        is_data_type = True  # 假设总是使用data协议

        # 状态机
        state = "normal"
        buffer = ""
        ref_content = ""
        ref_count = 0

        # 定义匹配模式
        ref_start = "<ref>"
        ref_end = "</ref>"

        def yield_text(text):
            # print(f'正文{text}')
            return StreamChunk.text(text) if is_data_type else text

        def yield_source(text: str, uuid: str):
            # print(f'引用{text}')
            combined_text = {
                "sourceType": "url",
                "id": f'{text}🔗',  # todo 临时加标，之后根据前端要求再改 或直接去掉，下方地址需要修改为相对路径
                "url": f'http://localhost:8001/api/v1/chat/view_chunk_detail/{self.chunk_id_map_doc[text]}/{text}/{uuid}/',
                "title": self.doc_id_map_name[self.chunk_id_map_doc[text]]
            }
            return StreamChunk.source(combined_text)

        for chunk, meta_data in self.stream:
            if meta_data['langgraph_node'] != 'model':
                continue
            self.ai_messages_chunk.append(chunk.content)
            chunk_text = chunk.content

            # 检查流结束信号
            if stream_finished(chunk):
                self.callback(self.ai_messages_chunk)
                print('finish')
                yield StreamChunk.finish_message('stop', {'input_tokens': 0, 'output_tokens': 0})
                # break #不能解开，后边还有一空块

            # 将当前chunk添加到总缓冲区
            buffer += chunk_text

            def inner():
                nonlocal state, buffer, ref_content, ref_count
                pre_buffer = ""
                # 思考部分
                if chunk.additional_kwargs:
                    think_content = chunk.additional_kwargs['reasoning_content']
                    return StreamChunk.reasoning(think_content)

                if state == 'normal':
                    match = False
                    match_i = 1
                    for i in range(1, len(ref_start)):
                        if ref_start[:i] in buffer:
                            match = True
                            match_i = i
                    if match:
                        state = "in_ref_start"
                        pre_buffer = buffer[:buffer.index(ref_start[:match_i])]
                        buffer = buffer[len(pre_buffer):]
                        return yield_text(pre_buffer)
                    tmp_buf = buffer
                    buffer = ''
                    return yield_text(tmp_buf)
                elif state == "in_ref_start":
                    if buffer.startswith(ref_start):
                        state = "in_ref_content"
                    elif buffer not in ref_start:
                        tmp_buffer = buffer
                        buffer = ''
                        return yield_text(tmp_buffer)
                    return None
                elif state == "in_ref_content":
                    if ref_end in buffer:
                        ref_content = buffer[:buffer.index(ref_end)].replace(ref_start, '').replace('[', '').replace(
                            ']', '')
                        buffer = buffer[buffer.index(ref_end) + len(ref_end):]
                        state = "normal"

                        # 验证大模型是否有错误的处理
                        if (ref_content in self.fake_id_map and
                                (trust_trunk_id := self.fake_id_map[ref_content][0]) in self.chunk_id_map_doc):
                            ref_count += 1
                            # buffer += f'☯️{ref_count}' #todo 先不返回给前端这个符号了
                            buffer += ''
                            # print(f'=------- {ref_content}')
                            uuid = self.fake_id_map[ref_content][1]
                            return yield_source(trust_trunk_id, uuid)
                        else:
                            print(f'大模型处理数据时错误修改了thunk_id：{ref_content}')
                            return yield_text(ref_content)
                    return None
                else:
                    print('---------测试不可达--------')
                    tmp_buf = buffer
                    buffer = ''
                    return yield_text(tmp_buf)

            result = inner()

            if result:
                yield result


class CatchableStreamProcessor(StreamProcessor):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.finished = False

    @override
    def on_finish(self):
        self.finished = True

    def __iter__(self):
        try:
            for chunk in self.process_with_thinking_mix():
                yield chunk
        except GeneratorExit:
            if not self.finished and self.callback:
                self.callback(self.ai_messages_chunk)
            raise GeneratorExit


class InterruptibleStreamingHttpResponse(StreamingHttpResponse):
    def __init__(self, processor: CatchableStreamProcessor, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.processor = processor
        # self.ai_messages_chunk = kwargs['ai_messages_chunk']

    def close(self):
        if not self.processor.finished and self.processor.callback:
            self.processor.callback(self.processor.ai_messages_chunk)
        super().close()
