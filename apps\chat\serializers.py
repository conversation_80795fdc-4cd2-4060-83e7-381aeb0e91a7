from rest_framework import serializers
from rest_framework.fields import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.relations import PrimaryKeyRelatedField
from rest_framework.serializers import Serializer, ModelSerializer
from rest_framework.validators import UniqueValidator

from chat.models import Conversation, ChatMessage, KeywordRule, CommonFile
from course.models import Course
from knowledge_base.models import KnowledgeBase


class ChatCreateSerializer(serializers.Serializer):
    conversation_id = CharField(max_length=100, required=True,help_text='会话id')
    question = CharField(max_length=5000,required=True)
    stream_protocol =CharField(max_length=20,default='data')
    dataset_ids = ListField(
        child=PrimaryKeyRelatedField(
            queryset=KnowledgeBase.objects.all(),
            error_messages={
                'does_not_exist': '知识库ID "{pk_value}" 不存在',
            }
        ),
        required=False,
        error_messages={
            'not_a_list': '知识库ID列表格式不正确'
        }
    )
    document_ids =ListField(child=Char<PERSON>ield(max_length=100),required=False)
    file_path =CharField(max_length=1000,required=False)
    stream=BooleanField(default=True)
    enable_thinking=<PERSON>oleanField(default=True,help_text='是否启用思考能力')
    course_id = PrimaryKeyRelatedField(
        queryset=Course.objects.all(),
        required=False,
        help_text='课程id',
        error_messages={
            'does_not_exist': '课程ID "{pk_value}" 不存在',
            'incorrect_type': '课程ID格式不正确，应为整数'
        }
    )
    model_name=CharField(max_length=100,required=False,help_text='模型名称',default='Qwen3-32B')


class ChatMessageSerializer(ModelSerializer):
    role = serializers.SerializerMethodField()

    def get_role(self, obj):
        # 根据存储的第二个元素（显示值）获取第一个元素（实际值）
        reverse_role_mapping = {
            "SYSTEM": "system",
            "HUMAN": "user",
            "TOOL": "tool",
            "Assistant": "assistant"
        }
        return reverse_role_mapping.get(obj.role, obj.role)

    class Meta:
        model = ChatMessage
        exclude = ['content','update_at']


class UploadChatFileSerializer(Serializer):
    file = serializers.FileField()
    conversation_id=serializers.PrimaryKeyRelatedField(queryset=Conversation.objects.all())

class ExportChatHistorySerializer(ModelSerializer):
    id = serializers.SerializerMethodField()
    role = serializers.SerializerMethodField()
    class Meta:
        model = ChatMessage
        fields = ['id','content','role','create_at']

    def get_id(self, obj):
        return self.context['id_map'][obj.id]

    def get_role(self, obj):
        # 利用模型的 ROLE_TYPE_CHOICES 进行转换，并提供中文显示
        role_mapping = {
            'system': '系统',
            'user': '用户',
            'human': '用户',
            'tool': '工具',
            'assistant': '助手'
        }
        return role_mapping.get(obj.role, obj.role)


class ConversationSerializer(ModelSerializer):
    class Meta:
        model = Conversation
        fields = '__all__'

class UpdateConversationSerializer(ModelSerializer):

    class Meta:
        model = Conversation
        fields = ['id','title']
        extra_kwargs = {
            'title': {
                'required': True,
                'allow_blank': False,
                'allow_null': False,
            }
        }
    def validate_title(self,value):
        user=self.context['request'].user
        conversation_id=self.initial_data.get('id')
        if Conversation.objects.filter(title=value,user=user).exclude(id=conversation_id).exists():
            raise serializers.ValidationError('标���已存在')
        return value

    def validate(self, attrs):
        attrs['user'] = self.context['request'].user
        return attrs

class KeywordRuleSerializer(ModelSerializer):
    class Meta:
        model = KeywordRule
        fields = ['id', 'keyword','created_at','updated_at']
        extra_kwargs = {
            'keyword': {
                'validators': [UniqueValidator(queryset=KeywordRule.objects.all(), message='该关键词已存在')],
            },
        }
class KeywordRuleDeleteSerializer(ModelSerializer):
    """
    删除关键词序列化器
    """
    class Meta:
        model = KeywordRule
        fields = ['id']
class CommonFileSerializer(ModelSerializer):
    """
    通用文件序列化器
    """

    class Meta:
        model = CommonFile
        fields ='__all__'
