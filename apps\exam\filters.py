from django_filters import rest_framework
from .models import BaseQuestion, Paper, KnowledgeExam, QuestionKnowledgePoint
from django.db.models import Subquery

class BaseQuestionFilter(rest_framework.FilterSet):
    author = rest_framework.NumberFilter(field_name='author_id', lookup_expr='exact')  # 关联到用户表的外键字段名
    stem = rest_framework.CharFilter(field_name='stem', lookup_expr='icontains')
    created_at = rest_framework.DateTimeFilter(field_name='created_at', lookup_expr='date')
    knowledge_point_ids = rest_framework.BaseInFilter(field_name='knowledge_point', lookup_expr='in')
    # course_id = rest_framework.NumberFilter(field_name='knowledge_point__chapter__course_id', lookup_expr='exact')
    course_id = rest_framework.NumberFilter(method='filter_by_course')

    def filter_by_course(self, queryset, name, value):
        # 找到所有属于目标课程的知识点关联的题目ID
        distinct_ids = QuestionKnowledgePoint.objects.filter(
            knowledge_point__chapter__course_id=value
        ).values('question').distinct()  # 重新获取不重复的题目ID
        return queryset.filter(id__in=Subquery(distinct_ids))
    class Meta:
        model = BaseQuestion
        fields = [
            'author', 'stem', 'created_at',
            'knowledge_point_ids', 'course_id'
        ]

class KnowledgeExamFilter(rest_framework.FilterSet):
    name = rest_framework.CharFilter(field_name='name', lookup_expr='icontains')
    status = rest_framework.CharFilter(field_name='status', lookup_expr='exact')
    usage_type = rest_framework.CharFilter(field_name='cs_exam_relations__usage_type', lookup_expr='exact')
    # course_id = rest_framework.NumberFilter(
    #     field_name='cs_exam_relations__course_semester__course_id', lookup_expr='exact'
    # )
    # semester_id = rest_framework.NumberFilter(
    #     field_name='cs_exam_relations__course_semester__semester_id', lookup_expr='exact'
    # )

    class Meta:
        model = KnowledgeExam
        fields = [
            'name', 'status', 'usage_type'
        ]