from django.apps import AppConfig




class ChatConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'chat'
    verbose_name = '聊天模块'

    def ready(self):
        from .safety.utils import keyword_intercepted
        from django.dispatch import receiver

        @receiver(keyword_intercepted)
        def create_keyword_filter_record(sender, **kwargs):
            keywords = kwargs.get('keywords', '')
            question=kwargs.get('question','')
            user_id=kwargs.get('user_id','')
            if not user_id :
                print('[ERROR]用户ID为空')
                return
            from .tasks import create_keyword_filter_record as record_task
            record_task.delay(keywords,question,user_id)
