# 在文件顶部添加导入
from .models import ChunkQA
from django.db.models import Q

from .pgdatabase import (
    get_knowledge_base_by_id
)

# 添加 ChunkQA 相关的 CRUD 函数
def create_chunk_qa(chunk_id, question, content, document_id, knowledge_base_id=None):
    """创建 ChunkQA 记录"""
    try:
        # 获取知识库对象
        knowledge_base = None
        if knowledge_base_id:
            try:
                knowledge_base = get_knowledge_base_by_id(knowledge_base_id)
                if knowledge_base:
                    print(f"[DEBUG] 找到知识库: {knowledge_base.name} (ID: {knowledge_base.id})")
                else:
                    print(f"[WARN] 未找到知识库ID: {knowledge_base_id}")
            except Exception as e:
                print(f"[ERROR] 获取知识库失败: {e}")

        chunk_qa = ChunkQA.objects.create(
            chunk_id=chunk_id,
            question=question,
            content=content,
            document_id=document_id,
            knowledge_base=knowledge_base
        )
        print(f"[INFO] 成功创建ChunkQA记录: {chunk_qa.id}")
        return chunk_qa
    except Exception as e:
        print(f"[ERROR] 创建ChunkQA记录失败: {e}")
        return None


def get_chunk_qa_by_chunk_id(chunk_id):
    """根据chunk_id获取ChunkQA记录"""
    try:
        return ChunkQA.objects.filter(chunk_id=chunk_id, is_active=True).first()
    except Exception as e:
        print(f"[ERROR] 获取ChunkQA记录失败: {e}")
        return None


def get_chunk_qas_by_dataset(dataset_id):
    """获取指定知识库的所有ChunkQA记录"""
    try:
        return ChunkQA.objects.filter(knowledge_base_id=dataset_id, is_active=True).order_by('-created_at')
    except Exception as e:
        print(f"[ERROR] 获取知识库ChunkQA记录失败: {e}")
        return ChunkQA.objects.none()


def get_chunk_qas_by_document(document_id):
    """获取指定文档的所有ChunkQA记录"""
    try:
        return ChunkQA.objects.filter(document_id=document_id, is_active=True).order_by('-created_at')
    except Exception as e:
        print(f"[ERROR] 获取文档ChunkQA记录失败: {e}")
        return ChunkQA.objects.none()


def update_chunk_qa(chunk_id, question=None, content=None):
    """更新ChunkQA记录"""
    try:
        chunk_qa = get_chunk_qa_by_chunk_id(chunk_id)
        if not chunk_qa:
            return False

        if question is not None:
            chunk_qa.question = question
        if content is not None:
            chunk_qa.content = content

        chunk_qa.save()
        print(f"[INFO] 成功更新ChunkQA记录: {chunk_qa.id}")
        return True
    except Exception as e:
        print(f"[ERROR] 更新ChunkQA记录失败: {e}")
        return False


def delete_chunk_qa(chunk_id, soft_delete=True):
    """删除ChunkQA记录"""
    try:
        chunk_qa = get_chunk_qa_by_chunk_id(chunk_id)
        if not chunk_qa:
            return False

        if soft_delete:
            chunk_qa.is_active = False
            chunk_qa.save()
            print(f"[INFO] 成功软删除ChunkQA记录: {chunk_qa.id}")
        else:
            chunk_qa.delete()
            print(f"[INFO] 成功物理删除ChunkQA记录")

        return True
    except Exception as e:
        print(f"[ERROR] 删除ChunkQA记录失败: {e}")
        return False


def delete_chunk_qas_by_dataset(dataset_id, soft_delete=True):
    """删除指定知识库的所有ChunkQA记录"""
    try:
        chunk_qas = ChunkQA.objects.filter(knowledge_base_id=dataset_id, is_active=True)
        count = chunk_qas.count()

        if soft_delete:
            chunk_qas.update(is_active=False)
            print(f"[INFO] 成功软删除知识库{dataset_id}的{count}条ChunkQA记录")
        else:
            chunk_qas.delete()
            print(f"[INFO] 成功物理删除知识库{dataset_id}的{count}条ChunkQA记录")

        return count
    except Exception as e:
        print(f"[ERROR] 删除知识库ChunkQA记录失败: {e}")
        return 0


def delete_chunk_qas_by_document(document_id, soft_delete=True):
    """删除指定文档的所有ChunkQA记录"""
    try:
        chunk_qas = ChunkQA.objects.filter(document_id=document_id, is_active=True)
        count = chunk_qas.count()

        if soft_delete:
            chunk_qas.update(is_active=False)
            print(f"[INFO] 成功软删除文档{document_id}的{count}条ChunkQA记录")
        else:
            chunk_qas.delete()
            print(f"[INFO] 成功物理删除文档{document_id}的{count}条ChunkQA记录")

        return count
    except Exception as e:
        print(f"[ERROR] 删除文档ChunkQA记录失败: {e}")
        return 0
def get_all_active_chunk_qas():
    """获取所有激活的ChunkQA记录"""
    try:
        return ChunkQA.objects.filter(is_active=True).order_by('knowledge_base_id', 'document_id', 'created_at')
    except Exception as e:
        print(f"[ERROR] 获取所有激活ChunkQA记录失败: {e}")
        return ChunkQA.objects.none()