
import os
from concurrent.futures.thread import Thread<PERSON>oolExecutor
from uuid import uuid4

from django.conf import settings
from langchain_community.chat_models import Chat<PERSON>ongyi
from langchain_community.document_loaders import WebB<PERSON><PERSON>oader, TextLoader, UnstructuredWordDocument<PERSON>oader, PDFPlumberLoader
from langchain_community.embeddings import DashScopeEmbeddings
from langchain_core.documents import Document
from langchain_openai import OpenAIEmbeddings

from langchain_text_splitters import RecursiveCharacterTextSplitter
# from sentence_transformers import CrossEncoder

# from chat.agents.langsmith import langsmith_llm
from chat.chat_models import CustomChatQwQ

from chat.models import CommonFile
from chat.prompts import upload_summary_template

from langchain_redis import RedisConfig, RedisVectorStore



"""

RAG_FLOW ------ document_schema:

{ "producer" : "macOS \u7248\u672c15.3.1\uff08\u7248\u53f724D70\uff09 Quartz PDFContext",
"creator" : "WPS \u6587\u5b57",
"creationdate" : "2025-05-21T13:54:46+08:00",
"author" : "storm",
"comments" : "",
"company" : "",
"keywords" : "",
"moddate" : "2025-05-21T13:54:46+08:00",
"sourcemodified" : "D:20250521135446+08'00'",
"subject" : "",
"title" : "",
"trapped" : "/False",
"source" : "/Users/<USER>/Documents/code/tianhe/lykz/wang_ju_develop_test_rag/wang_ju_develop_test_rag_agent_condition/knowledge/media/temp/2/xbzlxmQjsN3GBG0M/2025-06-23/20250623022434_288.pdf",
"total_pages" : 7,
"page" : 3,
"page_label" : "4",
"document_id" : "d26340dd-f1ab-4686-a4df-e4acbd1ce4ec",
"dataset_id" : "xbzlxmQjsN3GBG0M",
"conversation_id" : "xbzlxmQjsN3GBG0M"}


"""
embeddings = OpenAIEmbeddings(
    model="bge-m3",
    base_url=settings.LLM_BASE_URL_LESSON,
    api_key=settings.LLM_API_KEY,
)
# reranker = CrossEncoder(
#     model_name_or_path='bge-reranker-v2-m3',
#     token=settings.LLM_API_KEY,  # 使用你的 API Key
#     api_endpoint=settings.LLM_BASE_URL_LESSON  # 使用你的模型服务地址
# )
reranker = OpenAIEmbeddings(
    model="bge-reranker-v2-m3",
    base_url=settings.LLM_BASE_URL_LESSON,
    api_key=settings.LLM_API_KEY,
)
file_llm= CustomChatQwQ(max_tokens=40000,model=settings.LLM_NAME,api_key=settings.LLM_API_KEY,base_url=settings.LLM_BASE_URL_LESSON)

file_llm_max_tokens=40000
def get_load_docs(file_path):
    file_type = os.path.splitext(file_path)[1].lower().lstrip('.')
    match file_type:
        case 'pdf':
            loader = PDFPlumberLoader(file_path)
            return [Document(page_content='\n'.join([doc.page_content for doc in loader.load()]))]
        case 'txt':
            return TextLoader(file_path).load()
        case 'docx':
            loader = UnstructuredWordDocumentLoader(file_path)
            return loader.load()
        case 'doc':
            loader = UnstructuredWordDocumentLoader(file_path)
            return loader.load()
        case 'web':
            return WebBaseLoader(file_path)
        case 'md':
            return TextLoader(file_path).load()  # UnstructuredMarkdownLoader
    return ''

class ChatDocumentSpliter:
    def __init__(self, cf: CommonFile):
        self.file_path = cf.file_path
        self.cf = cf
        self.conversation_id = cf.conversation_id

    @staticmethod
    def get_split_vector_store(conversation_id: str):
        """
        切面向量库
        """
        config = RedisConfig(
            index_name=f"conversation_file:{conversation_id}:split",
            redis_url=settings.REDIS_BASE_URL,

            metadata_schema=[
                {"name": "conversation_id", "type": "text"},
                {"name": "document_id", "type": "text"},
                {"name": "dataset_id", "type": "text"},
                {"name": "source_name", "type": "text"},
            ],
        )
        return RedisVectorStore(embeddings, config)

    @staticmethod
    def get_split_summary_vector_store(conversation_id: str):
        """
        切片总结向量库
        """
        config = RedisConfig(
            index_name=f"conversation_file:{conversation_id}:split_summary",
            redis_url=settings.REDIS_BASE_URL,

            metadata_schema=[
                {"name": "conversation_id", "type": "text"},
                {"name": "document_id", "type": "text"},
                {"name": "dataset_id", "type": "text"},
                {"name": "source_name", "type": "text"},
            ]
        )
        return RedisVectorStore(embeddings, config)

    @staticmethod
    def get_all_summary_vector_store(conversation_id: str):
        """
        全文总结向量库
        """
        config = RedisConfig(
            index_name=f"conversation_file:{conversation_id}:summary",
            redis_url=settings.REDIS_BASE_URL,

            metadata_schema=[
                {"name": "conversation_id", "type": "text"},
                {"name": "document_id", "type": "text"},
                {"name": "dataset_id", "type": "text"},
                {"name": "source_name", "type": "text"},
            ]
        )
        return RedisVectorStore(embeddings, config)



    def load_with_sammary(self):
        print(f'-------conversation_id:{self.conversation_id}-------')
        docs =  get_load_docs(self.file_path)
        sum_len = sum([len(d.page_content) for d in docs])
        # 保证不会超过提问的最大token数
        deep=-1
        keys = []
        print(f'文档大小:{sum_len}')
        while sum_len > file_llm_max_tokens or deep==-1:
            deep+=1
            text_splitter = RecursiveCharacterTextSplitter(chunk_size=2000, chunk_overlap=100)
            all_splits = text_splitter.split_documents(docs)
            call_results_content = ''
            with ThreadPoolExecutor(max_workers=10) as tpe:
                futures = []
                for i, doc in enumerate(all_splits):
                    if deep ==0:
                        keys.append(self.add_metadata(doc))
                    # 分片生成摘要
                    prompt = upload_summary_template.format(input=doc.page_content, max=200)
                    future = tpe.submit(file_llm.invoke, prompt)
                    futures.append(future)

                for f in futures:
                    try:
                        r=f.result()
                        call_results_content +=r.content
                        print('完成小总结')
                    except Exception as e:
                        print(f'服务器支持不了10个线程，修改代码或者增加线程数{type(e)},{e}')
            #原始切面加入向量库
            if deep ==0:
                _ = self.get_split_vector_store(self.conversation_id).add_documents(documents=all_splits, ids=keys)
            docs = [Document(page_content=call_results_content)]
            sum_len = sum([len(d.page_content) for d in docs])

        summary_content = '\n'.join([x.page_content for x in docs])
        prompt = upload_summary_template.format(input=summary_content, max=2000)
        page_content = file_llm.invoke(prompt).content
        docs=Document(page_content=page_content)
        key = [self.add_metadata(docs)]
        _ = self.get_all_summary_vector_store(self.conversation_id).add_documents(documents=[docs], ids=key)
        # if deep==-1:
        #     _ = self.get_split_vector_store(self.conversation_id).add_documents(documents=all_splits, ids=keys)

    def load(self):
        print(f'-------conversation_id:{self.conversation_id}-------')
        docs =  get_load_docs(self.file_path)
        sum_len = sum([len(d.page_content) for d in docs])
        # 保证不会超过提问的最大token数
        deep=-1
        keys = []
        print(f'文档大小:{sum_len}')
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=2000, chunk_overlap=100)  # todo chunk size 没用上啊
        all_splits = text_splitter.split_documents(docs)
        for doc in all_splits:
            keys.append(self.add_metadata(doc))
        _ = self.get_split_vector_store(self.conversation_id).add_documents(documents=all_splits, ids=keys)


    def add_metadata(self, doc):
        key = str(uuid4())
        doc.metadata["chunk_id"] = str(key)
        doc.metadata["document_id"] =str(self.cf.id)
        doc.metadata["dataset_id"] = self.cf.conversation_id  # 临时文件，没有知识库，暂定使用会话id
        # doc.metadata["id"] = str(uuid.uuid4())
        doc.metadata['conversation_id'] = self.cf.conversation_id
        doc.metadata['source_name'] = self.cf.file_origin_name

        return key


class SummaryDocumentSpliter:
    def __init__(self, file_path: str):
        self.file_path = file_path


    def load(self):
        docs = get_load_docs(self.file_path)
        original=''
        sum_len=0
        for doc in docs:
            sum_len=sum_len+len(doc.page_content)
            original+=doc.page_content

        #保证不会超过提问的最大token数
        while sum_len>file_llm_max_tokens:#todo 这里不够严谨，应该是提问的最大token数，因为现在还无法在聊天页面配置 大模型的选择，所以暂时这么写
            text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=50)  # todo chunk size 没用上啊
            all_splits = text_splitter.split_documents(docs)
            call_results_content=''
            with ThreadPoolExecutor(max_workers=10) as tpe:
                futures = []
                for i, doc in enumerate(all_splits):
                    # 分片生成摘要
                    prompt = upload_summary_template.format(input=doc.page_content, max=200)
                    future = tpe.submit(file_llm.invoke, prompt)
                    futures.append(future)

                for f in futures:
                    try:
                        r=f.result()
                        call_results_content+=r.content
                        print('完成小总结')
                    except KeyError as e:
                        print('服务器支持不了10个线程，修改代码或者增加线程数')
            docs=[Document(page_content=call_results_content)]

        summary_content = '\n'.join([x.page_content for x in docs])
        prompt = upload_summary_template.format(input=summary_content, max=2000)
        page_content = file_llm.invoke(prompt).content
        return original,page_content
