"""
Knowledge Base app的Celery任务
"""
from th_dsep_backend.celery import app
# from .views import (
#     sync_knowledge_bases_intersection,
#     sync_knowledge_bases_rag_to_pg,
#     _fetch_all_rag_datasets,
#     _fetch_all_pg_datasets
# )
from .pgdatabase import *
import traceback
from datetime import datetime
from .sync_data import (sync_knowledge_bases_intersection,
    sync_knowledge_bases_rag_to_pg,_fetch_all_rag_datasets,
     _fetch_all_pg_datasets)
'''---知识库同步检查任务----'''
@app.task(bind=True)
def sync_knowledge_base_task(self):
    """
    知识库同步检查任务
    """
    try:
        print("[INFO] 开始执行知识库同步检查任务...")

        # 执行交集同步检查
        intersection_result = sync_knowledge_bases_intersection()

        # 执行数据更新同步
        update_result = sync_knowledge_bases_rag_to_pg()

        # 构建返回结果
        result = f"知识库同步检查完成:\n"
        result += f"=== 交集同步结果 ===\n"
        result += f"- RAG系统知识库: {intersection_result.get('rag_total', 0)} 个\n"
        result += f"- 本地数据库知识库: {intersection_result.get('pg_total', 0)} 个\n"
        result += f"- 交集知识库: {intersection_result.get('intersection_count', 0)} 个\n"
        result += f"- 从RAG删除: {intersection_result.get('deleted_from_rag', 0)} 个\n"
        result += f"- 从PG删除: {intersection_result.get('deleted_from_pg', 0)} 个\n"
        result += f"=== 数据更新同步结果 ===\n"
        result += f"- 更新的知识库: {update_result.get('updated_datasets', 0)} 个\n"
        result += f"- 创建的知识库: {update_result.get('created_datasets', 0)} 个\n"
        result += f"- 检查时间: {datetime.now().isoformat()}"

        # 检查是否有错误
        total_errors = len(intersection_result.get('errors', [])) + len(update_result.get('errors', []))
        if total_errors > 0:
            result += f"\n⚠️ 发现 {total_errors} 个错误，建议检查详细日志"
            print(f"[WARN] 知识库同步发现 {total_errors} 个错误")

        return result

    except Exception as e:
        error_msg = f"知识库同步检查任务失败: {str(e)}"
        print(f"[ERROR] {error_msg}")
        traceback.print_exc()
        return error_msg

@app.task(bind=True)
def knowledge_base_health_check_task(self):
    """
    知识库健康检查任务
    """
    try:
        print("[INFO] 开始执行知识库健康检查...")

        # 获取RAG和PG的知识库数量
        rag_datasets = _fetch_all_rag_datasets()
        pg_datasets = _fetch_all_pg_datasets()

        rag_count = len(rag_datasets)
        pg_count = len(pg_datasets)

        # 计算一致性
        rag_ids = {dataset["id"] for dataset in rag_datasets}
        pg_ids = {dataset.id for dataset in pg_datasets}

        intersection_count = len(rag_ids & pg_ids)
        consistency_rate = (intersection_count / max(rag_count, pg_count) * 100) if max(rag_count,
                                                                                        pg_count) > 0 else 100

        result = f"知识库健康检查完成:\n"
        result += f"- RAG系统知识库: {rag_count} 个\n"
        result += f"- 本地数据库知识库: {pg_count} 个\n"
        result += f"- 数据一致性: {consistency_rate:.1f}%\n"
        result += f"- 检查时间: {datetime.now().isoformat()}\n"

        if consistency_rate < 95:
            result += "⚠️ 数据一致性较低，建议执行同步任务"
        else:
            result += "✅ 数据一致性良好"

        return result

    except Exception as e:
        error_msg = f"知识库健康检查失败: {str(e)}"
        print(f"[ERROR] {error_msg}")
        traceback.print_exc()
        return error_msg