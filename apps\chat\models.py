from django.contrib.auth.models import AbstractUser
from django.db import models
from rest_framework_mongoengine.validators import UniqueValidator

from user.models import UserInfo


class Conversation(models.Model):
    id=models.CharField(max_length=100,primary_key=True)
    title=models.CharField(max_length=1000)
    user=models.ForeignKey(UserInfo,on_delete=models.CASCADE)
    create_at=models.DateTimeField(auto_now_add=True)
    update_at=models.DateTimeField(auto_now=True)
    objects = models.Manager()

    class Meta:
        db_table = 'conversation'
        verbose_name='会话'
        # ordering=('create_at',)





class ChatMessage(models.Model):
    content=models.TextField()
    parts = models.JSONField(default=list, help_text="消息内容")
    conversation=models.ForeignKey(Conversation,on_delete=models.CASCADE)
    ROLE_TYPE_CHOICES = (
        ("system", "system"),
        ("user", "human"),
        ('tool','tool'),
        ('assistant','assistant')
    )
    role=models.Char<PERSON>ield(max_length=20,choices=ROLE_TYPE_CHOICES,default='user')
    create_at = models.DateTimeField(auto_now_add=True)
    update_at = models.DateTimeField(auto_now=True)
    objects=models.Manager()
    class Meta:
        db_table = 'chat_message'
        verbose_name='聊天内容'
        ordering=('pk',)
        default_manager_name='objects'

class CommonFile(models.Model):
    id=models.CharField(max_length=64,primary_key=True)
    conversation=models.ForeignKey(Conversation,on_delete=models.CASCADE,related_name='files',db_constraint=False,null=True,blank=True)
    source_type_choice=(
    ('conversion','会话文件'),
    ('summary','摘要文件'),
    )
    source_type=models.CharField(max_length=120,choices=source_type_choice,default='conversion')
    file_path=models.CharField(max_length=200)
    file_name=models.CharField(max_length=100)
    file_origin_name=models.CharField(max_length=100)
    file_size=models.IntegerField()
    file_type=models.CharField(max_length=100)
    class Meta:
        db_table = 'conversation_file'
        verbose_name='会话文件'


class KeywordRule(models.Model):
    KEYWORD_TYPES = [
        ('block', '拦截'),
        # ('replace', '替换'),
        # ('warn', '警告'),
    ]

    keyword = models.CharField(max_length=4096, verbose_name="关键词")
    action = models.CharField(max_length=20, choices=KEYWORD_TYPES, default='block', verbose_name="处理方式")
    replacement = models.CharField(max_length=50,null=True, blank=True, verbose_name="替换文本",
                                   help_text="替换动作时使用的文本（默认为*号）")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    objects = models.Manager()
    class Meta:
        verbose_name = "关键词规则"
        verbose_name_plural = "关键词规则"

    def __str__(self):
        return f"{self.keyword} ({self.get_action_display()})"

class KeyworkFilterRecord(models.Model):
    name=models.CharField(max_length=100,verbose_name="名称")
    create_at=models.DateTimeField(auto_now_add=True)
    user=models.ForeignKey(UserInfo,on_delete=models.CASCADE)
    question=models.CharField(max_length=1000,verbose_name="问题")
    class Meta:
        verbose_name = "关键词过滤记录"
        verbose_name_plural = "关键词过滤记录"

# class WhitelistTerm(models.Model):
#     term = models.CharField(max_length=100, unique=True, verbose_name="白名单术语")
#     created_at = models.DateTimeField(auto_now_add=True)
#
#     class Meta:
#         verbose_name = "白名单术语"
#         verbose_name_plural = "白名单术语"
#
#     def __str__(self):
#         return self.term