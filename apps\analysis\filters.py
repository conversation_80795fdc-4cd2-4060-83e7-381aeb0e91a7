from django_filters import rest_framework
from .models import ExamAnalysis, CourseAnalysis

class ExamAnalysisFilter(rest_framework.FilterSet):
    class_id = rest_framework.NumberFilter(field_name='exam_id__class_id', lookup_expr='exact')
    exam_id = rest_framework.NumberFilter(field_name='exam_id', lookup_expr='exact')
    student_name = rest_framework.CharFilter(field_name='user__username', lookup_expr='icontains')

    class Meta:
        model = ExamAnalysis
        fields = [
            'class_id', 'exam_id', 'student_name'
        ]

class ClassKnowledgeExamFilter(rest_framework.FilterSet):
    class_id = rest_framework.NumberFilter(field_name='class_id', lookup_expr='exact')
    course = rest_framework.NumberFilter(field_name='course_semester__course', lookup_expr='exact')
    semester = rest_framework.NumberFilter(field_name='course_semester__semester', lookup_expr='exact')
    exam_id = rest_framework.NumberFilter(field_name='exam_id', lookup_expr='exact')

    class Meta:
        fields = [
            'class_id', 'exam_id', 'course', 'semester'
        ]
    
    def filter_queryset(self, queryset):
        queryset = super().filter_queryset(queryset)
        course_id = self.request.query_params.get('course', None)
        semester = self.request.query_params.get('semester', None)
        if course_id:
            queryset = queryset.filter(course_semester__course=course_id)
        if semester:
            queryset = queryset.filter(course_semester__semester=semester)
        return queryset

class CourseAnalysisFilter(rest_framework.FilterSet):
    class_id = rest_framework.NumberFilter(field_name='exam_id__class_id', lookup_expr='exact')
    course = rest_framework.NumberFilter(field_name='course_semester__course', lookup_expr='exact')
    semester = rest_framework.NumberFilter(field_name='course_semester__semester', lookup_expr='exact')
    student_name = rest_framework.CharFilter(field_name='user__username', lookup_expr='icontains')

    class Meta:
        model = CourseAnalysis
        fields = [
            'class_id', 'student_name', 'course', 'semester'
        ]