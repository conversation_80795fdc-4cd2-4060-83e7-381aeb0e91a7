from rest_framework import serializers
from ppt.models import *
from course.models import *
from user.models import *


class PptTmpModelSerializer(serializers.ModelSerializer):
    """
    ppt模板序列化器
    """
    is_deleted = serializers.BooleanField(default=False)  # 确保默认值为False
    is_public = serializers.BooleanField(default=False)  # 确保默认值为False
    template_type = serializers.ChoiceField(choices=PptTmpModel.TEMPLATE_TYPE_CHOICES, default='lecture')  # 确保默认值为'lecture'
    style = serializers.ChoiceField(choices=PptTmpModel.STYLE_CHOICES, default='modern')  # 确保默认值为'modern'
    # author = serializers.HiddenField(default=serializers.CurrentUserDefault())  # 隐藏字段，默认绑定为当前用户
    author_id = serializers.PrimaryKeyRelatedField(
        queryset=UserInfo.objects.all(),  # 替换为用户模型
        required=False,  # 允许为空或不存在
        allow_null=True,  # 允许为空值
        source='author'  # 关键：让 author_id 实际赋值给 author 外键
    )
    author = serializers.SerializerMethodField()
    json_file_content = serializers.JSONField(
        default = list(),
        required=False,
        allow_null=True
    )  # 允许为空或不存在

    def get_author(self, obj):
        return obj.author.username if obj.author else None
    class Meta:
        model = PptTmpModel
        fields = [
            'id', 'title', 'description', 'json_file_content',
            'tmp_json_file', 'ppt_index_png', 'is_deleted', 'deleted_at',
            'is_public', 'template_type', 'style', 'color_scheme',
            'metadata', 'author', 'created_at', 'updated_at',
            'usage_count', 'is_ai_generated', 'author_id'
        ]
        read_only_fields = ['id', 'uploaded_at', 'updated_at', 'author']

class PptModelSerializer(serializers.ModelSerializer):
    """
    ppt序列化器
    """
    file = serializers.FileField(required=False, allow_null=True)  # 允许文件字段为空或不存在
    is_deleted = serializers.BooleanField(default=False)  # 确保默认值为False
    template = serializers.PrimaryKeyRelatedField(
        queryset=PptTmpModel.objects.all(),
        required=False,  # 允许为空或不存在
        allow_null=True  # 允许为空值 
    )
    chapter = serializers.PrimaryKeyRelatedField(
        queryset=Chapter.objects.all(),
        required=False,  # 允许为空或不存在
        many=True
    )
    # chapter = serializers.ListField(child=StringToIntegerField())
    # author = serializers.HiddenField(default=serializers.CurrentUserDefault())  # 隐藏字段，默认绑定为当前用户
    author_id = serializers.PrimaryKeyRelatedField(
        queryset=UserInfo.objects.all(),  # 替换为用户模型
        required=False,  # 允许为空或不存在
        allow_null=True,  # 允许为空值
        source='author'  # 关键：让 author_id 实际赋值给 author 外键
    )
    author = serializers.SerializerMethodField()
    def get_author(self, obj):
        return obj.author.username if obj.author else None

    class Meta:
        model = PptModel
        fields = [
            'id', 'title', 'description',
            'author', 'file', 'ppt_json',
            'ppt_tmp_json', 'is_ai_generated', 'view_count',
            'uploaded_at', 'updated_at', 'template',
            'chapter', 'is_deleted', 'deleted_at',
            'author_id'
        ]
        read_only_fields = ['id', 'uploaded_at', 'updated_at', 'view_count', 'author']
