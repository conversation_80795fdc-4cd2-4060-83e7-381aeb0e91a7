"""基于信号的试卷评阅引擎"""

import re
import json
from typing import Dict, Any
from django.utils import timezone
from django.db import transaction
from .models import *
from .utils import parse_feedback


class MarkingEngine:
    @classmethod
    def mark_feedback(cls, question):
        """
        根据题目类型选择批阅方式
        """

        print(f"=== 开始批阅题目 {question.id} ===")
        try:
            # 验证题目对象结构
            if not hasattr(question, 'question_version'):
                raise ValueError("题目对象缺少question_version属性")
            
            if not hasattr(question, 'feedback'):
                raise ValueError("题目对象缺少feedback属性")
            question_version = question.question_version
            exam = question.exam_id

            # 从PaperQuestionVersion获取该试题在试卷中的标准分数
            paper_question = PaperQuestionVersion.objects.filter(
                paper=exam.paper,
                question_version=question_version
            ).first()

            if not paper_question:
                raise ValueError("未找到该题目在试卷中的信息")
            
            standard_score = float(paper_question.score) if paper_question.score else 0
            print(f"题目标准分数: {standard_score}")

            question_data = question_version.content
            question_type = question_data.get("type")
            print(f"题目类型: {question_type} | 学生答案: {question.feedback}")

            # 解析用户答案
            user_answer = parse_feedback(question.feedback, question_type)
            if question_type == "单选题":
                result = cls._grade_single_choice(user_answer, question_data, standard_score)
            elif question_type == "多选题":
                result = cls._grade_multi_choice(user_answer, question_data, standard_score)
            elif question_type == "判断题":
                result = cls._grade_true_false(user_answer, question_data, standard_score)
            elif question_type == "填空题":
                result = cls._grade_fill_blank(user_answer, question_data, standard_score)
            elif question_type == "问答题":
                result = cls._grade_essay(
                    stem = question_data.get("question", ""),
                    explanation = question_data.get("explanation", ""),
                    sample_answer = question_data.get("answer", ""),
                    student_answer = question.feedback,
                    max_score=standard_score
                )
            else:
                raise ValueError(f"未知的题目类型: {question_type}")

            # result['get_score'] = result.get('get_score', 0)
            print(f"result: {result}")
            return result

        except Exception as e:
            return {
                "status": "failed",
                "error": str(e),
                "get_score": 0,
                "comments": f"批阅失败: {str(e)}",
            }

    @classmethod
    def _grade_single_choice(cls, user_answer, question_data, standard_score):
        """
        批阅单选题
        :param question: 题目对象
        :param question_data: 题目数据
        :param standard_score: 标准分数
        """
        correct_answer = str(question_data.get("answer", "").strip().upper())
        if not correct_answer:
            raise ValueError("单选题标准答案不能为空")
        user_answer_processed = str(user_answer.strip().upper() if user_answer else "")
        print(f"\n[Debug] 单选题批阅：\n"
              f"标准答案: '{correct_answer}'\n"
              f"学生答案: '{user_answer_processed}'\n"
              f"题目标准分数: {standard_score}")

        is_correct = correct_answer == user_answer_processed
        get_score = standard_score if is_correct else 0
        return {
            "get_score": get_score,
            "is_correct": is_correct,
            "justification": (
                "回答正确" if is_correct else f"正确答案：{correct_answer}"
            ),
            "is_judged": True,
            "error_types": None if is_correct else "wrong_choice",
        }

    @classmethod
    def _grade_multi_choice(cls, user_answer, question_data, standard_score):
        """
        批阅多选题
        """
        correct_answer = question_data.get("answer", [])

        # 转换为集合比较并忽略顺序
        correct_set = set(correct_answer)
        user_set = set(user_answer)

        is_correct = correct_set == user_set
        get_score = standard_score if is_correct else 0
        return {
            "get_score": get_score,
            "is_correct": is_correct,
            "justification": (
                "回答正确" if is_correct else f"正确答案：{correct_answer}"
            ),
            "is_judged": True,
            "error_types": None if is_correct else "wrong_choices",
        }

    @classmethod
    def _grade_true_false(cls, user_answer, question_data, standard_score):

        """
        批阅判断题
        """
        correct_answer = str(question_data.get("answer", "").strip().upper())
        if not correct_answer:
            raise ValueError("判断题标准答案不能为空")
        
        user_answer_processed = str((user_answer).strip().upper() if user_answer else "")
        is_correct = correct_answer == user_answer_processed
        get_score = standard_score if is_correct else 0
        return {
            "get_score": get_score,
            "is_correct": is_correct,
            "justification": (
                "回答正确" if is_correct else f"正确答案：{correct_answer}"
            ),
            "is_judged": True,
            "error_types": None if is_correct else "wrong_choices",
        }

    @classmethod
    def _grade_fill_blank(cls, user_answer, question_data, standard_score):

        """
        批阅填空题
        标准答案形式：
            [{"blank": "答案1"}, {"blank": "答案2"}
        转换完的学生答案：
            ["答案1", "答案2"]
        """
        correct_answers = question_data.get("answer", [])

        correct_count = 0
        details = []
        max_score = standard_score
        per_blank_score = (
            round(max_score / len(correct_answers), 1) if correct_answers else 0
        )  # 计算每空平均分并保留一位小数

        # 处理每个空的评分
        for i, correct_item in enumerate(correct_answers):
            correct_answer = str(correct_item["blank"]).strip()

            # 获取学生答案
            student_answer = user_answer[i].strip() if i < len(user_answer) else ""

            # 使用正则表达式进行不区分大小写的匹配
            pattern = re.compile(re.escape(correct_answer), re.IGNORECASE)
            if re.search(pattern, student_answer):
                correct_count += 1
                details.append(f"空{i+1}：正确")
            else:
                details.append(f"空{i+1}：错误")
        
        get_score = round(correct_count * per_blank_score)
        is_fully_correct = correct_count == len(correct_answers)

        # 正确答案提示
        correct_answer_text = ", ".join(item["blank"] for item in correct_answers)

        return {
            "get_score": get_score,
            "is_correct": is_fully_correct,
            "justification": (
                "回答正确" if is_fully_correct else f"正确答案：{correct_answer_text}"
            ),
            "is_judged": True,
            "error_types": None if is_fully_correct else "wrong_blank",
        }

    @classmethod
    def _grade_essay(cls, stem, explanation, sample_answer, student_answer, max_score):
        """
        批阅问答题-接收原始字符串参数
        :param stem: 题干
        :param explanation: 题目解析
        :param sample_answer: 标准答案
        :param student_answer: 学生答案
        :param max_score: 最大分数
        """
        from .ai_marking import AIMarkingService  # 避免循环导入
        marking_service = AIMarkingService()  # 实例化

        try:
            ai_result = marking_service.grade_essay(
                stem = stem,
                explanation=explanation,
                sample_answer = sample_answer,
                student_answer = student_answer,
                max_score = max_score
            )
            # 确保返回结果包含所有必要字段
            required_fields = ["get_score", "justification", "initial_score", "calibrated_score"]
            for field in required_fields:
                if field not in ai_result:
                    raise ValueError(f"AI评分结果缺少{field}字段")

            return {
                "get_score": ai_result.get("get_score", 0),
                "initial_score": ai_result.get("initial_score", 0),
                "calibrated_score": ai_result.get("calibrated_score", 0),
                "is_correct": None,
                "justification": ai_result.get("justification", ""),
                "is_judged": True,
                "error_types": ai_result.get("error_types", ""),
            }

        except Exception as e:
            return {
                "get_score": 0,
                "comments": f"批阅失败: {str(e)}",
            }
