from django.http import JsonResponse
from docx import Document
from pypdf import PdfReader
import pdfplumber
import warnings
from typing import Optional

import fitz  # PyMuPDF
import io
from pathlib import Path
from typing import Optional, Union


def api_response(code, message, data=None, status=200):
    """
    封装 API 返回格式
    :param code: 状态码（自定义业务状态码）
    :param message: 返回信息
    :param data: 返回数据
    :param status: HTTP 状态码
    :return: JsonResponse
    """
    response_data = {
        'code': code,
        'message': message,
        'data': data if data is not None else {}
    }
    return JsonResponse(response_data, status=status, json_dumps_params={'ensure_ascii': False})


def docx_analysis(file):
    """
    docx解析工具, 用于解析文档中的信息
    :param file: api接收到的文件对象
    :return: 解析后的信息
    """

    doc = Document(file)
    # full_text = '\n'.join([para.text for para in doc.paragraphs])
    structured_content = []
    # 标题级别和对应的文本内容
    heading_levels = {
        'Heading 1': 1, '标题 1': 1,
        'Heading 2': 2, '标题 2': 2,
        'Heading 3': 3, '标题 3': 3,
        'Heading 4': 4, '标题 4': 4,
    }
    for para in doc.paragraphs:
        text = para.text.strip()
        if not text:  # 跳过空行
            continue
        # 获取段落样式名称
        style_name = para.style.name if para.style else None
        # 检查是否为标题
        if style_name and style_name in heading_levels:
            level = heading_levels[style_name]
            structured_content.append({
                'type': str(level) + '级标题',
                'text': text
            })
        else:  # 正文
            structured_content.append({
                'type': '正文',
                'text': text
            })
    # 将结构化内容转换为Markdown格式的字符串
    if structured_content:
        document_content = "\n\n".join([
            f"# {item['text']}" if item['type'] == '1级标题' else
            f"## {item['text']}" if item['type'] == '2级标题' else
            f"### {item['text']}" if item['type'] == '3级标题' else
            f"#### {item['text']}" if item['type'] == '4级标题' else
            f"{item['text']}"
            for item in structured_content
        ])
        # print(f'docx解析成功:{document_content}')
        return document_content, 1
    else:
        # print('docx解析失败')
        return '', 0


def txt_analysis(file):
    """
    txt解析工具, 用于解析文档中的信息
    :param file: api接收到的文件对象
    :return: 解析后的信息
    """
    try:
        # 读取文件内容
        content = file.read().decode('utf-8')
        return content, 1
    except Exception as e:
        return str(e), 0

    # try:
    #     with open(file_path, 'r', encoding='utf-8') as f:
    #         return ' '.join(line.strip() for line in f if line.strip())
    # except FileNotFoundError:
    #     print(f"错误：文件 {file_path} 不存在")
    #     return ""
    # except UnicodeDecodeError:
    #     print("错误：文件编码不支持，请尝试其他编码")
    #     return ""


#
# def pdf_analysis(
#         pdf_path: Union[str, Path, io.BytesIO],
#         page_numbers: Optional[list[int]] = None,
#         repair_cropbox: bool = True,
#         remove_duplicates: bool = True,
#         clean_text: bool = True
# ) -> str:
#     """
#     参数:
#         pdf_path: PDF文件路径或BytesIO对象
#         page_numbers: 要提取的页码列表（从0开始），如果为None则提取所有页面
#         repair_cropbox: 是否尝试修复CropBox问题
#         remove_duplicates: 是否移除重复文本
#         clean_text: 是否清理文本（移除多余空白字符）
#
#     返回:
#         提取的文本内容
#     """
#     # 初始化文档对象
#     if isinstance(pdf_path, (str, Path)):
#         doc = fitz.open(pdf_path)
#     else:  # BytesIO对象
#         doc = fitz.open("pdf", pdf_path)
#
#     all_text = []
#     seen_texts = set()  # 用于去重
#
#     # 确定要处理的页面
#     if page_numbers is None:
#         page_numbers = list(range(len(doc)))
#
#     for page_idx in page_numbers:
#         if 0 <= page_idx < len(doc):
#             page = doc[page_idx]
#
#             # 尝试修复CropBox问题
#             if repair_cropbox:
#                 # 检查是否有CropBox，如果没有则使用MediaBox
#                 if page.rect.width == 0 or page.rect.height == 0:
#                     page.set_cropbox(page.mediabox)
#                 else:
#                     # 尝试修复异常的CropBox
#                     if page.rect.width < 10 or page.rect.height < 10:
#                         page.set_cropbox(page.mediabox)
#
#             # 提取文本
#             text = page.get_text()
#
#             # 清理文本
#             if clean_text:
#                 text = " ".join(text.split())
#
#             # 去重处理
#             if remove_duplicates:
#                 if text not in seen_texts:
#                     seen_texts.add(text)
#                     all_text.append(text)
#             else:
#                 all_text.append(text)
#
#     doc.close()
#     return "\n\n".join(all_text)


def pdf_analysis(file):
    """
    pdf解析工具, 用于解析文档中的信息
    :param file: api接收到的文件对象
    :return: 解析后的信息
    """
    try:
        reader = PdfReader(file)
        text = ""

        # 逐页提取文本
        for page in reader.pages:
            text += page.extract_text() or ""
        return text, 1
    except Exception as e:
        # return JsonResponse({'error': f'Failed to parse PDF: {str(e)}'}, status=400)
        return str(e), 0


def process_text_file(uploaded_file):
    """处理上传的TXT文件并返回内容"""
    try:
        # 确保文件是文本类型
        if not uploaded_file.name.endswith('.txt'):
            raise ValueError("文件类型不是TXT")

        # 获取文件编码（默认使用UTF-8）
        encoding = getattr(uploaded_file, 'charset', 'utf-8')

        # 读取文件内容
        content = uploaded_file.read().decode(encoding)

        # 按行分割内容（可选）
        lines = content.splitlines()

        return {
            'success': True,
            'content': content,
            'lines': lines,
            'line_count': len(lines),
            'word_count': len(content.split())
        }

    except UnicodeDecodeError:
        return {
            'success': False,
            'error': '文件解码失败，请检查文件编码'
        }
    except Exception as e:
        return {
            'success': False,
            'error': f'处理文件时出错: {str(e)}'
        }


def filter_think_tags(stream):
    """
    从 LangChain 的流响应中移除 <think>...</think> 标签及其内容
    """
    think_tag_start = "<think>"
    think_tag_end = "</think>"

    current_content = ""
    in_think_block = False

    for chunk in stream:
        # 从AIMessageChunk对象中提取文本内容
        if hasattr(chunk, 'content'):
            content = chunk.content
        else:
            content = str(chunk)  # 备用转为字符串

        if not content:
            continue

        # 简单的标签状态机处理
        while content:
            if in_think_block:
                # 在<think>标签内，查找</think>结束标签
                end_idx = content.find(think_tag_end)
                if end_idx != -1:
                    # 找到结束标签，跳过标签内容
                    content = content[end_idx + len(think_tag_end):]
                    in_think_block = False
                else:
                    # 未找到结束标签，跳过整个内容
                    content = ""
            else:
                # 在标签外，查找<think>开始标签
                start_idx = content.find(think_tag_start)
                if start_idx != -1:
                    # 找到开始标签，先发送标签前的内容
                    if start_idx > 0:
                        current_content += content[:start_idx]
                        yield current_content
                        current_content = ""
                    # 处理标签内内容
                    content = content[start_idx + len(think_tag_start):]
                    in_think_block = True
                else:
                    # 没有标签，直接发送内容
                    current_content += content
                    yield current_content
                    current_content = ""
                    content = ""
