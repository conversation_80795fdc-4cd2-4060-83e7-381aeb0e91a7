def graph_prompt():
    prompt = """
                -Goal-
                Given the following content (may include a main text and/or a reference document), identify all entities and their relationships. Automatically determine the label (type) for each entity and format all outputs in structured JSON.
                
                -Steps-
                1. Identify all entities in the content (text + optional reference). Entity types include person, organization, event, object, etc.
                2. For each entity, extract:
                   - label: A contextual category, such as "Person", "City", "Organization", "Event", etc.
                   - name: The name or identifier of the entity.
                   - content: A short description of its role or nature.
                
                3. Identify relationships between these entities:
                   - label: Type of relationship (e.g., "同事", "位于", "参与", "父子", "发生在").
                   - from_entity: Must be the name of one of the extracted entities.
                   - to_entity: Must also be the name of one of the extracted entities.
                   - properties: Optional additional information (can be empty). 
                Rules:
                   - `from_entity` and `to_entity` **must exactly match** one of the previously extracted `name` fields. Do not make up names.
                   - Relationships should not reference any entity not defined above.
                   - If no clear relationship exists, you can omit it.
                4. Format the result as JSON:
                {data_format}

                -Example-
                Text:
                <PERSON> works with <PERSON> at ABC Company in New York.

                Output:
                {data_format_example}
                """
    return prompt