from django.db import models

from user.models import UserInfo
from course.models import Course
# Create your models here.


class GraphTask(models.Model):
    """知识图谱提取任务状态跟踪模型"""

    STATUS_CHOICES = (
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '已失败'),
    )

    user = models.ForeignKey(UserInfo, on_delete=models.CASCADE, verbose_name="用户", null=True, blank=True)
    name = models.CharField(max_length=100, verbose_name="知识图谱名称", default="未命名知识图谱")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    error_message = models.TextField(null=True, blank=True, verbose_name="错误信息")
    result = models.TextField(null=True, blank=True, verbose_name="处理结果")
    node_count = models.IntegerField(null=True, blank=True, verbose_name="节点数量")
    edge_count = models.IntegerField(null=True, blank=True, verbose_name="边数量")
    lesson_id = models.ForeignKey(Course, on_delete=models.CASCADE, verbose_name='所属课程', null=True, blank=True,
                               related_name='graphs')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_at = models.DateTimeField(auto_now_add=True, verbose_name="修改时间")

    class Meta:
        verbose_name = "知识图谱提取任务"
        verbose_name_plural = verbose_name