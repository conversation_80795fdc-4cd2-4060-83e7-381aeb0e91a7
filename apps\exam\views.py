import json
import random
import pandas as pd

from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction
from django.db.models import Q, Prefetch, Count
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import status, viewsets, mixins, permissions
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination

from ppt.utils import upload_file_docx

from .filters import BaseQuestionFilter, KnowledgeExamFilter
from .models import *
from .serializers import *
from .utils import *


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 10  # 每页的数据量
    page_size_query_param = 'page_size'  # 允许客户端通过 `page_size` 参数自定义每页数据量
    max_page_size = 100  # 每页最大数据量

    def get_paginated_response(self, data):
        """
        重写分页返回数据格式
        :param data:
        :return:
        """
        data = {
            'count': self.page.paginator.count,
            'next': self.get_next_link(),
            'previous': self.get_previous_link(),
            'results': data,
        }
        return data

class SingleChoiceQuestionViewSet(viewsets.ModelViewSet):
    """
    单选题视图集
    """
    queryset = SingleChoiceQuestion.objects.all()
    pagination_class = StandardResultsSetPagination
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        # 动态选择序列化器
        if self.action in ['create', 'update', 'partial_update']:
            return QuestionCreateUpdateSerializer
        return SingleChoiceQuestionSerializer

    def create(self, request, *args, **kwargs):
        """
        添加单选题
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        user_id = request.user.id
        if not user_id:
            return api_response(400, '用户Session未正常传输')
        data = request.data.copy()
        data['author'] = user_id
        
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        # print(f"serializer_data_f:{serializer.validated_data}")
        self.perform_create(serializer)
        # print(f"serializer_data_e:{serializer.data}")
        return api_response(200, '添加成功', serializer.data)

    def update(self, request, *args, **kwargs):
        """
        更新单选题
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        partial = kwargs.pop('partial', False)
        try:
            instance = self.get_object()
        except Exception as e:
            return api_response(400, '未找到该单选题')
        
        if instance.author.id != request.user.id:
            return api_response(400, '无权限修改该单选题')
        
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)  # 调用Mixin的方法

        if getattr(instance, '_prefetched_objects_cache', None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        return api_response(200, '修改成功', serializer.data)

    def destroy(self, request, *args, **kwargs):
        """
        删除单选题
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        try:
            instance = self.get_object()
            if instance.author.id != request.user.id:
                return api_response(400, '无权限删除该单选题')
            self.perform_destroy(instance)
            return api_response(200, '删除成功')
        except Exception as e:
            return api_response(400, '该单选题不存在')

    def retrieve(self, request, *args, **kwargs):
        """
        获取单选题详情
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        try:
            instance = self.get_object()
        except Exception as e:
            return api_response(400, '该单选题不存在')
        serializer = self.get_serializer(instance)
        return api_response(200, '查询成功', serializer.data)

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset()).order_by('-id')
        # print(f"queryset: {queryset.count()}")

        page = self.paginate_queryset(queryset)
        # print(f"page: {page}")
        # print(f"page的长度:{len(page)}")
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            data = self.get_paginated_response(serializer.data)
            return api_response(200, '查询成功', data)

        serializer = self.get_serializer(queryset, many=True)
        # print(serializer.data)
        return api_response(200, '查询成功', serializer.data)


class MultipleChoiceQuestionViewSet(viewsets.ModelViewSet):
    """
    多选题视图集
    """
    queryset = MultipleChoiceQuestion.objects.all()
    pagination_class = StandardResultsSetPagination
    permission_classes = [permissions.IsAuthenticated]
    def get_serializer_class(self):
        # 动态选择序列化器
        if self.action in ['create', 'update', 'partial_update']:
            return QuestionCreateUpdateSerializer
        return MultipleChoiceQuestionSerializer

    def create(self, request, *args, **kwargs):
        """
        多选题添加
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        user_id = request.user.id
        if not user_id:
            return api_response(400, '用户Session未正常传输')
        print(f"user_id:{user_id}")
        data = request.data.copy()
        data['author'] = user_id
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return api_response(200, '添加成功', serializer.data)

    def update(self, request, *args, **kwargs):
        """
        多选题修改
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        partial = kwargs.pop('partial', False)
        try:
            instance = self.get_object()
        except Exception as e:
            return api_response(400, '未找到该多选题')
        
        if instance.author.id != request.user.id:
            return api_response(400, '无权限修改该多选题')
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            instance._prefetched_objects_cache = {}

        return api_response(200, '修改成功', serializer.data)

    def retrieve(self, request, *args, **kwargs):
        """
        多选题查单一
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        try:
            instance = self.get_object()
        except Exception as e:
            return api_response(400, '该多选题不存在')
        serializer = self.get_serializer(instance)
        return api_response(200, '查询成功', serializer.data)

    def destroy(self, request, *args, **kwargs):
        """
        多选题单一删除
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        try:
            instance = self.get_object()
            if instance.author.id != request.user.id:
                return api_response(400, '无权限删除该多选题')
            self.perform_destroy(instance)
            return api_response(200, '删除成功')
        except Exception as e:
            return api_response(400, '该多选题不存在')

    def list(self, request, *args, **kwargs):
        """
        多选题查全部
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        queryset = self.filter_queryset(self.get_queryset()).order_by('-id')

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            data = self.get_paginated_response(serializer.data)
            return api_response(200, '查询成功', data)

        serializer = self.get_serializer(queryset, many=True)
        return api_response(200, '查询成功', serializer.data)


class TrueOrFalseQuestionViewSet(viewsets.ModelViewSet):
    """
    判断题视图集
    """
    queryset = TrueOrFalseQuestion.objects.all()
    pagination_class = StandardResultsSetPagination
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        # 动态选择序列化器
        if self.action in ['create', 'update', 'partial_update']:
            return QuestionCreateUpdateSerializer
        return TrueOrFalseQuestionSerializer

    def create(self, request, *args, **kwargs):
        """
        判断题添加
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        user_id = request.user.id
        if not user_id:
            return api_response(400, '用户Session未正常传输')
        data = request.data.copy()
        data['author'] = user_id
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return api_response(200, '添加成功', serializer.data)

    def update(self, request, *args, **kwargs):
        """
        判断题修改
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        partial = kwargs.pop('partial', False)
        try:
            instance = self.get_object()
        except Exception as e:
            return api_response(400, '未找到该判断题')
        if instance.author.id != request.user.id:
            return api_response(400, '无权限修改该判断题')
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            instance._prefetched_objects_cache = {}

        return api_response(200, '修改成功', serializer.data)


    def retrieve(self, request, *args, **kwargs):
        """
        判断题查单一
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        try:
            instance = self.get_object()
        except Exception as e:
            return api_response(400, '该判断题不存在')
        serializer = self.get_serializer(instance)
        return api_response(200, '查询成功', serializer.data)

    def list(self, request, *args, **kwargs):
        """
        判断题查全部
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        queryset = self.filter_queryset(self.get_queryset()).order_by('-id')
        # print(f"queryset: {queryset.count()}")
        page = self.paginate_queryset(queryset)
        # print(f"page: {page}")
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            page_data = self.get_paginated_response(serializer.data)
            return api_response(200, '查询成功', page_data)
        serializer = self.get_serializer(queryset, many=True)
        return api_response(200, '查询成功', serializer.data)

    def destroy(self, request, *args, **kwargs):
        """
        判断题单一删除
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        try:
            instance = self.get_object()
            if instance.author.id != request.user.id:
                return api_response(400, '无权限删除该判断题')
            self.perform_destroy(instance)
            return api_response(200, '删除成功')
        except Exception as e:
            return api_response(400, '该判断题不存在')


class FillInBlankQuestionViewSet(viewsets.ModelViewSet):
    """
    填空题视图集
    """
    queryset = FillInBlankQuestion.objects.all()
    pagination_class = StandardResultsSetPagination
    permission_classes = [permissions.IsAuthenticated]
    def get_serializer_class(self):
        # 动态选择序列化器
        if self.action in ['create', 'update', 'partial_update']:
            return QuestionCreateUpdateSerializer
        return FillInBlankQuestionSerializer

    def create(self, request, *args, **kwargs):
        """
        填空题添加
        :param request:
        :param args:
        :param kwargs:
        :return:
        """

        user_id = request.user.id
        if not user_id:
            return api_response(400, '用户Session未正常传输')
        data = request.data.copy()
        data['author'] = user_id
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return api_response(200, '添加成功', serializer.data)
    
    def update(self, request, *args, **kwargs):
        """
        填空题修改
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        partial = kwargs.pop('partial', False)
        try:
            instance = self.get_object()
        except Exception as e:
            return api_response(400, '未找到该填空题')
        if instance.author.id != request.user.id:
            return api_response(400, '无权限修改该填空题')
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        if getattr(instance, '_prefetched_objects_cache', None):
            instance._prefetched_objects_cache = {}
        
        return api_response(200, '修改成功', serializer.data)
    
    def retrieve(self, request, *args, **kwargs):
        """
        填空题查单一
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        try:
            instance = self.get_object()
        except Exception as e:
            return api_response(400, '该填空题不存在')
        serializer = self.get_serializer(instance)
        return api_response(200, '查询成功', serializer.data)
    
    def list(self, request, *args, **kwargs):
        """
        填空题查全部
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        queryset = self.filter_queryset(self.get_queryset()).order_by('-id')
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            page_data = self.get_paginated_response(serializer.data)
            return api_response(200, '查询成功', page_data)
        serializer = self.get_serializer(queryset, many=True)
        return api_response(200, '查询成功', serializer.data)
    
    def destroy(self, request, *args, **kwargs):
        """
        填空题单一删除
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        try:
            instance = self.get_object()
            if instance.author.id != request.user.id:
                return api_response(400, '无权限删除该填空题')
            self.perform_destroy(instance)
            return api_response(400, '删除成功')
        except Exception as e:
            return api_response(200, '该填空题不存在')


class QuestionAndAnswerQuestionViewSet(viewsets.ModelViewSet):
    """
    问答题视图集
    """
    queryset = QuestionAndAnswerQuestion.objects.all()
    pagination_class = StandardResultsSetPagination
    permission_classes = [permissions.IsAuthenticated]
    def get_serializer_class(self):
        # 动态选择序列化器
        if self.action in ['create', 'update', 'partial_update']:
            return QuestionCreateUpdateSerializer
        return QuestionAndAnswerQuestionSerializer

    def create(self, request, *args, **kwargs):
        """
        问答题添加
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        user_id = request.user.id
        if not user_id:
            return api_response(400, '用户Session未正常传输')
        data = request.data.copy()
        data['author'] = user_id
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return api_response(200, '添加成功', serializer.data)

    def update(self, request, *args, **kwargs):
        """
        问答题修改
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        partial = kwargs.pop('partial', False)
        try:
            instance = self.get_object()
        except Exception as e:
            return api_response(400, '未找到该问答题')
        if instance.author.id != request.user.id:
            return api_response(400, '无权限修改该问答题')
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            instance._prefetched_objects_cache = {}

        return api_response(200, '修改成功', serializer.data)

    def retrieve(self, request, *args, **kwargs):
        """
        问答题查单一
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        try:
            instance = self.get_object()
        except Exception as e:
            return api_response(400, '该问答题不存在')
        serializer = self.get_serializer(instance)
        return api_response(200, '查询成功', serializer.data)

    def list(self, request, *args, **kwargs):
        """
        问答题查全部
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        queryset = self.filter_queryset(self.get_queryset()).order_by('-id')
        # print(f"queryset: {queryset.count()}")
        page = self.paginate_queryset(queryset)
        # print(f"page: {page}")
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            page_data = self.get_paginated_response(serializer.data)
            return api_response(200, '查询成功', page_data)
        serializer = self.get_serializer(queryset, many=True)
        return api_response(200, '查询成功', serializer.data)

    def destroy(self, request, *args, **kwargs):
        """
        问答题单一删除
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        try:
            instance = self.get_object()
            self.perform_destroy(instance)
            return api_response(200, '删除成功')
        except Exception as e:
            return api_response(400, '该问答题不存在')


class QuestionListViewSet(viewsets.ModelViewSet):
    """
    全部题目视图集
    list: 获取全部题目列表
    retrieve: 获取单个题目详情
    update: 修改单个题目
    destroy: 删除单个题目
    delete_batch: 批量删除题目
    create_batch: 批量添加题目
    """
    queryset = BaseQuestion.objects.filter(is_deleted=False)  # 使用django-polymorphic
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend]  # 启用过滤后端
    filterset_class = BaseQuestionFilter  # 指定过滤器类
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        # 动态选择序列化器
        if self.action in ['create', 'update', 'partial_update']:
            return QuestionCreateUpdateSerializer
        return QuestionPolymorphicSerializer

    def list(self, request, *args, **kwargs):
        # 用户只能查询到自己创建的题目和visible='公开'的题目
        user_id = request.user.id
        # print(f"user_id: {user_id}")
        if not user_id:
            return api_response(400, '用户Session未正常传输')
        base_queryset = self.get_queryset().filter(
            Q(author_id=user_id) | Q(visible='公开')
        )
        queryset = self.filter_queryset(base_queryset).order_by('-id')

        # 获取题目类型查询参数
        question_type = request.query_params.get('type', '')

        if question_type:
            # 定义子类与type的映射关系
            type_map = {
                '单选题': SingleChoiceQuestion,
                '多选题': MultipleChoiceQuestion,
                '判断题': TrueOrFalseQuestion,
                '填空题': FillInBlankQuestion,
                '问答题': QuestionAndAnswerQuestion,
            }
            # 根据type获取对应的子类
            model_class = type_map.get(question_type)
            if model_class:
                # 使用instance_of方法过滤子类
                queryset = queryset.instance_of(model_class)
            else:
                return api_response(400, '无效的题型')
        else:
            # 如果没有指定type，强制转换为列表，手动分页解决PolymorphicQueryset不兼容的问题
            queryset = list(queryset)
            page_number = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 10))
            total_count = len(queryset)
            total_pages = (total_count + page_size - 1) // page_size  # 计算总页数

            # 页码超出范围时直接报错
            if page_number < 1 or (total_pages > 0 and page_number > total_pages):
                return api_response(400, 'Invalid page.')

            start = (page_number - 1) * page_size
            end = start + +page_size
            page = queryset[start:end]
            serializer = self.get_serializer(page, many=True)

            # 构造 next/previous url
            params = request.GET.copy()
            params['page_size'] = page_size

            # next url
            next_url = None
            if end < len(queryset) and len(page) > 0:
                params['page'] = page_number + 1
                next_url = request.build_absolute_uri(f"{request.path}?{params.urlencode()}")

            # previous url
            previous_url = None
            if page_number > 1 and len(page) > 0:
                params['page'] = page_number - 1
                previous_url = request.build_absolute_uri(f"{request.path}?{params.urlencode()}")
            
            # 只有一个或无数据时都为null
            if len(queryset) <= page_size or len(page) == 0:
                next_url = None
                previous_url = None

            # 手动构造分页响应
            data = {
                'count': len(queryset),
                'next': next_url,
                'previous': previous_url,
                'results': serializer.data
            }
            return api_response(200, '查询成功', data)
        
        page = self.paginate_queryset(queryset)
        # print(f"page: {page} length: {len(page) if page else 0}")
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            data = self.get_paginated_response(serializer.data)
            return api_response(200, '查询成功', data)

        serializer = self.get_serializer(queryset, many=True)
        return api_response(200, '查询成功', serializer.data)
    
    def retrieve(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
        except Exception as e:
            return api_response(400, '该题目不存在')
        serializer = self.get_serializer(instance)
        return api_response(200, '查询成功', serializer.data)
    
    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        try:
            instance = self.get_object()
        except Exception as e:
            return api_response(400, '该题目不存在')
        if instance.author.id != request.user.id:
            # print(f"instance.author.id: {instance.author.id}, request.user.id: {request.user.id}")
            return api_response(400, '用户无权限修改该题目')
        
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            instance._prefetched_objects_cache = {}

        return api_response(200, '修改成功', serializer.data)
        
    
    
    def destroy(self, request, *args, **kwargs):
        """
        单一删除题库
        """
        try:
            instance = self.get_object()
            if instance.author.id != request.user.id:
                return api_response(400, '无权限删除该题目')
            self.perform_destroy(instance)
            return api_response(200, '删除成功')
        except Exception as e:
            return api_response(400, '该题目不存在')
    
    @action(methods=['DELETE'], detail=False, url_path='batch_delete')
    def delete_batch(self, request, *args, **kwargs):
        """
        批量删除题目
        """
        ids = request.data.get('ids', [])

        # 参数校验
        if not isinstance(ids, list):
            return api_response(400, '参数ids必须是列表', data={})
        
        ids = [int(i) for i in ids if str(i).isdigit()]  # 过滤掉非数字的id

        if not ids:
            return api_response(400, '缺少参数ids', data={})

        try:
            with transaction.atomic():  # 事务保护
                # 手动删除子表记录
                total_delete_count = 0
                sub_models = [
                    SingleChoiceQuestion,
                    MultipleChoiceQuestion,
                    TrueOrFalseQuestion,
                    FillInBlankQuestion,
                    QuestionAndAnswerQuestion
                ]
                for sub_model in sub_models:
                    # 判断该题用户是否有权限删除
                    if sub_model.objects.filter(id__in=ids, author=request.user).count() == 0:
                        return api_response(400, f'无权限删除{sub_model._meta.verbose_name}题目', data={})
                    # 过滤出指定的id对应的记录并删除
                    _, delete_info = sub_model.objects.filter(id__in=ids, author=request.user).delete()
                    # 累加删除的记录数
                    total_delete_count += delete_info.get(sub_model._meta.label, 0)

                if total_delete_count == 0:
                    return api_response(400, '未找到指定题目', data={})
                
                return api_response(200, f'成功删除 {total_delete_count} 条题目', data={})
        except Exception as e:
            return api_response(400, f'删除失败{str(e)}', data={})
        
    @action(methods=['POST'], detail=False, url_path='soft_delete')
    def delete_batch(self, request, *args, **kwargs):
        """
        批量软删除题目（标记is_deleted=True, 保留版本记录）
        只有题目创建者可删除自己的题目
        """
        ids = request.data.get('question_ids', [])

        # 参数校验
        if not isinstance(ids, list):
            return api_response(400, '参数ids必须是列表', data={})
        
        ids = [int(i) for i in ids if str(i).isdigit()]  # 过滤掉非数字的id

        if not ids:
            return api_response(400, '缺少参数ids', data={})

        try:
            with transaction.atomic():  # 事务保护
                # 获取所有需要删除的题目
                questions = BaseQuestion.objects.filter(id__in=ids)
                existing_ids = questions.values_list('id', flat=True)
                existing_ids_set = set(existing_ids)

                # 未找到的题目
                no_existent_ids = set(ids) - existing_ids_set

                # 区分可删除题目和不可删除题目
                deletable_questions = questions.filter(author=request.user)
                no_deletable_ids = existing_ids_set - set(deletable_questions.values_list('id', flat=True))


                # 执行软删除
                updated_count = deletable_questions.update(
                    is_deleted = True,
                    deleted_at = timezone.now()
                )

                # 准备返回数据
                result_data = {
                    'deleted_count': updated_count,
                    'no_existent_ids': list(no_existent_ids),
                    'no_deletable_ids': list(no_deletable_ids)
                }
                
                # 构造响应数据
                messages = []
                if updated_count:
                    messages.append(f'成功删除 {updated_count} 道题目')
                if no_existent_ids:
                    messages.append(f'未找到题目ID: {", ".join(map(str, no_existent_ids))}')
                if no_deletable_ids:
                    messages.append(f'无权限删除题目ID: {", ".join(map(str, no_deletable_ids))}')

                status_code = 200 if updated_count > 0 else 400    
                return api_response(status_code, '; '.join(messages), data=result_data)
        except Exception as e:
            return api_response(400, f'删除失败 {str(e)}', data={})
        
    
    @action(methods=['POST'], detail=False, url_path='batch_create')
    def create_batch(self, request, *args, **kwargs):
        """
        批量添加题目
        """
        user = request.user
        if not user:
            return api_response(400, '用户Session未正常传输')
        data = request.data.get('data', []) # 获取data字段
        if not isinstance(data, list):
            return api_response(400, '参数data必须是列表', data={})
        
        # 定义type与序列化器的映射关系
        TYPE_SERIALIZER_MAP = {
            '单选题': SingleChoiceQuestionSerializer,
            '多选题': MultipleChoiceQuestionSerializer,
            '判断题': TrueOrFalseQuestionSerializer,
            '填空题': FillInBlankQuestionSerializer,
            '问答题': QuestionAndAnswerQuestionSerializer,
        }

        try:
            with transaction.atomic():  # 事务保护
                results = []
                for item in data:
                    # 提取知识点关联数据
                    knowledge_point_ids = item.pop('knowledge_point_ids', [])
                    relevance = item.pop('relevance', None)
                    is_primary = item.pop('is_primary', False)
                    question_type = item.get('type')
                    # 检查type字段是否存在
                    if not question_type:
                        raise ValueError('缺少type字段')
                    serializer_class = TYPE_SERIALIZER_MAP.get(question_type)
                    if not serializer_class:
                        raise ValueError(f"无效的题目类型：{question_type}")
                    
                    #创建题目
                    serializer = serializer_class(data=item, context=self.get_serializer_context())
                    # serializer.validated_data['author'] = user_id
                    serializer.is_valid(raise_exception=True)
                    question = serializer.save(author=user)

                    # 创建知识点关联
                    for item in knowledge_point_ids:

                        knowledge_point_instance = KnowledgePoint.objects.get(id=item)
                        QuestionKnowledgePoint.objects.create(
                            question = question,
                            knowledge_point = knowledge_point_instance,
                            relevance = relevance,
                            is_primary = is_primary
                        )

                    # 创建版本记录
                    question_content = create_question_version(question)

                    results.append({
                        'knowledge_point_ids': knowledge_point_ids,
                        'content': question_content
                    })
                    
                return api_response(200, f'成功创建 {len(results)} 题', {'results': results})
        except Exception as e:
            return api_response(400, f'创建失败: {str(e)}', data={})
            
    @action(methods=['POST'], detail=False, url_path='excel_upload')
    def excel_upload(self, request, *args, **kwargs):
        """
        解析excel文件，返回结构化数据
        """
        excel_file = request.FILES.get('file')  # 获取上传的文件
        print(f"excel_file:{excel_file}")
        if not excel_file:
            return api_response(400, '未上传文件', data={})

        try:
            # 读取Excel文件
            df = pd.read_excel(excel_file)
            print(df)
            # 基本验证
            if df.empty:
                return api_response(400, '文件内容为空', data={})
            
            # 解析Excel数据为题目列表
            question_data = parse_excel_data(df)
            print(f"question_data: {question_data}")
            # return api_response(200, '解析成功', data=question_data)
            
            # 返回解析结果，解决前端出现的中文乱码
            return JsonResponse({'code': 200, 'message': '解析成功', 'data': question_data},
                                json_dumps_params={'ensure_ascii': False},
                                content_type='application/json; charset=utf-8'
                                )
        except Exception as e:
            return api_response(400, f'解析失败: {str(e)}', data={})

    @action(methods=['POST'], detail=False, url_path='ai_selection')
    def select_question(self, request, *args, **kwargs):
        """
        AI选题-抽取试题
        """
        knowledge_point_ids = request.data.get('knowledge_point_ids', [])  # 知识点（即题目关联的知识点id）
        if not isinstance(knowledge_point_ids, list):
            return api_response(400, '参数knowledge_point_ids必须是列表')
        question_counts = request.data.get('question_counts',{})  # 题目数量
        difficulty = request.data.get('difficulty')  # 题目难度
        direction = request.data.get('direction')  # 考核目标--知识方向
        # 定义type与序列化器的映射关系
        TYPE_SERIALIZER_MAP = {
            '单选题': SingleChoiceQuestionSerializer,
            '多选题': MultipleChoiceQuestionSerializer,
            '判断题': TrueOrFalseQuestionSerializer,
            '填空题': FillInBlankQuestionSerializer,
            '问答题': QuestionAndAnswerQuestionSerializer,
        }
        MODEL_MAP = {
                '单选题': SingleChoiceQuestion,
                '多选题': MultipleChoiceQuestion,
                '判断题': TrueOrFalseQuestion,
                '填空题': FillInBlankQuestion,
                '问答题': QuestionAndAnswerQuestion,
        }
        # print(f"receive difficulty: {difficulty}")
        # print(f"knowledge_point: {knowledge_point}")

        # 构建基础查询条件
        base_query = Q(is_deleted=False) & (Q(author=request.user) | Q(visible='公开'))  # 只查询未删除、用户自己创建和公开的题目
        if difficulty is not None:
            base_query &= Q(difficulty=difficulty)
        if direction:
            base_query &= Q(direction=direction)
        if knowledge_point_ids:
            base_query &= Q(knowledge_point__id__in=knowledge_point_ids)


        result = []
        warnings = []  # 用于存储题目数量不足的警告信息

        # print(f"base_query: {base_query}")
        # print(f"question_count: {question_counts}")
        # 按题型查询指定数量的题目
        for question_type, count in question_counts.items():
            if count <= 0:
                continue

            # 根据题型选择对应的模型
            model = MODEL_MAP.get(question_type)
            if not model:
                continue
                
            try:
                # 查询符合条件的题目
                queryset = model.objects.filter(base_query).values_list('id', flat=True)
                available_count = queryset.count()

                # 确定实际获取的题目数量
                actual_count = min(count, available_count)
                if actual_count < count:
                    warnings.append(f"{question_type}题型数量不足：请求{count}道，实际只找到{actual_count}道")

                if actual_count <= 0:
                    continue

                # 随机选择题目id
                selected_ids = random.sample(list(queryset), actual_count)
                selected_questions = model.objects.filter(id__in=selected_ids)

                # 转换为统一格式
                for question in selected_questions:
                    serializer = TYPE_SERIALIZER_MAP.get(question_type)(question)
                    result.append(serializer.data)
                # print(f"result: {result}")

            except Exception as e:
                raise ValueError(f"选题失败: {str(e)}")

        response_data = {
            'question': result,
            'count':len(result)
        }

        if warnings:
            response_data['warning'] = '部分题型题目数量不足'
            response_data['details'] = warnings

        return api_response(200, '选题成功', response_data)


    @action(methods=["POST"], detail=False, url_path='knowledge_points')
    def bulk_update_knowledge_point(self, request):
        """
        批量新增/修改试题知识点关联（仅题目创建者可以操作）
        """
        serializer = QuestionKnowledgePointBulkSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        question_ids = serializer.validated_data['question_ids']
        kp_ids = serializer.validated_data['knowledge_point_ids']
        if not isinstance(question_ids, list):
            return api_response(400, '参数question_ids必须是列表')
        question_ids = [int(i) for i in question_ids if str(i).isdigit()]  # 过滤掉非数字的id
        if not question_ids:
            return api_response(400, '题目id不能为空')
        
        # 查询当前用户创建的题目ID
        user_question_ids = set(
            BaseQuestion.objects.filter(
                id__in=question_ids,  # 仅筛选传入的id
                author=request.user,  # 仅当前用户创建的题目
            ).values_list('id', flat=True)
        )
        all_input_ids = set(question_ids)
        no_permission_ids = list(all_input_ids - user_question_ids)

        # 仅对有权限的题目进行操作
        target_question_ids = list(user_question_ids)
        if not target_question_ids:
            return api_response(400, '无权限修改题目的知识点关联', data={'no_permission_ids': no_permission_ids})

        # 获取已存在的关联
        existing = set(QuestionKnowledgePoint.objects.filter(
            question__id__in=target_question_ids,
            knowledge_point__id__in=kp_ids
            ).values_list('question_id', 'knowledge_point_id')
        )

        # 批量创建数据
        relations_to_create = []
        for question in target_question_ids:
            for kp in kp_ids:
                if (question, kp) not in existing:
                    relations_to_create.append(
                        QuestionKnowledgePoint(
                            question_id=question,
                            knowledge_point_id=kp,
                            relevance=0.5,
                            is_primary=False
                        )
                    )
        
        # 批量创建
        created_count = 0
        if relations_to_create:
            created = QuestionKnowledgePoint.objects.bulk_create(relations_to_create)
            created_count = len(created)

        # 构建响应数据
        response_data = {
            'created_count': created_count,
            'operated_question_ids': target_question_ids
        }
        if no_permission_ids:
            response_data['no_permission_ids'] = no_permission_ids
            message = f'成功关联{created_count}个知识点到题目，部分题目无权限操作: {no_permission_ids}'
        else:
            message = f'成功关联{created_count}个知识点到题目'

        return api_response(200, message, data=response_data)


    @action(methods=['GET'], detail=False, url_path='user_list')
    def user_list(self, request):
        """
        获取归属人列表（课程入口）
        """
        user_id = request.user.id
        if not user_id:
            return api_response(400, '用户Session未正常传输')
        course = request.query_params.get('course_id')
        semester = request.query_params.get('semester_id')
        if not course or not semester:
            return api_response(400, 'error', '课程或学期参数不能为空')
        
        try:
            # 获取当前课程学期下的所有教师关联记录
            course_semester = CourseSemester.objects.get(course=course, semester=semester, user_id=user_id)
            if not course_semester:
                return api_response(400, 'error', '指定的课程学期不存在')

            course_teachers = CourseTeacher.objects.filter(course_semester=course_semester)
            teacher_ids = course_teachers.values_list('teacher_id', flat=True)

            users = UserInfo.objects.filter(id__in=teacher_ids)
            serializer = UserInfoListSerializer(users, many=True)
            return api_response(200, 'success', serializer.data)
        except Exception as e:
            return api_response(400, f'查询失败: {str(e)}', data={})
    

class AIQuestionViewSet(viewsets.GenericViewSet):

    """
    AI生成题目视图集
    generate: 生成试题
    """
    serializer_class = None
    permission_classes = [permissions.IsAuthenticated]

    @action(methods=['POST'], detail=False)
    def generate(self, request):
        """
        生成题目
        """
        data = request.data.copy()

        # 类型转换处理
        try:
            if 'difficulty' in data:
                data['difficulty'] = int(data['difficulty'])  # 转换为整数
                
            # 转换所有题目数量参数
            count_fields = [
                'single_question_count',
                'multiple_question_count', 
                'boolean_question_count',
                'faq_question_count',
                'blank_filling_question_count'
            ]
            
            for field in count_fields:
                if field in data and data[field] not in [None, '']:
                    data[field] = int(data[field])
                    
        except (ValueError, TypeError) as e:
            return api_response(400, f'参数类型错误: {str(e)}')

        document = request.FILES.get('documents')  # 获取上传的文件
        print(f"question_document:{document}")


        # 处理知识点ID
        # knowledge_point_ids = data.get('knowledge_point_ids', [])
        # print(f"knowledge_point_ids:{knowledge_point_ids}")
        # if knowledge_point_ids:
        #     # 确保是列表类型
        #     if not isinstance(knowledge_point_ids, list):
        #         knowledge_point_ids = [knowledge_point_ids]
            
        if document: # 如果有文件上传
            if not document.name.lower().endswith('.docx'):  # 检查文件类型
                return api_response(400, 'error', '仅支持上传Word文档(.docx)')
            # 检查文件大小
            if document.size > 3 * 1024 * 1024:  # 3MB
                return api_response(400, 'error', '文件大小超出限制')
            try:
                data['content'] = upload_file_docx(document)  # 处理上传的文件
                print(f"document_content:{data['content']}")
            except Exception as e:
                return api_response(400, 'error', str(e))
        else:
            data['content'] = data.get('content', '')
        
        try:
            resp = generate_quiz(data)
                
            if resp.status_code != 200:
                error_detail = json.loads(resp.content).get('error', '未知错误')
                return api_response(400, 'AI试题生成失败', error_detail)
            
            resp_data = json.loads(resp.content)
            return api_response(200, '试题生成成功', resp_data.get('data'))

        except Exception as e:
            return api_response(400, '试题生成失败', str(e))


class PaperViewSet(viewsets.ModelViewSet):
    """
    试卷视图集
    """
    queryset = Paper.objects.all()
    pagination_class = StandardResultsSetPagination
    # filter_backends = [DjangoFilterBackend]
    # filterset_class = PaperFilter
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        # 动态选择序列化器
        if self.action in ['create', 'update', 'partial_update']:
            return PaperCreateUpdateSerializer
        return PaperSerializer
    
    def create(self, request, *args, **kwargs):
        """
        新增试卷
        """
        user = request.user.id
        if not user:
            return api_response(400, '用户Session未正常传输')
        data = request.data.copy()
        data['user'] = user

        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return api_response(200, '试卷创建成功', serializer.data)
    
    def update(self, request, *args, **kwargs):
        """
        修改试卷
        """
        partial = kwargs.pop('partial', False)
        try:
            instance = self.get_object()
        except Exception as e:
            return api_response(400, f'试卷不存在: {str(e)}')
        data = request.data.copy()
        if instance.author.id != request.user.id:
            return api_response(400, '无权限修改该试卷')
        else:
            data['user'] = request.user.id  # 确保user字段正确传递
        serializer = self.get_serializer(instance, data=data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        if getattr(instance, '_prefetched_objects_cache', None):
            instance._prefetched_objects_cache = {}

        return api_response(200, '试卷修改成功', serializer.data)

    def retrieve(self, request, *args, **kwargs):
        """
        获取试卷详情
        """
        try:
            instance = self.get_object()
        except Exception as e:
            return api_response(400, f'试卷不存在: {str(e)}')
        serializer = PaperDetailSerializer(instance)
        return api_response(200, '查询成功', serializer.data)
    

class KnowledgeExamViewSet(viewsets.ModelViewSet):
    """
    考试/作业视图集
    """
    queryset = KnowledgeExam.objects.all()
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend]
    filterset_class = KnowledgeExamFilter
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        # 动态选择序列化器
        if self.action in ['create', 'update', 'partial_update']:
            return KnowledgeExamCreateUpdateSerializer
        return KnowledgeExamSerializer
    
    def create(self, request, *args, **kwargs):
        """
        新增考试/作业
        """
        user = request.user
        print(f"user:{user}")
        if not user:
            return api_response(400, '用户Session未正常传输')
        data = request.data.copy()
        data['author'] = user.id

        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return api_response(200, '考试/作业创建成功', serializer.data)
    
    def list(self, request, *args, **kwargs):
        """
        获取考试/作业列表 -【教师视角：查看当前课程学期下所有的考试/作业列表】
        """
        try:
            user_id = request.user.id
            if not user_id:
                return api_response(400, '用户Session未正常传输')
            course_id = request.query_params.get('course_id', None)
            semester_id = request.query_params.get('semester_id', None)
            if not course_id or not semester_id:
                return api_response(400, 'error', '请同时提供课程ID和学期ID')
            
            # 获取课程学期对象
            try:
                course_semester_queryset = CourseSemester.objects.filter(
                    course=course_id,
                    semester=semester_id
                )
                print(f"course_semester_queryset: {course_semester_queryset}")
            except ObjectDoesNotExist:
                return api_response(400, 'error', '指定的课程学期不存在或您无权限访问')
            
            # 构建查询
            queryset = KnowledgeExam.objects.filter(
                cs_exam_relations__course_semester__in=course_semester_queryset,
                # author_id=user_id  # 仅查询当前用户创建的考试/作业
            ).distinct().annotate(
                total_take_count=Count(
                    'usertakepartexam',
                    distinct=True
                ),
                submitted_count=Count(
                    'usertakepartexam',
                    filter=~Q(usertakepartexam__take_part_status='未提交'),
                    distinct=True
                )
            ).prefetch_related(
                Prefetch(
                    'cls_exam_relations',
                    queryset=ClassKnowledgeExam.objects.select_related('class_id'),
                    to_attr='prefetched_cls_relations'
                )
            ).order_by('-id')

            # 显式应用过滤器（让usage_type等参数生效）
            filtered_queryset = self.filter_queryset(queryset)

            # 分页处理
            page = self.paginate_queryset(filtered_queryset)
            if page is not None:
                serializer = KnowledgeExamListSerializer(page, many=True, context={'request':request})
                page_data = self.get_paginated_response(serializer.data)
                return api_response(200, '查询成功', page_data)
            serializer = KnowledgeExamListSerializer(queryset, many=True, context={'request':request})
            return api_response(200, '查询成功', serializer.data)
        except Exception as e:
            return api_response(400, '查询失败', str(e))


    @action(methods=['GET'], detail=False, url_path='student_list')
    def student_list(self, request, *args, **kwargs):
        """
        获取作业/考试列表-【学生视角】
        """
        try:
            user_id = request.user.id
            if not user_id:
                return api_response(400, '用户Session未正常传输')
            course_semester_id = request.query_params.get('course_semester_id', None)
            if not course_semester_id:
                return api_response(400, 'error', '请提供课程学期ID')
            try:
                course_semester = CourseSemester.objects.get(id=course_semester_id)
            except CourseSemester.DoesNotExist:
                return api_response(400, 'error', '指定的课程学期不存在或您无权限访问')
            usage_type = request.query_params.get('usage_type', None)
            if usage_type not in ['exam', 'homework']:
                return api_response(400, 'error', 'usage_type参数错误，仅支持exam或homework')
            
            # 使用Prefetch优化查询
            user_exams_prefetch = Prefetch(
                'usertakepartexam_set',
                queryset=UserTakePartExam.objects.filter(user_id=user_id),
                to_attr='user_take_part_exam'
            )
            course_semester_prefetch = Prefetch(
                'cs_exam_relations',
                queryset=CourseSemesterExam.objects.select_related('course_semester'),
                to_attr='prefetched_cs_relations'
            )

            # 获取该学生参加该课程的所有考试/作业
            queryset = KnowledgeExam.objects.filter(
                cs_exam_relations__course_semester=course_semester,
                usertakepartexam__user_id=user_id,
                cs_exam_relations__usage_type=usage_type,
                status='已发布',
            ).prefetch_related(user_exams_prefetch, course_semester_prefetch).distinct().order_by('-id')

            # 分页处理
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = KnowledgeExamStudentListSerializer(page, many=True, context={'request': request})
                page_data = self.get_paginated_response(serializer.data)
                return api_response(200, '查询成功', page_data)

            serializer = KnowledgeExamStudentListSerializer(queryset, many=True, context={'request':request})
            return api_response(200, '查询成功', serializer.data)
        
        except Exception as e:
            return api_response(400, '查询失败', str(e))

    
    def update(self, request, *args, **kwargs):
        """
        修改考试/作业
        """
        partial = kwargs.pop('partial', False)
        try:
            instance = self.get_object()
            user = request.user
            print(f"user:{user}")
            # 验证用户有无修改权限
            print(f"instance_author: {instance.author}")
            if instance.author != user:
                return api_response(400, '您没有权限修改该考试/作业')

            if instance.status == '已发布':
                return api_response(400, '已发布的考试/作业无法修改')

            data = request.data.copy()
            data['author'] = user.id

            serializer = self.get_serializer(instance, data=data, partial=partial)
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)
            if getattr(instance, '_prefetched_objects_cache', None):
                instance._prefetched_objects_cache = {}
            return api_response(200, '修改成功', serializer.data)
        except Exception as e:
            return api_response(400, f'考试/作业不存在: {str(e)}')

    def retrieve(self, request, *args, **kwargs):
        """
        获取考试/作业修改详情
        """
        try:
            instance = self.get_object()
            serializer = KnowledgeExamDetailSerializer(instance)
            return api_response(200, '查询成功', serializer.data)
        except Exception as e:
            return api_response(400, f'考试/作业不存在: {str(e)}')

    @action(methods=['POST'], detail=False, url_path='delete')
    def batch_delete(self, request, *args, **kwargs):
        """
        删除考试/作业（支持单一和批量删除）
        """
        try:
            with transaction.atomic():

                # 1. 获取待删除的考试ID列表
                exam_ids = request.data.get('exam_ids', [])
                if not isinstance(exam_ids, list) or len(exam_ids) == 0:
                    return api_response(400, '请提供有效的考试ID列表（非空数组）')
                exam_ids = [int(i) for i in exam_ids if str(i).isdigit()]  # 过滤掉非数字的id

                user = request.user
                if not user:
                    return api_response(400, '用户Session未正常传输')
                
                # 2. 查询所有待处理的考试
                all_exams = KnowledgeExam.objects.filter(id__in=exam_ids).select_related('author')

                # 3. 分类：可删除、已发布、无权限、不存在
                deletable_exams = []  # 可删除的考试
                published_exams = []  # 已发布的考试不能删
                no_permission_ids = []  # 无权限删除的考试
                no_exist_ids = []  # 不存在的考试ID

                existing_ids = set(all_exams.values_list('id', flat=True))
                for exam_id in exam_ids:
                    if exam_id not in existing_ids:
                        no_exist_ids.append(exam_id)
                
                for exam in all_exams:
                    if exam.status == '已发布':
                        published_exams.append(exam.id)
                        continue
                    if exam.author != user:
                        no_permission_ids.append(exam.id)
                        continue
                    deletable_exams.append(exam)

                # 4. 执行删除操作（仅删除可删除的考试）
                deleted_count = 0
                if deletable_exams:
                    deletable_ids = [exam.id for exam in deletable_exams]

                    # 删除班级考试关联
                    ClassKnowledgeExam.objects.filter(exam_id__in=deletable_ids).delete()
                    # 删除课程学期考试关联
                    CourseSemesterExam.objects.filter(exam_id__in=exam_ids).delete()
                    # 删除试卷
                    paper_ids = [exam.paper.id for exam in deletable_exams if exam.paper]
                    if paper_ids:
                        Paper.objects.filter(id__in=paper_ids).delete()
                    # 删除考试本身
                    deleted_count, _ = KnowledgeExam.objects.filter(id__in=deletable_ids).delete()

                # 5. 构建详细响应信息
                message = f"成功删除 {deleted_count} 条考试/作业"
                details = {
                    "deleted_count": deleted_count,
                    "deleted_ids": deletable_ids
                }

                if published_exams:
                    message += f", 其中考试ID为{published_exams}的考试已发布，无法删除"
                if no_permission_ids:
                    message += f", 考试ID为{no_permission_ids}的考试您没有权限删除"
                if no_exist_ids:
                    message += f", 考试ID为{no_exist_ids}的考试不存在"
                
                return api_response(200, message, details)
            
        except Exception as e:
            return api_response(400, '删除失败', str(e))


    @action(methods=['POST'], detail=False, url_path='publish')
    def publish_exam(self, request, *args, **kwargs):
        """
        保存和发布考试
        1. 修改更新考试基本信息
        2. 修改更新班级考试关联
        3. 新增用户考试记录表（将班级中对应的学生id和考试id添加记录，并设用户参加考试状态为未提交）
        """
        exam_id = request.data.get('exam_id')
        if not exam_id:
            return api_response(400, '缺少考试ID')
        status = request.data.get('status', None)
        if not status or status not in ['已发布', '未发布']:
            return api_response(400, '缺少考试状态或状态不合法')
        user_id = request.user.id
        data = request.data.copy()
        data['author'] = user_id
        try:
            # 检查考试是否存在
            exam = KnowledgeExam.objects.get(id=exam_id)
            print(f"exam.author: {exam.author.pk}")
            print(f"user_id: {user_id}")
            if exam.status == '已发布':
                return api_response(400, '已发布，无需重复发布')
                
            elif exam.author.pk != user_id:
                return api_response(400, '您没有权限发布考试/作业')
            else:
                if status == '已发布':
                    serializer = KnowledgeExamPublishSerializer(exam, data=data, partial=True)
                    serializer.is_valid(raise_exception=True)
                    serializer.save()
                    return api_response(200, '发布成功')
                elif status == '未发布':
                    serializer = KnowledgeExamPublishSerializer(exam, data=data, partial=True)
                    serializer.is_valid(raise_exception=True)
                    serializer.save()
                    return api_response(200, '保存成功')

        except Exception as e:
            return api_response(400, 'error', str(e))

    @action(methods=['GET'], detail=False, url_path='overview')
    def get_exam_overview(self, request, *args, **kwargs):
        """
        作业总览 - 教师视角：
        1. 通过考试ID查询用户参加考试记录表，查询提交时间、学生ID、状态、成绩
        2. 通过学生ID列表查询用户表的学生姓名、学工号
        3. 通过班级ID查询班级名称进行班级过滤筛选
        4. 通过状态进行过滤筛选
        5. 根据学生姓名实现过滤筛选
        """
        exam_id = request.query_params.get('exam_id')
        # if not exam_id:
        #     return api_response(400, '缺少考试ID')

        try:
            exam_instance = KnowledgeExam.objects.get(pk=exam_id)

            user_exam_records = UserTakePartExam.objects.filter(exam_id=exam_instance).select_related('user')

            # 获取查询参数
            class_id = request.query_params.get('class_id', '')
            status = request.query_params.get('status', '')
            student_name = request.query_params.get('student_name', None)

            # 班级过滤
            if class_id:
                # 获取该班级的学生ID列表
                student_ids = ClassMember.objects.filter(
                    class_obj_id=class_id,
                    student__isnull=False
                ).values_list('student_id', flat=True)

                user_exam_records = user_exam_records.filter(user_id__in=student_ids)

            # 状态过滤
            if status:
                user_exam_records = user_exam_records.filter(take_part_status=status)

            # 学生姓名过滤
            if student_name:
                user_exam_records = user_exam_records.filter(user__first_name__icontains=student_name)
            
            user_exam_records = user_exam_records.order_by('user__id')
            # 分页处理
            page = self.paginate_queryset(user_exam_records)
            if page is not None:
                serializer = UserTakePartExamOverviewSerializer(page, many=True)
                page_data = self.get_paginated_response(serializer.data)
                return api_response(200, '查询成功', page_data)
            serializer = UserTakePartExamOverviewSerializer(user_exam_records, many=True)
            return api_response(200, '查询成功', serializer.data)
        except Exception as e:
            return api_response(400, '查询失败', str(e))
        

    @action(methods=['GET'], detail=False, url_path='student_retrieve')
    def get_student_exam(self, request, *args, **kwargs):
        """
        学生考试/作业详情：
        1. 通过学生ID查询用户参加考试记录表，查询考试ID、状态、成绩
        2. 通过考试ID查询考试表的考试名称、考试类型、考试时间、班级ID
        3. 通过班级ID查询班级名称进行班级过滤筛选
        4. 通过状态进行过滤筛选
        5. 根据考试时间进行排序
        """
        student_id = request.query_params.get('student_id')
        if not student_id:
            return api_response(400, '缺少学生ID')
        try:
            user_exam_records = UserTakePartExam.objects.filter(user_id=student_id).select_related('exam')

            # 获取查询参数
            exam_type = request.query_params.get('exam_type', '')
            class_id = request.query_params.get('class_id', '')
            status = request.query_params.get('status', '')
            sort_by = request.query_params.get('sort_by', '-exam__exam_time')  # 默认按考试时间倒序排序
            # 考试类型过滤
            if exam_type:
                user_exam_records = user_exam_records.filter(exam__exam_type=exam_type)
            # 班级过滤
            if class_id:
                user_exam_records = user_exam_records.filter(exam__class_exams__class_id=class_id)
            # 状态过滤
            if status:
                user_exam_records = user_exam_records.filter(take_part_status=status)
            # 排序
            user_exam_records = user_exam_records.order_by(sort_by)
        except Exception as e:
            return api_response(400, '查询失败', str(e))

    
    @action(methods=['POST'], detail=False, url_path='submit')
    def submit_exam(self, request, *args, **kwargs):
        """
        提交考试/作业 - 学生提交
        1. 校验提交数据并创建反馈记录
        2. 更新考试状态和时间
        3. 处理试题版本关联
        """
        user = request.user  # 获取当前登录用户
        request_data = request.data.copy()

        try:
            # 1. 提取并验证exam_info
            exam_info = request_data.pop('exam_info', {})
            if not exam_info:
                raise ValueError("缺少exam_info参数")
            exam_id = exam_info.get('exam_id')
            if not exam_id:
                raise ValueError("exam_info中缺少exam_id")
            
            # 2. 验证user_take_exam
            user_take_exam = request_data.pop('user_take_exam')
            if not user_take_exam:
                raise ValueError("缺少user_take_exam参数")
            
            # 2. 构造序列化器所需数据
            serializer_data = {
                'exam_id': exam_id,
                'user': user.id,
                'questions': request_data.pop('questions', []),
                'user_take_exam': user_take_exam
            }
            # 3. 其他字段
            if 'end_time' in request_data:
                serializer_data['end_time'] = request_data['end_time']
            if 'take_part_status' in request_data:
                serializer_data['take_part_status'] = request_data['take_part_status']
            
            serializer = UserExamQuestionFeedbackSubmitSerializer(
                data=serializer_data,
                context={'request': request}  # 传递request以便获取当前用户
            )

            serializer.is_valid(raise_exception=True)
            result = serializer.save()            

            return api_response(200, '提交成功，批阅进行中', data={
                'exam_id': exam_id,
                'feedback_count': result.get('count', 0)
            })

        except KnowledgeExam.DoesNotExist:
            return api_response(400, '内容不存在')
        except Exception as e:
            return api_response(400, '提交失败', str(e))

    @action(methods=['POST'], detail=True, url_path='detail')
    def get_exam_detail(self, request, *args, **kwargs):
        """
        获取考试/作业详情
        """
        try:
            instance = self.get_object()
        except Exception as e:
            return api_response(400, f'考试/作业不存在: {str(e)}')
        serializer = UserTakePartExamDetailSerializer(instance)
        return api_response(200, '查询成功', serializer.data)

class UserTakePartExamViewSet(viewsets.ModelViewSet):
    """
    用户参加考试记录视图集
    """
    queryset = UserTakePartExam.objects.all()
    serializer_class = UserTakePartExamSerializer
    permission_classes = [permissions.IsAuthenticated]

    def retrieve(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            serializer = UserTakePartExamDetailSerializer(instance)
            return api_response(200, '查询成功', serializer.data)
        except Exception as e:
            return api_response(400, '查询失败', str(e))
    

    # @action(methods=['GET'], detail=False, url_path='by_exam/(?P<exam_id>\d+)')
    # def retrieve_by_exam(self, request, exam_id=None):
    #     """
    #     获取考试/作业详情  【已弃用】
    #     1. 通过考试ID获取当前用户的参加记录详情
    #     示例：GET /api/v1/take_part_exam/by_exam/1/
    #     """
    #     try:
    #         user_id = request.user.id
    #         if not user_id:
    #             return api_response(400, '用户Session未正常传输')
    #         # 获取当前用户的考试参加记录    
    #         instance = UserTakePartExam.objects.get(
    #             user = user_id,
    #             exam_id = exam_id
    #         )
    #         serializer = UserTakePartExamDetailSerializer(instance)
    #         return api_response(200, '查询成功', serializer.data)
    #     except Exception as e:
    #         return api_response(400, f'考试/作业不存在: {str(e)}')
    
    @action(methods=['GET'], detail=False, url_path='student_answer/(?P<exam_id>\d+)')
    def retrieve_student(self, request, exam_id=None):
        """
        获取学生参加考试/作业答卷详情
        1. 通过考试ID获取当前用户的参加记录详情
        示例：GET /api/v1/take_part_exam/student_answer/1/
        """
        user = request.user  # 获取当前登录的用户
        if not user:
            return api_response(400, '用户Session未正常传输')
        try:
            # 获取当前用户的考试参加记录    
            instance = UserTakePartExam.objects.get(
                user = user.id,
                exam_id = exam_id
            )
            serializer = UserTakePartExamStudentDetailSerializer(instance)
            return api_response(200, '查询成功', serializer.data)
        except Exception as e:
            return api_response(400, f'考试/作业不存在: {str(e)}')

class UserExamQuestionFeedbackViewSet(viewsets.ModelViewSet):
    """
    用户考试/作业问题反馈视图集
    """
    queryset = UserExamQuestionFeedback.objects.all()
    serializer_class = UserExamQuestionFeedbackSerializer
    permission_classes = [permissions.IsAuthenticated]

    @action(methods=['POST'], detail=False, url_path='publish_mark')
    def publish_mark(self, request, *args, **kwargs):
        """
        教师阅卷并发布成绩
        1. 更新用户答题反馈表中的得分和评语
        2. 更新用户参加考试记录表中的总分
        3. 获取下一个需要发布的考试ID
        """
        # 1. 从请求中获取数据
        user = request.user  # 获取当前登录用户
        if not user:
            return api_response(400, '用户Session未正常传输')
        request_data = request.data.copy()

        exam_id = request_data.pop('exam_info', {}).get('exam_id')
        user_id = request_data.pop('user_info', {}).get('student_id')
        user_take_exam = request_data.pop('user_take_exam', None)
        course = request_data.pop('course_id', None)
        semester = request_data.pop('semester_id', None)
        if not course or not semester:
            return api_response(400, '缺少course_id或semester_id')
        course_semester_obj = CourseSemester.objects.get(
            course=course,
            semester=semester,
            user_id=user.id
        )
        if not course_semester_obj:
            return api_response(400, '指定的课程学期不存在或您无权限访问')

        if not exam_id or not user_id or not user_take_exam:
            return api_response(400, '缺少exam_id或student_id或user_take_exam')
        
        request_data['exam_id'] = exam_id
        request_data['user'] = user_id
        request_data['user_take_exam'] = user_take_exam
        request_data['course_semester'] = course_semester_obj.id
        request_data['course'] = course
        request_data['semester'] = semester

        # 检查考试状态
        exam_record = UserTakePartExam.objects.filter(
            exam_id=exam_id,
            user_id=user_id
        ).first()
        if exam_record.take_part_status != '已阅卷':
            return api_response(400, '当前考试状态不允许发布')
        
        serializer = UserExamQuestionFeedbackPublishSerializer(
            data = request_data,
            context={'request': request}
        )
        try:
            serializer.is_valid(raise_exception=True)
            user_exam_record = serializer.save()

            # 从序列化器实例中获取 next_publish_id
            next_publish_id = getattr(serializer, 'next_publish_id', None)

            return api_response(200, '发布成功', {
                'exam_id': exam_id,
                'next_publish_id': next_publish_id
            })
        except Exception as e:
            return api_response(400, '发布失败', str(e))


        

        




