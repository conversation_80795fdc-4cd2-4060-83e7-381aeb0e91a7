from django.db import transaction
from django.dispatch import receiver

from .signals import course_exam_analysis, class_exam_analysis, course_learn_analysis
from .exam_analysis import AIExamAnalysisService
from .course_analysis import AICourseAnalysisService
from .class_analysis import AIClassAnalysisService
from .models import ExamAnalysis, CourseAnalysis
from exam.models import *
from exam.signals import exam_analysis

@receiver(exam_analysis)
def handle_exam_analysis(sender, **kwargs):
    """
    学情分析
    1. 考试/作业学情分析
    2. 课程学情分析
    """
    print(f"【收到exam_analysis信号，参数：{kwargs}】")

    try:
        with transaction.atomic():
            exam = kwargs.get('exam_id')
            user = kwargs.get('user')
            total_score = kwargs.get('total_score')
            course = kwargs.get('course')
            semester = kwargs.get('semester')
            course_semester = kwargs.get('course_semester')
            total_score = kwargs.get('total_score')
            usage_type = kwargs.get('usage_type')

            # course_semester = CourseSemester.objects.get(course=course, semester=semester, user_id=user.id)
            is_exam = usage_type == "exam"
            

            state = {
                'user_name': user.first_name,
                'user_number': user.username,
                'user_id': user.id,
                'exam_name': exam.name,
                'is_exam': is_exam,
                'analysis_score': total_score,
                'course_id': course.id,
                'semester_id': semester.id
            }

            analysis_service = AIExamAnalysisService()
            analysis_result = analysis_service.analyze_exam(state)
            print(f"考试/作业分析结果：{analysis_result}")

            # 存储考试分析结果
            ExamAnalysis.objects.create(
                user = user,
                exam_id = exam,
                is_exam = is_exam,
                analysis_score = total_score,
                course_semester = course_semester,
                analysis_exam = analysis_result['analysis_exam'],
                abstract_exam = analysis_result['abstract_exam']
            )

            exam_analysis_summary = ExamAnalysis.objects.filter(
                course_semester=course_semester,
                is_exam=True
            ).select_related('user')

            abstract_combined_exam = "\n".join(
                f"{record.user.username}：{record.abstract_exam}"
                for record in exam_analysis_summary 
                if record.abstract_exam
            )
            print("拼接后的考试摘要内容：",abstract_combined_exam)

            homework_analysis_summary = ExamAnalysis.objects.filter(
                course_semester=course_semester, is_exam=False
            ).select_related('user')

            abstract_combined_homework = "\n".join(
                f"{record.user.username}：{record.abstract_exam}"
                for record in homework_analysis_summary 
                if record.abstract_exam
            )
            print("拼接后的作业摘要内容：",abstract_combined_homework)

            # 发送课程分析信号
            print(f"【考试分析成功，准备发送课程学情分析信号，course_id:{course.id}, semester_id:{semester.id}】")
            course_exam_analysis.send(
                sender=None,
                user=user,
                course_id = course.id,
                semester_id = semester.id,
                course_semester = course_semester,
                abstract_combined_exam = abstract_combined_exam,
                abstract_combined_homework = abstract_combined_homework,
            )

            # 发送班级考试/作业信号
            class_exams = ClassKnowledgeExam.objects.filter(
                exam_id=exam.id,
            ).select_related('class_id')
            for class_exam in class_exams:
                class_abstracts = ExamAnalysis.objects.filter(
                    exam_id=exam.id,
                    user__students__class_obj=class_exam.class_id
                ).exclude(abstract_exam='').values_list('abstract_exam', flat=True)

                summary_class_exam = ",".join(class_abstracts)
                
                print(f"【发送班级考试/作业信号，class_id:{class_exam.class_id.id}】")

                class_exam_analysis.send(
                    sender=None,
                    course_id = course.id,
                    semester_id = semester.id,
                    course_semester = course_semester,
                    course_name = course.title,
                    class_id = class_exam.class_id.id,
                    exam_id = exam.id,
                    summary_class_exam = summary_class_exam,
                )

    except Exception as e:
        print(f"作业/考试AI分析失败: {str(e)}")
    

@receiver(course_exam_analysis)
def handle_course_exam_analysis(sender, **kwargs):
    """
    课程分析【课程考试成绩分析、课程作业情况分析】
    1. 分析考试成绩
    2. 分析作业完成情况
    3. 分析课程评语和课程状态
    """
    print(f"【收到course_exam_analysis信号，参数：{kwargs}】")
    try:
        with transaction.atomic():
            user = kwargs.get('user')
            course_id = kwargs.get('course_id')
            semester_id = kwargs.get('semester_id')
            course_semester = kwargs.get('course_semester')
            abstract_combined_exam = kwargs.get('abstract_combined_exam')
            abstract_combined_homework = kwargs.get('abstract_combined_homework')
            
            course_name = Course.objects.get(id=course_id).title
            # 学生学习进度完成情况拼接 - 拼接课程学期下的章节+章节完成百分比
            chapter_completions = ChapterCompletion.objects.filter(
                chapter__course_semester=course_semester
            ).select_related('chapter')

            # 章节内容拼接：title:percentage;title:percentage;...
            result = []
            for item in chapter_completions:
                # 获取关联的Chapter表的title
                title = item.chapter.title
                # 拼接单条数据
                single = f"{title}:{item.percentage}"
                result.append(single)

            # 用分号连接所有数据
            final_str = ";".join(result)
            print(f"章节内容拼接结果：{final_str}")
            learn_situation_summary = final_str


            state = {
                'user_name': user.first_name,
                'user_number': user.username,
                'course_name': course_name,
                'abstract_combined_exam': abstract_combined_exam,
                'abstract_combined_homework': abstract_combined_homework,
                'learn_situation_summary': learn_situation_summary,
            }

            analysis_service = AICourseAnalysisService()
            analysis_result = analysis_service.analyze_course_exam(state)

            print(f"课程分析结果：{analysis_result}")
            # 存储课程分析结果
            CourseAnalysis.objects.create(
                user = user,
                course_semester = course_semester,
                exam_grade = analysis_result['exam_grade'],
                homework_situation = analysis_result['homework_situation'],
                course_comment = analysis_result['course_comment'],
                analysis_status = analysis_result['analysis_status'],
                abstract_course = analysis_result['abstract_course'],
                learn_progress = analysis_result['learn_progress'],
            )

    except Exception as e:
        print(f"课程考试AI分析失败: {str(e)}")
            

@receiver(class_exam_analysis)
def handle_class_exam_analysis(sender, **kwargs):
    """
    班级分析【班级考试/作业情况分析】
    1. 分析单个班级的考试/作业情况
    2. 分析课程中所有班级某次考试/作业的情况
    """
    print(f"【收到class_exam_analysis信号，参数：{kwargs}】")
    try:
        with transaction.atomic():
            class_id = kwargs.get('class_id')
            exam_id = kwargs.get('exam_id')
            summary_class_exam = kwargs.get('summary_class_exam')
            course_id = kwargs.get('course_id')
            semester_id = kwargs.get('semester_id')
            course_semester = kwargs.get('course_semester')

            course_name = kwargs.get('course_name')

            class_name = Class.objects.get(id=class_id).name
            exam_name = KnowledgeExam.objects.get(id=exam_id).name
            
            state = {
                'class_name': class_name,
                'exam_name': exam_name,
                'course_name': course_name,
                'summary_class_exam': summary_class_exam,
                'summary_classes_exam': "",
            }

            analysis_service = AIClassAnalysisService()
            analysis_result = analysis_service.analyze_class_exam(state)
            print(f"班级分析结果：{analysis_result}")
            
            # 更新当前班级的分析结果
            ClassKnowledgeExam.objects.filter(
                class_id = class_id,
                exam_id = exam_id,
            ).update(
                analysis_exams = analysis_result['analysis_exams'],
            )

            # 获取同一考试/作业的所有班级分析结果

            all_class_analysis = ClassKnowledgeExam.objects.filter(
                exam_id=exam_id
            ).exclude(analysis_exams__isnull=True).exclude(analysis_exams='')

            # 汇总所有班级的分析结果
            combined_class_analysis = "\n".join(
                f"班级 {item.class_id.name} 分析结果:\n{item.analysis_exams}" 
                for item in all_class_analysis
            )
            # 生成课程级别的分析
            course_state = {
                'class_name': '',
                'course_name': course_name,
                'exam_name': exam_name,
                'summary_class_exam': summary_class_exam,
                'summary_classes_exam': combined_class_analysis,  # 传入汇总结果
            }

            course_result = analysis_service.analyze_class_exam(course_state)
            print(f"课程级别分析结果：{course_result}")

            # 更新课程分析结果
            CourseSemesterExam.objects.update_or_create(
                course_semester = course_semester,
                exam_id = exam_id,
                defaults={
                    'analysis_course_exams': course_result['analysis_course_exams'],
                },
                updated_at = timezone.now(),
            )


    except Exception as e:
        print(f"以班级为单位 考试/作业AI分析失败: {str(e)}")


# @receiver(course_learn_analysis)
# def handle_course_learn_analysis(sender, **kwargs):
#     """
#     课程学习进度分析  【教师手动输入的学习进度情况】
#     """
#     print(f"【收到course_learn_analysis信号，参数：{kwargs}】")
#     try:
#         with transaction.atomic():
#             user_id = kwargs.get('user_id')
#             course_id = kwargs.get('course_id')
#             semester_id = kwargs.get('semester_id')
#             course_semester = kwargs.get('course_semester')
#             learn_situation_summary = kwargs.get('learn_situation_summary')
#             course_name = kwargs.get('course_name')
#             user_name = kwargs.get('user_name')

#             state = {
#                 'user_name': user_name,
#                 'course_name': course_name,
#                 'learn_situation_summary': learn_situation_summary,
#             }
#             analysis_service = AICourseAnalysisService()
#             analysis_result = analysis_service.analyze_course_exam(state)
#             print(f"课程学习进度分析结果：{analysis_result}")
#             # 更新学习进度分析结果
#             CourseAnalysis.objects.update_or_create(
#                 user_id = user_id,
#                 course_semester = course_semester,
#                 defaults={
#                     'learn_progress': analysis_result['learn_progress'],
#                 }
#             )
        
#     except Exception as e:
#         print(f"以班级为单位 考试/作业AI分析失败: {str(e)}")





