import os

from langchain_community.chat_models import ChatTongyi

LANGSMITH_TRACING=True
LANGSMITH_ENDPOINT="https://api.smith.langchain.com"
LANGSMITH_API_KEY="***************************************************"
LANGSMITH_PROJECT="pr-ordinary-colon-52"
OPENAI_API_KEY="sk-d5d356caae2e417e98b289f3e86903b1"
os.environ["LANGCHAIN_TRACING"]="true"
os.environ["LANGCHAIN_ENDPOINT"]="https://api.smith.langchain.com"
os.environ["LANGCHAIN_API_KEY"]="***************************************************"
os.environ["LANGCHAIN_PROJECT"]="pr-ordinary-colon-52"
os.environ["OPENAI_API_KEY"]="sk-d5d356caae2e417e98b289f3e86903b1"
os.environ["DASHSCOPE_API_KEY"]="sk-d5d356caae2e417e98b289f3e86903b1"


langsmith_llm = ChatTongyi(model="qwen-turbo", api_key=os.environ.get("sk-d5d356caae2e417e98b289f3e86903b1"))