import time

from django.db import models
from course.models import Chapter

# Create your models here.


def ppt_path(instance, filename):
    """
    自定义PPT动态上传路径
    :param instance:
    :param filename: 文件名
    :return:
    """
    filename = f'{int(time.time())}_{filename}'
    return 'ppt_file/{0}'.format(filename)


def ppt_json_path(instance, filename):
    """
    自定义PPT JSON文件动态上传路径
    :param instance:
    :param filename: 文件名
    :return:
    """
    filename = f'{int(time.time())}_{filename}'
    return 'ppt_file/json/{0}'.format(filename)


def ppt_png_path(instance, filename):
    """
    自定义PPT索引图片动态上传路径
    :param instance:
    """
    filename = f'{int(time.time())}_{filename}'
    return 'ppt_file/png/{0}'.format(filename)


class PptTmpModel(models.Model):
    """
    PPT模板表
    """
    title = models.CharField(max_length=90, verbose_name='标题', null=True)  # 模板标题
    description = models.CharField(max_length=900, verbose_name='描述', null=True, blank=True)  # 模板的简要描述和适用场景
    json_file_content= models.JSONField(
        verbose_name='JSON文件内容',
        null=True,
        blank=True
    )  # PPT模版json文件内容
    tmp_json_file = models.FileField(
        upload_to=ppt_json_path,
        verbose_name='JSON文件',
        null=True
    )  # PPT模版的JSON文件
    ppt_index_png = models.FileField(
        upload_to=ppt_png_path,
        verbose_name='索引图片',
        null=True,
        blank=True
    )  # PPT索引图片
    is_deleted = models.BooleanField(default=False, verbose_name='是否删除', null=True, blank=True)  # 软删除判断
    deleted_at = models.DateTimeField(verbose_name='删除时间', null=True, blank=True)  # 软删除时间
    is_public = models.BooleanField(default=False, verbose_name='是否公开', null=True, blank=True)  # 是否对所有用户公开可用
    
    # 模版类型
    TEMPLATE_TYPE_CHOICES = [
        ('lecture', '讲授型'),
        ('interactive', '互动型'),
        ('report', '汇报型'),
        ('creative', '创意型'),
        ('academic', '学术型'),
        ('other', '其他')
    ]
    template_type = models.CharField(
        default='lecture',
        choices=TEMPLATE_TYPE_CHOICES,
        max_length=20,
        verbose_name='模板类型'
    )

    # 主题风格
    STYLE_CHOICES = [
        ('modern', '现代简约'),
        ('classic', '经典正式'),
        ('colorful', '多彩活泼'),
        ('minimalist', '极简风格'),
        ('business', '商务专业'),
        ('education', '教育学术'),
        ('creative', '创意设计'),
        ('nature', '自然风光'),
        ('tech', '科技感'),
        ('other', '其他风格'),
    ]
    style = models.CharField(
        default='modern',
        choices=STYLE_CHOICES,
        max_length=20,
        verbose_name='主题风格'
    )

    color_scheme = models.CharField(max_length=100, verbose_name='颜色方案', null=True, blank=True)  # 模板的主要颜色方案
    metadata = models.JSONField(verbose_name='元数据', null=True, blank=True)  # 模版的详细配置信息
    author = models.ForeignKey(
        "user.UserInfo",
        on_delete=models.SET_NULL,  # 设置为null而不是级联删除
        verbose_name='创建者ID',
        null=True,
        blank=True
    )  # 关联到用户表的外键
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')  # 创建时间
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')  # 最后更新时间
    usage_count = models.IntegerField(default=0, verbose_name='使用次数')  # 该模板被使用的次数
    is_ai_generated = models.BooleanField(default=False, verbose_name='AI生成', null=True, blank=True)  # 是否由AI生成


    class Meta:
        default_permissions = ()
        db_table = 'ppt_tmp'
        verbose_name = 'PPT模板表'
        verbose_name_plural = verbose_name
        permissions = (
            ("add_ppt_tmp", "PPT模版添加"),
            ("view_ppt_tmp", "PPT模版查询"),
            ("change_ppt_tmp", "PPT模版修改"),
            ("delete_ppt_tmp", "PPT模版删除"),
        )

class PptModel(models.Model):
    """
    PPT表
    """
    title = models.CharField(max_length=255, verbose_name='标题', null=True)  # PPT标题
    description = models.TextField(verbose_name='描述', null=True, blank=True)  # PPT内容简要描述
    author = models.ForeignKey(
        "user.UserInfo", 
        on_delete=models.CASCADE, 
        verbose_name='归属人ID',
        null=True,
        blank=True
    )  # 关联到用户表的外键
    file = models.FileField(upload_to=ppt_path, verbose_name='文件')  # PPT文件
    ppt_json = models.JSONField(
        verbose_name='PPT内容JSON',
        null=True,
        blank=True
    )  # 存储PPT的JSON格式内容
    ppt_tmp_json = models.JSONField(
        verbose_name='PPT模板JSON',
        null=True,
        blank=True
    )  # 存储PPT模板的JSON格式内容
    is_ai_generated = models.BooleanField(default=False, verbose_name='AI生成', null=True, blank=True)  # 是否由AI生成
    view_count = models.IntegerField(default=0, verbose_name='查看次数', null=True, blank=True)  # 被查看的次数
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name='上传时间', null=True, blank=True)  # 上传时间
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间', null=True, blank=True)  # 最后更新时间
    template = models.ForeignKey(
        'PptTmpModel',  # 关联到PPT模板表的外键,
        on_delete=models.SET_NULL,  # 设置为null而不是级联删除
        verbose_name='使用模板ID',
        null=True,
        blank=True
    )  # 关联到PPT模板表的外键

    # chapter = models.ForeignKey(
    #     'course.Chapter',
    #     on_delete=models.SET_NULL,  # 级联删除
    #     verbose_name='章节ID',
    #     null=True,
    #     blank=True,
    #     related_name='ppts'  # 反向关联名称
    # )  # 关联到章节表的外键

    chapter = models.ManyToManyField(
        Chapter,
        related_name='ppts',
        verbose_name='关联章节',
        blank=True,
        null=True,
    )

    is_deleted = models.BooleanField(default=False, verbose_name='是否删除', null=True, blank=True)  # 软删除判断
    deleted_at = models.DateTimeField(verbose_name='删除时间', null=True, blank=True)  # 软删除时间

    class Meta:
        default_permissions = ()
        db_table = 'ppt_info'
        verbose_name = 'PPT表'
        verbose_name_plural = verbose_name
        permissions = (
            ("add_ppt_info", "PPT添加"),
            ("view_ppt_info", "PPT查询"),
            ("change_ppt_info", "PPT修改"),
            ("delete_ppt_info", "PPT删除"),
        )

