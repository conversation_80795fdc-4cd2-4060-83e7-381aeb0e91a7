from django.http import JsonResponse
from django.db import transaction
from django.core.files.base import ContentFile
from .models import (
    Course, CourseSemester, Chapter,
    KnowledgePoint
)
from ppt.models import PptModel
from teachplan.models import TeachPlanModel
from exam.models import BaseQuestion, QuestionKnowledgePoint


def api_response(code, message, data=None, status=200):
    """
    封装 API 返回格式
    :param code: 状态码（自定义业务状态码）
    :param message: 返回信息
    :param data: 返回数据
    :param status: HTTP 状态码
    :return: JsonResponse
    """
    response_data = {
        'code': code,
        'message': message,
        'data': data if data is not None else {}
    }
    return JsonResponse(response_data, status=status)


@transaction.atomic
def duplicate_course_with_relations(course_id, semester_id, new_course_name=None, new_semester_id=None):
    # 获取原始课程及相关数据
    """
    复制课程及相关数据
    :param course_id: 原始课程ID
    :param semester_id: 原始课程学期ID
    :param new_course_name: 新课程名称（可选）
    :param new_semester_id: 新课程学期ID（可选）
    :return: 新课程实例
    """
    original_course = Course.objects.get(id=course_id)  # 原始课程
    original_course_semester = CourseSemester.objects.get(course_id=course_id, semester_id=semester_id)  # 原始课程学期

    # 创建新课程实例
    new_course = Course(**{
        field.name: getattr(original_course, field.name)
        for field in original_course._meta.fields
        if field.name not in ['id', 'created_at', 'updated_at']
    })
    if new_course_name:
        # print()
        new_course.title = new_course_name
    new_course.save()

    # 复制课程学期
    new_semester = CourseSemester(**{
        field.name: getattr(original_course_semester, field.name)
        for field in original_course_semester._meta.fields
        if field.name not in ['id', 'course', 'semester_id']
    })
    new_semester.course = new_course
    new_semester.semester_id = new_semester_id
    new_semester.save()

    # 复制教师团队
    for original_teacher in original_course_semester.teachers.all():
        # print(original_teacher)
        new_semester.teachers.create(course_semester=new_semester,
                                     teacher=original_teacher.teacher,
                                     role=original_teacher.role,
                                     is_active=original_teacher.is_active
                                     )

    # 章节到新章节的映射，用于子章节和内容的引用
    chapter_mapping = {}

    # 教案到新教案的映射，避免重复复制
    teaching_plan_mapping = {}

    # PPT到新PPT的映射，避免重复复制
    ppt_plan_mapping = {}

    # 复制章节及其子内容（4级目录结构）
    def copy_chapter(old_chapter, parent_chapter=None):
        new_chapter = Chapter(**{
            field.name: getattr(old_chapter, field.name)
            for field in old_chapter._meta.fields
            if field.name not in ['id', 'course', 'parent']
        })
        new_chapter.course = new_course
        new_chapter.parent = parent_chapter
        new_chapter.save()

        # 记录章节映射
        chapter_mapping[old_chapter.id] = new_chapter

        # 复制PPT（多对多关系）
        for ppt in old_chapter.ppts.all():
            if ppt.id in ppt_plan_mapping:
                # 使用已复制的教案
                new_ppt = ppt_plan_mapping[ppt.id]
            else:
                # 创建新教案
                new_ppt = PptModel(**{
                    field.name: getattr(ppt, field.name)
                    for field in ppt._meta.fields
                    if field.name not in ['id', 'file', 'created_at', 'updated_at']
                })

                if ppt.file:
                    new_ppt.file.save(
                        f"copy_{ppt.file.name}",
                        ContentFile(ppt.file.read()),
                        save=False
                    )
                new_ppt.save()

                # 记录教案映射
                ppt_plan_mapping[ppt.id] = new_ppt

            # 建立新的多对多关系
            new_chapter.ppts.add(new_ppt)

        # 复制教案（多对多关系）
        for plan in old_chapter.teachplan.all():
            if plan.id in teaching_plan_mapping:
                # 使用已复制的教案
                new_plan = teaching_plan_mapping[plan.id]
            else:
                # 创建新教案
                new_plan = TeachPlanModel(**{
                    field.name: getattr(plan, field.name)
                    for field in plan._meta.fields
                    if field.name not in ['id', 'file', 'created_at', 'updated_at']
                })

                if plan.file:
                    new_plan.file.save(
                        f"copy_{plan.file.name}",
                        ContentFile(plan.file.read()),
                        save=False
                    )
                new_plan.save()

                # 记录教案映射
                teaching_plan_mapping[plan.id] = new_plan

            # 建立新的多对多关系
            new_chapter.teachplan.add(new_plan)

        # 复制知识点（4级目录）
        knowledge_point_mapping = {}
        for point in old_chapter.knowledge_points.all():
            new_point = KnowledgePoint(**{
                field.name: getattr(point, field.name)
                for field in point._meta.fields
                if field.name not in ['id', 'chapter', 'created_at', 'updated_at']
            })
            new_point.chapter = new_chapter
            new_point.save()

            # 记录知识点映射
            knowledge_point_mapping[point.id] = new_point

            # 复制题库（绑定知识点）
            copy_question_bank(point, new_point, knowledge_point_mapping)

        # 递归复制子章节
        for sub_chapter in old_chapter.children.all():
            copy_chapter(sub_chapter, new_chapter)

    # 处理多态题库的复制
    def copy_question_bank(old_point, new_point, knowledge_point_mapping):
        # 获取所有基础题目
        for question in old_point.question_knowledge_points.all():
            # 获取具体的子类实例（多态处理）
            # print(question)
            # print(question.__class__)
            # print(question.id)
            # concrete_question = base_question.as_subclass()

            # 创建新的基础题目实例
            new_base_question = BaseQuestion(**{
                field.name: getattr(question.basequestion_ptr, field.name)
                for field in question.basequestion_ptr._meta.fields
                if field.name not in ['id', 'created_at', 'updated_at', 'is_deleted', 'deleted_at']
            })
            new_base_question.save()

            # 创建新的子类题目实例
            new_sub_question_fields = {
                field.name: getattr(question, field.name)
                for field in question._meta.fields
                # if field.name not in ['id', 'polymorphic_ctype', 'knowledge_point']
            }

            # 旧知识点与题目关联信息
            old_question_knowledge_point = QuestionKnowledgePoint.objects.get(
                question=question.basequestion_ptr.id,
                knowledge_point=old_point
            )

            # 创建新的关联关系
            QuestionKnowledgePoint.objects.create(
                question=new_base_question,
                knowledge_point=new_point,
                relevance=old_question_knowledge_point.relevance,
                is_primary=old_question_knowledge_point.is_primary
            )

            # 设置基础题目的外键
            new_sub_question_fields['basequestion_ptr'] = new_base_question

    # 从根章节开始复制
    for root_chapter in original_course.chapters.filter(parent=None):
        copy_chapter(root_chapter)

    return new_course, new_semester
