"""
知识库数据同步检查逻辑
"""
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
import requests
from rest_framework.parsers import <PERSON>Part<PERSON><PERSON><PERSON>, FormParser, JSONParser
from rest_framework.decorators import api_view, parser_classes
import os

import re
import urllib.parse
from django.http import StreamingHttpResponse, HttpResponse
from django.utils.encoding import escape_uri_path
import sys
import json
from django.db import transaction
from datetime import datetime
from django.conf import settings
import traceback
from .models import ChunkQA
# sys.path.insert(0, os.path.abspath("/root/aieducation_backend/knowledge"))
# from pgdatabase import *
from .pgdatabase import (
    get_knowledge_base_by_id,
    #get_user_kb_links,
    get_kb_course_links,
    get_course_by_id,
    get_kb_courses_batch,
    add_knowledge_base,
    #add_user_kb_link,
    add_course_kb_link,
    update_knowledge_base,
    delete_knowledge_base,
    get_all_knowledge_bases,
    #link_user_kb,

    delete_all_kb_links,
    delete_all_kb_course_links,
    #delete_user_kb_link,
    delete_course_kb_link,
    delete_knowledge_base_cascade,
    get_all_active_knowledge_bases,
    batch_delete_knowledge_bases_cascade,
    get_user_knowledge_bases,
    get_user_kb_ids,
    get_course_kb_links
)
from .chunk_qa_utils import *
# BASE_URL = "http://192.168.5.249:30847"
# HEADERS = {
#     "Content-Type": "application/json",
#     "Authorization": "Bearer tianhe1-Y5YmFkNjhjNGZmNjExZjA4MDNiNGFkNW"
# }
from django.conf import settings
# 获取配置
from .views import *
'''----------知识库数据同步函数---------'''

'''
功能：同步RAG和PG知识库表中的数据
1. 比较RAG和PG知识库表中的数据，取交集，其余的删掉
'''
def sync_knowledge_bases_intersection():
    """
    功能1：比较RAG和PG知识库表中的数据，取交集，其余的删掉
    保留两边都存在的知识库，删除只在一边存在的知识库
    """
    print(f"[INFO] 开始执行知识库交集同步")

    sync_result = {
        "operation": "intersection_sync",
        "rag_total": 0,
        "pg_total": 0,
        "intersection_count": 0,
        "deleted_from_rag": 0,
        "deleted_from_pg": 0,
        "errors": [],
        "sync_time": datetime.now().isoformat()
    }

    try:
        # 1. 获取RAG中的所有知识库
        rag_datasets = _fetch_all_rag_datasets()
        rag_ids = {dataset["id"] for dataset in rag_datasets}
        sync_result["rag_total"] = len(rag_ids)

        print(f"[DEBUG] RAG知识库总数: {len(rag_ids)}")

        # 2. 获取PG中的所有知识库
        pg_datasets = _fetch_all_pg_datasets()
        pg_ids = {dataset.id for dataset in pg_datasets}
        sync_result["pg_total"] = len(pg_ids)

        print(f"[DEBUG] PG知识库总数: {len(pg_ids)}")

        # 3. 计算交集和差集
        intersection_ids = rag_ids & pg_ids  # 交集：两边都有的
        rag_only_ids = rag_ids - pg_ids  # RAG独有的
        pg_only_ids = pg_ids - rag_ids  # PG独有的

        sync_result["intersection_count"] = len(intersection_ids)

        print(f"[DEBUG] 交集数量: {len(intersection_ids)}")
        print(f"[DEBUG] RAG独有: {len(rag_only_ids)}")
        print(f"[DEBUG] PG独有: {len(pg_only_ids)}")

        # 4. 删除RAG中独有的知识库
        for kb_id in rag_only_ids:
            try:
                success = _delete_rag_dataset(kb_id)
                if success:
                    sync_result["deleted_from_rag"] += 1
                    print(f"[INFO] 从RAG删除知识库: {kb_id}")
                else:
                    sync_result["errors"].append(f"删除RAG知识库失败: {kb_id}")
            except Exception as e:
                error_msg = f"删除RAG知识库异常 {kb_id}: {str(e)}"
                print(f"[ERROR] {error_msg}")
                sync_result["errors"].append(error_msg)

        # 5. 删除PG中独有的知识库
        for kb_id in pg_only_ids:
            try:
                success = _delete_pg_dataset_cascade(kb_id)
                if success:
                    sync_result["deleted_from_pg"] += 1
                    print(f"[INFO] 从PG删除知识库: {kb_id}")
                else:
                    sync_result["errors"].append(f"删除PG知识库失败: {kb_id}")
            except Exception as e:
                error_msg = f"删除PG知识库异常 {kb_id}: {str(e)}"
                print(f"[ERROR] {error_msg}")
                sync_result["errors"].append(error_msg)

        print(f"[INFO] 交集同步完成 - 保留: {len(intersection_ids)}, "
              f"从RAG删除: {sync_result['deleted_from_rag']}, "
              f"从PG删除: {sync_result['deleted_from_pg']}")

        return sync_result

    except Exception as e:
        error_msg = f"交集同步过程异常: {str(e)}"
        print(f"[ERROR] {error_msg}")
        traceback.print_exc()
        sync_result["errors"].append(error_msg)
        return sync_result



def _fetch_all_rag_datasets():
    """获取RAG中的所有知识库"""
    try:
        all_datasets = []
        page = 1
        page_size = 100

        while True:
            resp = requests.get(
                f"{BASE_URL}/api/v1/datasets",
                headers=HEADERS,
                params={
                    "page": page,
                    "page_size": page_size
                },
                timeout=30
            )

            if resp.status_code == 200:
                data = resp.json()
                datasets = data.get("data", [])

                if not datasets:
                    break

                all_datasets.extend(datasets)
                print(f"[DEBUG] 获取第{page}页RAG数据，本页{len(datasets)}条")

                # 检查是否还有更多数据
                if len(datasets) < page_size:
                    break

                page += 1
            else:
                print(f"[ERROR] 获取RAG数据失败: {resp.status_code}")
                break

        print(f"[INFO] 共获取RAG知识库 {len(all_datasets)} 个")
        return all_datasets

    except Exception as e:
        print(f"[ERROR] 获取RAG数据异常: {e}")
        return []


def _fetch_all_pg_datasets():
    """获取PG中的所有活跃知识库"""
    try:
        # 假设有这个函数获取所有活跃的知识库
        datasets = get_all_active_knowledge_bases()
        print(f"[INFO] 共获取PG知识库 {len(datasets)} 个")
        return datasets
    except Exception as e:
        print(f"[ERROR] 获取PG数据异常: {e}")
        return []



def _delete_rag_dataset(kb_id):
    """删除RAG中的知识库 - 参考DatasetListCreateView的delete方法"""
    try:
        # 参考DatasetListCreateView中的delete方法，使用批量删除接口
        # RAG的删除接口需要传递ids数组格式
        delete_data = {"ids": [kb_id]}

        print(f"[DEBUG] 准备删除RAG知识库: {kb_id}")
        print(f"[DEBUG] 删除数据: {delete_data}")

        resp = requests.delete(
            f"{BASE_URL}/api/v1/datasets",  # 注意：这里是批量删除的端点，不是单个删除
            headers={**HEADERS, "Content-Type": "application/json"},
            json=delete_data,
            timeout=30
        )

        print(f"[DEBUG] RAG删除响应状态码: {resp.status_code}")
        print(f"[DEBUG] RAG删除响应内容: {resp.text}")

        if resp.status_code == 200:
            print(f"[INFO] 成功从RAG删除知识库: {kb_id}")

            # 可选：解析响应内容获取详细信息
            try:
                response_data = resp.json()
                print(f"[DEBUG] 删除响应详情: {response_data}")
            except json.JSONDecodeError:
                print(f"[WARN] 无法解析RAG删除响应JSON")

            return True
        else:
            print(f"[ERROR] 从RAG删除知识库失败: {kb_id}, 状态码: {resp.status_code}")
            print(f"[ERROR] 错误响应: {resp.text}")

            # 尝试解析错误信息
            try:
                error_data = resp.json()
                print(f"[ERROR] 错误详情: {error_data}")
            except json.JSONDecodeError:
                print(f"[ERROR] 无法解析错误响应JSON")

            return False

    except requests.exceptions.Timeout:
        print(f"[ERROR] 删除RAG知识库请求超时: {kb_id}")
        return False
    except requests.exceptions.ConnectionError:
        print(f"[ERROR] 无法连接到RAG服务删除知识库: {kb_id}")
        return False
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] 删除RAG知识库网络请求失败: {kb_id}, 错误: {e}")
        return False
    except Exception as e:
        print(f"[ERROR] 删除RAG知识库异常 {kb_id}: {e}")

        traceback.print_exc()
        return False


def _delete_pg_dataset_cascade(kb_id):
    """级联删除PG中的知识库及其关联"""
    try:
        # 删除用户-知识库关联
        deleted_user_links = delete_all_kb_links(kb_id)

        # 删除课程-知识库关联
        deleted_course_links = delete_all_kb_course_links(kb_id)

        # 软删除知识库本身
        kb_deleted = delete_knowledge_base(kb_id, force=False)

        if kb_deleted:
            print(
                f"[INFO] 成功从PG删除知识库: {kb_id} (用户关联:{deleted_user_links}, 课程关联:{deleted_course_links})")
            return True
        else:
            print(f"[ERROR] 从PG删除知识库失败: {kb_id}")
            return False

    except Exception as e:
        print(f"[ERROR] 删除PG知识库异常 {kb_id}: {e}")
        return False


def _compare_dataset_details(rag_data, pg_data):
    """详细比较RAG和PG中的知识库数据"""
    changes = []
    needs_update = False

    try:
        # 比较名称
        rag_name = rag_data.get("name", "").strip()
        pg_name = (pg_data.name or "").strip()
        if rag_name != pg_name:
            changes.append(f"名称: '{pg_name}' -> '{rag_name}'")
            needs_update = True

        # 比较描述
        rag_desc = rag_data.get("description", "").strip()
        pg_desc = (pg_data.description or "").strip()
        if rag_desc != pg_desc:
            changes.append(f"描述: '{pg_desc}' -> '{rag_desc}'")
            needs_update = True

        # 比较文档数量
        rag_doc_count = rag_data.get("document_count", 0)
        pg_doc_count = pg_data.document_count or 0
        if rag_doc_count != pg_doc_count:
            changes.append(f"文档数: {pg_doc_count} -> {rag_doc_count}")
            needs_update = True

        # 可以添加更多字段的比较
        # 比如更新时间、状态等

        return {
            "needs_update": needs_update,
            "changes": changes
        }

    except Exception as e:
        print(f"[ERROR] 比较数据异常: {e}")
        return {"needs_update": False, "changes": []}


def _update_pg_dataset_from_rag_data(kb_id, rag_data):
    """使用RAG数据更新PG知识库"""
    try:
        success = update_knowledge_base(
            kb_id=kb_id,
            name=rag_data.get("name"),
            description=rag_data.get("description"),
            document_count=rag_data.get("document_count", 0)
        )
        return success
    except Exception as e:
        print(f"[ERROR] 更新PG知识库异常 {kb_id}: {e}")
        return False


# def _create_pg_dataset_from_rag_data(rag_data):
#     """根据RAG数据在PG中创建知识库"""
#     try:
#         # 由于没有用户信息，这里需要特殊处理
#         # 可能需要设置默认用户或从created_by字段获取
#         default_user_id = 1  # 设置默认用户ID，或者从rag_data["created_by"]获取
#
#         success = add_knowledge_base(
#             kb_id=rag_data["id"],
#             name=rag_data.get("name", ""),
#             description=rag_data.get("description", ""),
#             document_count=rag_data.get("document_count", 0),
#             user_id=default_user_id
#         )
#
#         return success
#     except Exception as e:
#         print(f"[ERROR] 创建PG知识库异常 {rag_data.get('id', 'unknown')}: {e}")
#         return False


def sync_knowledge_bases_rag_to_pg():
    """
    功能2：比较RAG和PG知识库表中的数据，不一致的且RAG也有的字段，用PG的数据覆盖RAG知识库的字段
    PG作为主数据源，更新RAG中不一致的数据
    """
    print(f"[INFO] 开始执行PG到RAG的数据同步（PG覆盖RAG）")

    sync_result = {
        "operation": "pg_to_rag_sync",
        "rag_total": 0,
        "pg_total": 0,
        "updated_datasets": 0,
        "created_datasets": 0,
        "comparison_details": [],
        "errors": [],
        "sync_time": datetime.now().isoformat()
    }

    try:
        # 1. 获取RAG中的所有知识库
        rag_datasets = _fetch_all_rag_datasets()
        rag_dict = {dataset["id"]: dataset for dataset in rag_datasets}
        sync_result["rag_total"] = len(rag_dict)

        # 2. 获取PG中的所有知识库
        pg_datasets = _fetch_all_pg_datasets()
        pg_dict = {dataset.id: dataset for dataset in pg_datasets}
        sync_result["pg_total"] = len(pg_dict)

        print(f"[DEBUG] 准备比较 {len(rag_dict)} 个RAG知识库和 {len(pg_dict)} 个PG知识库")

        # 3. 遍历RAG中的每个知识库
        for kb_id, rag_data in rag_dict.items():
            try:
                if kb_id in pg_dict:
                    # PG中存在，检查是否需要更新RAG
                    pg_data = pg_dict[kb_id]
                    comparison = _compare_dataset_details_pg_priority(rag_data, pg_data)

                    if comparison["needs_update"]:
                        # 需要用PG数据更新RAG
                        success = _update_rag_dataset_from_pg_data(kb_id, pg_data, rag_data)
                        if success:
                            sync_result["updated_datasets"] += 1
                            print(f"[INFO] 用PG数据更新RAG知识库: {kb_id}")
                            sync_result["comparison_details"].append({
                                "kb_id": kb_id,
                                "action": "updated",
                                "changes": comparison["changes"]
                            })
                        else:
                            sync_result["errors"].append(f"更新RAG知识库失败: {kb_id}")
                    else:
                        print(f"[DEBUG] 知识库 {kb_id} 数据一致，无需更新")
                else:
                    # PG中不存在，创建新的PG记录
                    success = _create_pg_dataset_from_rag_data(rag_data)
                    if success:
                        sync_result["created_datasets"] += 1
                        print(f"[INFO] 在PG中创建知识库: {kb_id}")
                        sync_result["comparison_details"].append({
                            "kb_id": kb_id,
                            "action": "created",
                            "changes": ["新建知识库"]
                        })
                    else:
                        sync_result["errors"].append(f"在PG中创建知识库失败: {kb_id}")

            except Exception as e:
                error_msg = f"处理知识库 {kb_id} 时异常: {str(e)}"
                print(f"[ERROR] {error_msg}")
                sync_result["errors"].append(error_msg)

        print(f"[INFO] PG到RAG同步完成 - 更新: {sync_result['updated_datasets']}, "
              f"创建: {sync_result['created_datasets']}")

        return sync_result

    except Exception as e:
        error_msg = f"PG到RAG同步过程异常: {str(e)}"
        print(f"[ERROR] {error_msg}")

        traceback.print_exc()
        sync_result["errors"].append(error_msg)
        return sync_result


def _compare_dataset_details_pg_priority(rag_data, pg_data):
    """详细比较RAG和PG中的知识库数据 - PG优先"""
    changes = []
    needs_update = False

    try:
        # 比较名称 - 如果PG有名称且与RAG不同，则需要更新
        rag_name = rag_data.get("name", "").strip()
        pg_name = (pg_data.name or "").strip()
        if pg_name and rag_name != pg_name:
            changes.append(f"名称: RAG '{rag_name}' -> PG '{pg_name}'")
            needs_update = True

        # 比较描述 - 如果PG有描述且与RAG不同，则需要更新
        rag_desc = rag_data.get("description", "").strip()
        pg_desc = (pg_data.description or "").strip()
        if pg_desc and rag_desc != pg_desc:
            changes.append(f"描述: RAG '{rag_desc}' -> PG '{pg_desc}'")
            needs_update = True

        # 比较文档数量 - 如果PG有文档数且与RAG不同，则需要更新
        rag_doc_count = rag_data.get("document_count", 0)
        pg_doc_count = pg_data.document_count or 0
        if pg_doc_count >= 0 and rag_doc_count != pg_doc_count:  # PG文档数大于等于0才更新
            changes.append(f"文档数: RAG {rag_doc_count} -> PG {pg_doc_count}")
            needs_update = True

        return {
            "needs_update": needs_update,
            "changes": changes
        }

    except Exception as e:
        print(f"[ERROR] 比较数据异常: {e}")
        return {"needs_update": False, "changes": []}


def _update_rag_dataset_from_pg_data(kb_id, pg_data, rag_data):
    """使用PG数据更新RAG知识库"""
    try:
        # 构建更新数据，只更新PG中有值的字段
        update_data = {}

        # 名称
        if pg_data.name:
            update_data["name"] = pg_data.name

        # 描述
        if pg_data.description:
            update_data["description"] = pg_data.description

        # 其他RAG原有的字段保持不变
        # 保留RAG中的其他字段
        for key in ["avatar", "chunk_method", "language", "embedding_model",
                    "similarity_threshold", "vector_similarity_weight", "parser_config"]:
            if key in rag_data:
                update_data[key] = rag_data[key]

        print(f"[DEBUG] 准备用PG数据更新RAG知识库 {kb_id}: {update_data}")

        # 调用RAG API更新
        resp = requests.put(
            f"{BASE_URL}/api/v1/datasets/{kb_id}",
            headers=HEADERS,
            json=update_data,
            timeout=30
        )

        if resp.status_code == 200:
            print(f"[INFO] 成功用PG数据更新RAG知识库: {kb_id}")
            return True
        else:
            print(f"[ERROR] 用PG数据更新RAG知识库失败: {kb_id}, 状态码: {resp.status_code}")
            print(f"[ERROR] 响应内容: {resp.text}")
            return False

    except Exception as e:
        print(f"[ERROR] 更新RAG知识库异常 {kb_id}: {e}")

        traceback.print_exc()
        return False


def _create_pg_dataset_from_rag_data(rag_data):
    """根据RAG数据在PG中创建知识库"""
    try:
        # 由于没有用户信息，这里需要特殊处理
        # 可能需要设置默认用户或从created_by字段获取
        default_user_id = 1  # 设置默认用户ID，或者从rag_data["created_by"]获取

        success = add_knowledge_base(  # 修正：使用正确的函数名
            kb_id=rag_data["id"],
            name=rag_data.get("name", ""),
            description=rag_data.get("description", ""),
            document_count=rag_data.get("document_count", 0),
            user_id=default_user_id
        )

        return success
    except Exception as e:
        print(f"[ERROR] 创建PG知识库异常 {rag_data.get('id', 'unknown')}: {e}")
        return False


# 同时修改API接口的描述
@api_view(['POST'])
def sync_datasets_rag_to_pg_api(request):
    """执行PG到RAG数据同步的API接口（PG覆盖RAG）"""
    try:
        user = get_test_user(request)
        print(f"[INFO] 用户 {user.username} 触发PG到RAG同步")

        result = sync_knowledge_bases_rag_to_pg()

        return sucess_api_response({
            "message": "PG到RAG同步完成",
            "sync_result": result
        })

    except Exception as e:
        print(f"[ERROR] PG到RAG同步API调用失败: {e}")

        traceback.print_exc()
        return fail_api_response(
            {"error": "PG到RAG同步失败", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
# API接口
@api_view(['POST'])
def sync_datasets_intersection_api(request):
    """执行知识库交集同步的API接口"""
    try:
        user = get_test_user(request)
        print(f"[INFO] 用户 {user.username} 触发知识库交集同步")

        result = sync_knowledge_bases_intersection()

        return sucess_api_response({
            "message": "知识库交集同步完成",
            "sync_result": result
        })

    except Exception as e:
        print(f"[ERROR] 交集同步API调用失败: {e}")
        traceback.print_exc()
        return fail_api_response(
            {"error": "交集同步失败", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
def sync_datasets_comprehensive_api(request):
    """执行综合同步：先做交集同步，再做数据更新同步"""
    try:
        user = get_test_user(request)
        print(f"[INFO] 用户 {user.username} 触发知识库综合同步")

        # 先执行交集同步
        intersection_result = sync_knowledge_bases_intersection()

        # 再执行数据更新同步
        update_result = sync_knowledge_bases_rag_to_pg()

        return sucess_api_response({
            "message": "知识库综合同步完成",
            "intersection_sync": intersection_result,
            "update_sync": update_result
        })

    except Exception as e:
        print(f"[ERROR] 综合同步API调用失败: {e}")
        traceback.print_exc()
        return fail_api_response(
            {"error": "综合同步失败", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

'''-----------------知识库数据同步函数结束-----------------'''