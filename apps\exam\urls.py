from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from .views import *

router = DefaultRouter()
router.register('single_question', SingleChoiceQuestionViewSet, basename='single_question')
router.register('multi_question', MultipleChoiceQuestionViewSet, basename='multi_question')
router.register('tf_question', TrueOrFalseQuestionViewSet, basename='tf_question')
router.register('fill_question', FillInBlankQuestionViewSet, basename='fill_question')
router.register('qa_question', QuestionAndAnswerQuestionViewSet, basename='qa_question')
router.register('question', QuestionListViewSet, basename='question')
router.register('ai_question', AIQuestionViewSet, basename='ai_question')
router.register('paper', PaperViewSet, basename='paper')
router.register('exam', KnowledgeExamViewSet, basename='exam')
router.register('take_part_exam', UserTakePartExamViewSet, basename='take_part_exam')
router.register('feedback', UserExamQuestionFeedbackViewSet, basename='feedback')

urlpatterns = [
    path('', include(router.urls)),
]