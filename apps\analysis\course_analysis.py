"""
课程 AI 学情分析服务
"""

import json
import re

from django.conf import settings
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END, START
from typing import Any, Dict, List, TypedDict
from .models import *


class AnalysisState(TypedDict):
    """考试/作业学情分析流程状态结构"""

    user_name: str  # 学生姓名
    user_number: str  # 学生学号
    course_name: str  # 课程名称
    abstract_combined_exam: str  # 课程考试摘要
    abstract_combined_homework: str  # 课程作业摘要
    course_comment: str  # 每位学生的课程评价
    exam_grade: str  # 每位学生的课程成绩分析
    homework_situation: str  # 每位学生的作业情况分析
    abstract_course: str  # 每位学生的课程分析摘要
    analysis_status: str  # 每位学生的课程学习状态

    learn_situation_summary: str  # 每位学生的课程学习进度情况汇总
    learn_progress: str  # 每位学生的课程学习进度分析


class AICourseAnalysisService:
    """考试/作业AI学情分析服务"""

    _instance = None

    def __new__(cls):
        """
        单例模式实现
        """
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.llm = ChatOpenAI(
                model=settings.LLM_NAME,
                openai_api_key=settings.LLM_API_KEY,
                openai_api_base=settings.LLM_BASE_URL_LESSON,
            )

        return cls._instance

    # -------------------- 核心 AI 服务方法 --------------------
    def analyze_course_exam(self, state):
        """
        考试/作业学情分析-主入口
        """
        #  考试/作业学情分析
        initial_state = AnalysisState(
            user_name=state.get("user_name", ""),
            user_number=state.get("user_number", ""),
            course_name=state.get("course_name", ""),
            abstract_combined_exam=state.get("abstract_combined_exam", ""),
            abstract_combined_homework=state.get("abstract_combined_homework", ""),
            learn_situation_summary=state.get("learn_situation_summary", ""),

            exam_grade="",
            homework_situation="",
            course_comment="",
            analysis_status="",
            abstract_course="",
            learn_progress=""
        )

        workflow = self.build_analyzing_flow()
        final_state = workflow.invoke(initial_state)

        return {
            "exam_grade": final_state["exam_grade"],
            "homework_situation": final_state["homework_situation"],
            "course_comment": final_state["course_comment"],
            "analysis_status": final_state["analysis_status"],
            "abstract_course": final_state["abstract_course"],
            "learn_progress": final_state["learn_progress"]
        }

    def build_analyzing_flow(self):
        """
        构建学情分析流程图
        """
        builder = StateGraph(AnalysisState)

        # 添加节点
        builder.add_node("JudgeInput", self._judge_input)
        builder.add_node("ExamHomeworkAnalysis", self._homework_exam_analysis)
        builder.add_node("CommentAnalysis", self._comment_analysis)
        builder.add_node("AbstractCourse", self._abstract_course)
        builder.add_node("StatusAnalysis", self._status_analysis)
        builder.add_node("LearnProgress", self._learn_progress)
        builder.add_node("OnlyLearnProgress", self._only_learn_progress)

        # 设置初始流程路径
        builder.add_edge(START, "JudgeInput")

        # 根据判断结果设置条件分支
        builder.add_conditional_edges(
            "JudgeInput",
            # 条件判断函数
            lambda state: state.get("next_step", END),
            # 可能得下一节点映射
            {
                "ExamHomeworkAnalysis": "ExamHomeworkAnalysis",
                "OnlyLearnProgress": "OnlyLearnProgress",
                END: END
            }
        )

        # 设置常规流程路径
        builder.add_edge("ExamHomeworkAnalysis", "CommentAnalysis")
        builder.add_edge("CommentAnalysis", "AbstractCourse")
        builder.add_edge("AbstractCourse", "StatusAnalysis")
        builder.add_edge("StatusAnalysis", "LearnProgress")
        builder.add_edge("LearnProgress", END)

        # 仅学习进度分析路径
        builder.add_edge("OnlyLearnProgress", END)

        return builder.compile()

    # -------------------- 流程节点 --------------------
    def _judge_input(self, state):
        """判断流程节点 对流程进行分支操作"""
        # 检查是否有足够的信息进行分析
        has_exam_data = bool(state.get('abstract_combined_exam', '').strip())
        has_homework_data = bool(state.get('abstract_combined_homework', '').strip())
        has_learn_progress_data = bool(state.get('learn_situation_summary', '').strip())

        # 决定下一步的流程
        if has_learn_progress_data:
            if has_exam_data or has_homework_data:
                # 有学习进度和考试/作业数据 - 走完整流程
                return {**state, "next_step": "ExamHomeworkAnalysis"}
            else:
                # 只有学习进度 - 仅分析学习进度
                return {**state, "next_step": "OnlyLearnProgress"}
        elif has_exam_data or has_homework_data:
            # 只有考试/作业数据 - 走常规分析流程
            return {**state, "next_step": "ExamHomeworkAnalysis"}
        else:
            # 没有足够数据 - 结束流程
            return {**state, "next_step": END, "error": "没有提供足够的分析数据"}
        
    def _only_learn_progress(self, state):
        """仅分析学习进度流程节点"""
        prompt = f"""
        /no_think
        你是一个专业的学情分析助手，根据学生的学习进度汇总，对学生的学习进度进行分析。
        请根据以下信息，分析学生的课程学习进度情况：
        1. 学生姓名：{state['user_name']}
        2. 课程名称：{state['course_name']}
        3. 学生课程学习进度汇总：{state['learn_situation_summary']}
        请返回JSON格式的分析结果，包含以下字段：
        {{
        "learn_progress": "详细的学习进度分析",
        "abstract_course": "基于学习进度的课程摘要分析",
        "course_comment": "基于学习进度的课程评语",
        "analysis_status": "基于学习进度的分析状态（优秀/良好/一般/较差/不合格）"
        }}
        
        """
        try:
            response = self._call_ai(prompt)
            data = self._extract_json(response)
            return {
                **state,
                "learn_progress": data.get("learn_progress", "学习进度分析：数据不足"),
                "abstract_course": data.get("abstract_course", "基于学习进度的课程摘要：数据不足"),
                "course_comment": data.get("course_comment", "基于学习进度的课程评语：数据不足"),
                "analysis_status": data.get("analysis_status", "一般")
            }
        except Exception as e:
            return {
                **state,
                "learn_progress": "学习进度分析失败",
                "abstract_course": "课程摘要分析失败",
                "course_comment": "课程评语分析失败",
                "analysis_status": "一般"
            }

    def _homework_exam_analysis(self, state):
        """考试/作业分析流程节点"""
        prompt = f"""
            /no_think
            你是一个专业的学情分析助手，根据学生的考试和作业情况，分析学生的考试和作业情况。
            请根据以下信息，分析学生的考试和作业情况：
            1. 学生姓名：{state['user_name']}
            2. 学生学号：{state['user_number']}
            3. 课程名称：{state['course_name']}
            4. 考试内容摘要：{state['abstract_combined_exam']}
            5. 作业内容摘要：{state['abstract_combined_homework']}
            请根据以上信息，分析学生的作业情况和考试成绩情况。
            请返回JSON格式的分析结果
            {{
            "exam_grade": "考试成绩分析结果",
            "homework_situation": "作业情况分析结果"
            }}
            """
        try:
            response = self._call_ai(prompt)
            data = self._extract_json(response)
            return {
                **state,
                "exam_grade": data["exam_grade"],
                "homework_situation": data["homework_situation"],
            }
        except Exception as e:
            return  {
                **state,
                "exam_grade": "考试成绩分析：继续加油",
                "homework_situation": "作业情况分析：继续加油"
            }

    def _comment_analysis(self, state):
        """评论分析流程节点"""

        prompt = f"""
            /no_think
            你是一个专业的课程评论分析助手，根据学生的课程表现情况，分析学生的课程情况，并得到课程评价。
            请根据以下信息，分析学生的课程评论：
            1. 学生姓名：{state['user_name']}
            2. 学生学号：{state['user_number']}
            3. 课程名称：{state['course_name']}
            4. 考试成绩分析：{state['exam_grade']}
            5. 作业情况分析：{state['homework_situation']}
            请根据以上信息，分析学生的课程评论。
            请返回JSON格式的分析结果
            {{
            "course_comment": "课程评论分析结果"
            }}
            """
        try:
            response = self._call_ai(prompt)
            data = self._extract_json(response)
            return {
                **state,
                "course_comment": data["course_comment"],
            }
        except Exception as e:
            return  {
                **state,
                "course_comment": "课程评语分析：整体表现良好，继续加油"
            }

    def _abstract_course(self, state):
        """课程摘要分析流程节点"""
        prompt = f"""
            /no_think
            你是一个专业的课程摘要分析助手，根据课程评价、考试成绩分析、作业情况分析，分析学生的课程情况。
            请根据以下信息，分析学生的课程情况，并得到课程分析摘要：
            1. 学生姓名：{state['user_name']}
            2. 学生学号：{state['user_number']}
            3. 课程名称：{state['course_name']}
            4. 考试成绩分析：{state['exam_grade']}
            5. 作业情况分析：{state['homework_situation']}
            6. 课程评论内容：{state['course_comment']}
            请根据以上信息，分析学生的课程评论。
            请返回JSON格式的分析结果
            {{
            "abstract_course": "课程摘要分析结果"
            }}
            """
        try:
            response = self._call_ai(prompt)
            data = self._extract_json(response)
            return {
                **state,
                "abstract_course": data["abstract_course"],
            }
        except Exception as e:
            return  {
                **state,
                "abstract_course": "课程摘要分析结果：整体一般"
            }

    def _status_analysis(self, state):
        """分析状态流程节点"""
        prompt = f"""
            /no_think
            你是一个专业的课程分析状态助手，根据课程分析摘要，分析学生的课程学习状态。
            请根据以下信息，分析学生的课程分析状态：
            1. 学生姓名：{state['user_name']}
            2. 学生学号：{state['user_number']}
            3. 课程名称：{state['course_name']}
            4. 课程分析摘要：{state['abstract_course']}
            请根据以上信息，分析学生的课程分析状态。
            请返回JSON格式的分析结果
            {{
            "analysis_status": "课程分析状态分析结果，可选结果：优秀、良好、一般、较差、不合格五个等级"
            }}
            """
        try:
            response = self._call_ai(prompt)
            data = self._extract_json(response)
            return {
                **state,
                "analysis_status": data["analysis_status"],
            }
        except Exception as e:
            return {
                **state,
                "analysis_status": "良好",
            }
        
    def _learn_progress(self, state):
        """分析学习进度流程节点"""
        prompt = f"""
        /no_think
        你是一个专业的课程学习进度分析助手，根据课程分析摘要、课程评语、学习进度汇总，对学生的学习进度进行分析。
        请根据以下信息，分析学生的课程学习进度情况：
        1. 学生姓名：{state['user_name']}
        2. 课程名称：{state['course_name']}
        3. 课程分析摘要：{state['abstract_course']}
        4. 课程评语：{state['course_comment']}
        5. 学生课程学习进度汇总：{state['learn_situation_summary']}
        请根据以上信息，分析学生的学习进度情况。
        请返回JSON格式的分析结果
        {{
        "learn_progress": "学生的学习进度分析",
        "abstract_course": "结合学生的学习进度情况以及之前的课程摘要，重新分析后的课程摘要"
        "course_comment": "结合学生的学习进度情况以及之前的课程评语，重新分析后的课程评语"
        }}
        """
        try:
            response = self._call_ai(prompt)
            data = self._extract_json(response)
            return {
                **state,
                "learn_progress": data["learn_progress"]
            }
        except Exception as e:
            return {
                **state,
                "learn_progress": "学习进度分析：继续加油"
            }


    def _call_ai(self, prompt):
        """调用AI的统一接口"""
        try:
            response = self.llm.invoke(prompt)
            return response.content
        except Exception as e:
            return json.dumps(
                {
                    "error": f"调用AI接口失败: {str(e)}",
                }
            )

    def _extract_json(self, text):
        """从文本提取JSON内容"""
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            match = re.search(r"\{.*\}", text, re.DOTALL)
            if match:
                return json.loads(match.group())
            raise ValueError("无法解析JSON响应")
