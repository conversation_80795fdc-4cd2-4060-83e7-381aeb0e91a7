from gremlin_python.process.traversal import Cardinality
from gremlin_python.process.graph_traversal import GraphTraversalSource
from gremlin_python.process.strategies import SubgraphStrategy
from gremlin_python.process.traversal import T, Direction
from langchain.text_splitter import RecursiveCharacterTextSplitter
from PyPDF2 import PdfReader
from docx import Document
import logging
import os
import re
import json
from typing import List, Dict, Any, Optional
import textwrap
import asyncio
# import magic
import mimetypes
from celery import Celery
from django.http import JsonResponse
# from .models import Task, status
# Define and configure the Celery app

logger = logging.getLogger(__name__)

def api_response(code, message, data=None, status=200):
    """
    封装 API 返回格式
    :param code: 状态码（自定义业务状态码）
    :param message: 返回信息
    :param data: 返回数据
    :param status: HTTP 状态码
    :return: JsonResponse
    """
    response_data = {
        'code': code,
        'message': message,
        'data': data if data is not None else {}
    }
    return JsonResponse(response_data, status=status)

def split_text(text, max_length=500):
    """
    将文本按 max_length 分块，按自然语言断句切分
    """
    return [text[i:i + max_length] for i in range(0, len(text), max_length)]


def split_text_into_chunks(text: str, chunk_size: int = 500, overlap: int = 100) -> list[str]:
    """
    将文本按指定大小分块，支持块之间的重叠

    Args:
        text: 要分块的文本
        chunk_size: 每块的最大字数
        overlap: 块之间的重叠字数

    Returns:
        分块后的文本列表
    """
    if not text:
        return []

    # 简单的基于句号、问号和感叹号的分割策略
    sentences = re.split(r'([。？！])', text)
    # 重新组合句子和标点符号
    sentences = [s + p for s, p in zip(sentences[0::2], sentences[1::2])] + sentences[len(sentences)::2]

    chunks = []
    current_chunk = ""

    for sentence in sentences:
        if len(current_chunk) + len(sentence) <= chunk_size:
            current_chunk += sentence
        else:
            # 如果当前句子无法加入当前块，则创建新块
            chunks.append(current_chunk)

            # 计算重叠部分
            overlap_text = current_chunk[-overlap:] if overlap > 0 else ""
            current_chunk = overlap_text + sentence

    # 添加最后一个块
    if current_chunk:
        chunks.append(current_chunk)

    return chunks

def text_into_chunks(text, chunk_size=500, chunk_overlap=50):
    """
    使用 langchain 的文本切分器分割长文本
    :param text: 原始文本
    :param chunk_size: 每个 chunk 的最大字符数
    :param chunk_overlap: chunk 之间的重叠字符数
    :return: 分割后的文本块列表
    """
    splitter = RecursiveCharacterTextSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        separators=["\n\n", "\n", "。", "！", "？", "；", ".", "!", "?", ";", " "]
    )
    return splitter.split_text(text)


def remove_duplicate_nodes(nodes: List[Dict[str, Any]]) -> (List[Dict[str, Any]], Dict[str, str]):
    """
    去重并合并相似的节点，同时返回合并映射

    Args:
        nodes: 节点列表

    Returns:
        去重后的节点列表和原始节点到合并后节点的映射
    """
    # 用于存储已处理节点的字典，键为节点标识，值为节点索引
    processed_nodes = {}
    # 去重后的节点列表
    unique_nodes = []
    # 原始节点到合并后节点的映射 (原始标识 -> 合并后标识)
    merge_map = {}
    # 记录无效节点
    invalid_nodes = []

    for node in nodes:
        # 验证节点结构完整性
        if not isinstance(node, dict):
            invalid_nodes.append(node)
            continue

        # 检查必要字段
        label = node.get('label')
        properties = node.get('properties')

        if label is None:
            invalid_nodes.append(node)
            continue

        # 处理缺少 properties 字段的情况
        if properties is None:
            # 尝试从顶层提取 name 和 content
            name = node.get('name')
            content = node.get('content', '')

            if name is None:
                invalid_nodes.append(node)
                continue

            # 创建 properties 字段
            properties = {'name': name, 'content': content}
            node['properties'] = properties
        else:
            # 验证 properties 中的必要字段
            if not isinstance(properties, dict) or 'name' not in properties:
                invalid_nodes.append(node)
                continue

        # 创建节点标识（标签+名称）
        original_key = f"{label}_{properties['name']}"

        # 标准化节点内容，去除多余空格和特殊字符
        content = properties.get('content', '').strip()
        content = re.sub(r'\s+', ' ', content)

        # 如果节点已存在，合并内容
        if original_key in processed_nodes:
            existing_index = processed_nodes[original_key]
            existing_content = unique_nodes[existing_index]['properties'].get('content', '')

            # 合并内容，避免重复
            if content and content not in existing_content:
                unique_nodes[existing_index]['properties']['content'] = f"{existing_content}；{content}"

            # 更新映射关系
            merge_map[original_key] = original_key  # 同名节点映射到自身
        else:
            # 添加新节点
            processed_nodes[original_key] = len(unique_nodes)

            # 使用标准化后的内容
            if content:
                properties['content'] = content

            unique_nodes.append(node)
            merge_map[original_key] = original_key  # 新节点映射到自身

    # 打印无效节点信息（可选）
    if invalid_nodes:
        logger.warning(f"发现 {len(invalid_nodes)} 个无效节点，已跳过处理")
        logger.debug(f"无效节点详情: {invalid_nodes}")

    return unique_nodes, merge_map


def remove_duplicate_edges(edges: List[Dict[str, Any]], node_map: Dict[str, str]) -> List[Dict[str, Any]]:
    """
    去重边，并使用节点映射更新边的引用

    Args:
        edges: 边列表
        node_map: 节点标识映射 (原始标识 -> 合并后标识)

    Returns:
        去重后的边列表
    """
    # 用于存储已处理边的集合，元素为边的标识元组
    processed_edges = set()
    # 去重后的边列表
    unique_edges = []

    for edge in edges:
        # 获取原始节点标识
        from_label = edge.get('from_label', '')
        to_label = edge.get('to_label', '')

        # 处理多目标实体（如 "孙权, 刘备"）
        from_entity = edge['from_entity']
        to_entities = [e.strip() for e in edge['to_entity'].split(',')] if isinstance(edge['to_entity'], str) else [
            edge['to_entity']]

        # 为每个目标实体创建单独的边
        for to_entity in to_entities:
            if not to_entity:  # 跳过空的目标实体
                continue

            # 创建原始节点标识
            from_key = f"{from_label}_{from_entity}" if from_label else from_entity
            to_key = f"{to_label}_{to_entity}" if to_label else to_entity

            # 映射到合并后的节点标识
            merged_from_key = node_map.get(from_key, from_key)
            merged_to_key = node_map.get(to_key, to_key)

            # 提取合并后的实体名称（去掉标签前缀）
            merged_from_entity = merged_from_key.split('_')[-1]
            merged_to_entity = merged_to_key.split('_')[-1]

            # 如果边不存在，则添加
            edge_key = (merged_from_entity, merged_to_entity)
            if edge_key not in processed_edges:
                processed_edges.add(edge_key)
                # 创建新边（处理多目标实体的情况）
                new_edge = {
                    'label': edge['label'],
                    'from_entity': merged_from_entity,
                    'to_entity': merged_to_entity,
                    'properties': edge['properties'].copy()
                }
                unique_edges.append(new_edge)

    return unique_edges


def merge_nodes_and_edges(nodes: List[Dict[str, Any]], edges: List[Dict[str, Any]]) -> (List[Dict[str, Any]],
                                                                                        List[Dict[str, Any]]):
    """
    整合节点和边的去重处理

    Args:
        nodes: 原始节点列表
        edges: 原始边列表

    Returns:
        去重后的节点和边列表
    """
    # 去重节点并获取合并映射
    unique_nodes, merge_map = remove_duplicate_nodes(nodes)

    # 去重边，使用合并映射更新边的引用
    unique_edges = remove_duplicate_edges(edges, merge_map)

    # 构建节点名称到ID的映射（用于验证边的有效性）
    node_names = {node['properties']['name'] for node in unique_nodes}

    # 过滤无效边（连接到不存在节点的边）
    valid_edges = []
    for edge in unique_edges:
        from_entity = edge['from_entity']
        to_entity = edge['to_entity']

        if from_entity in node_names and to_entity in node_names:
            valid_edges.append(edge)

    return unique_nodes, valid_edges

def upload_file(document):
    doc = Document(document)
    # full_text = '\n'.join([para.text for para in doc.paragraphs])
    structured_content = []
    # 标题级别和对应的文本内容
    heading_levels = {
        'Heading 1': 1, '标题 1': 1,
        'Heading 2': 2, '标题 2': 2,
        'Heading 3': 3, '标题 3': 3,
        'Heading 4': 4, '标题 4': 4,
    }
    for para in doc.paragraphs:
        text = para.text.strip()
        if not text: # 跳过空行
            continue
        # 获取段落样式名称
        style_name = para.style.name if para.style else None
        # 检查是否为标题
        if style_name and style_name in heading_levels:
            level = heading_levels[style_name]
            structured_content.append({
                'type': str(level) + '级标题',
                'text': text
            })
        else: # 正文
            structured_content.append({
                'type': '正文',
                'text': text
            })
    # 将结构化内容转换为Markdown格式的字符串
    if structured_content:
        document_content = "\n\n".join([
            f"# {item['text']}" if item['type'] == '1级标题' else
            f"## {item['text']}" if item['type'] == '2级标题' else
            f"### {item['text']}" if item['type'] == '3级标题' else
            f"#### {item['text']}" if item['type'] == '4级标题' else
            f"{item['text']}"
            for item in structured_content
        ])
    return document_content

def document_parsing(file):
    """
    文档解析（txt、pdf、docx）
    """
    # 根据文件后缀名指定解析方式
    if file.name.endswith('.pdf'):
        content, flag = pdf_analysis(file)
        if flag == 0:
            return api_response(400, 'pdf解析失败')
    elif file.name.endswith('.docx'):
        content, flag = docx_analysis(file)
    elif file.name.endswith('.txt'):
        content, flag = txt_analysis(file)
    else:
        return api_response(400, '文件格式不支持')

    if flag == 0:
        return api_response(400, '文件解析失败')
    else:
        data = {
            'filename': file.name,
            'size': file.size,  # 文件大小（字节）
            'content_preview': content,
            'total_length': len(content),
            'success': True
         }
        return data

def pdf_analysis(file):
    """
    pdf解析工具, 用于解析文档中的信息
    :param file: api接收到的文件对象
    :return: 解析后的信息
    """
    try:
        reader = PdfReader(file)
        text = ""

        # 逐页提取文本
        for page in reader.pages:
            text += page.extract_text() or ""
        return text, 1
    except Exception as e:
        # return JsonResponse({'error': f'Failed to parse PDF: {str(e)}'}, status=400)
        return str(e), 0

def txt_analysis(file):
    """
    txt解析工具, 用于解析文档中的信息
    :param file: api接收到的文件对象
    :return: 解析后的信息
    """
    try:
        # 读取文件内容
        content = file.read().decode('utf-8')
        return content, 1
    except Exception as e:
        return str(e), 0


# def parse_llm_response(raw_response):
#     """解析LLM返回的包含Markdown代码块的响应"""
#     try:
#         # 提取JSON部分
#         start_idx = raw_response.find("```json")
#         end_idx = raw_response.rfind("```")
#
#         if start_idx >= 0 and end_idx > start_idx:
#             # 提取JSON内容并去除前后空格
#             json_str = raw_response[start_idx + 7:end_idx].strip()
#
#             # 解析JSON
#             graph_data = json.loads(json_str)
#
#             # 验证数据结构
#             if not isinstance(graph_data, dict):
#                 raise ValueError("返回的数据不是有效的JSON对象")
#
#             nodes = graph_data.get("nodes", [])
#             edges = graph_data.get("edges", [])
#
#             # 进一步验证节点和边的结构
#             if not isinstance(nodes, list) or not isinstance(edges, list):
#                 raise ValueError("节点或边的格式不正确")
#
#             return {
#                 "status": "success",
#                 "data": {
#                     "nodes": nodes,
#                     "edges": edges
#                 }
#             }
#         else:
#             # 没有找到JSON代码块，尝试直接解析整个响应
#             try:
#                 graph_data = json.loads(raw_response.strip())
#                 return {
#                     "status": "success",
#                     "data": graph_data
#                 }
#             except json.JSONDecodeError:
#                 return {
#                     "status": "error",
#                     "message": "未能在响应中找到有效的JSON代码块",
#                     "raw_response": raw_response
#                 }
#
#     except json.JSONDecodeError as e:
#         return {
#             "status": "error",
#             "message": f"JSON解析错误: {str(e)}",
#             "raw_response": raw_response
#         }
#     except Exception as e:
#         return {
#             "status": "error",
#             "message": f"解析失败: {str(e)}",
#             "raw_response": raw_response
#         }

def parse_llm_response(response_text):
    """
    从LLM响应文本中提取并验证JSON数据
    """
    try:
        # 尝试直接解析整个响应
        parsed_data = json.loads(response_text)
    except json.JSONDecodeError:
        # 尝试使用正则表达式提取JSON块
        json_pattern = r'\{.*\}'  # 简单的JSON模式，匹配大括号内的内容
        json_match = re.search(json_pattern, response_text, re.DOTALL)

        if json_match:
            json_str = json_match.group(0)
            try:
                parsed_data = json.loads(json_str)
            except json.JSONDecodeError as e:
                return {"status": "error", "message": f"提取的JSON格式不正确: {str(e)}", "raw_response": response_text}
        else:
            # 尝试提取<think>标签后的内容
            think_pattern = r'<think>.*?</think>(.*)'
            think_match = re.search(think_pattern, response_text, re.DOTALL)

            if think_match:
                content_after_think = think_match.group(1).strip()
                try:
                    parsed_data = json.loads(content_after_think)
                except json.JSONDecodeError as e:
                    return {"status": "error", "message": f"去除<think>标签后的内容不是有效的JSON: {str(e)}",
                            "raw_response": response_text}
            else:
                return {"status": "error", "message": "未能在响应中找到有效的JSON代码块", "raw_response": response_text}

    # 验证数据结构
    if not isinstance(parsed_data, dict):
        return {"status": "error", "message": "返回的数据不是有效的JSON对象", "raw_response": response_text}

    # 提取并验证 nodes 和 edges
    nodes = parsed_data.get("nodes", [])
    edges = parsed_data.get("edges", [])

    if not isinstance(nodes, list):
        return {"status": "error", "message": "nodes 字段不是有效的列表", "raw_response": response_text}
    if not isinstance(edges, list):
        return {"status": "error", "message": "edges 字段不是有效的列表", "raw_response": response_text}

    return {
        "status": "success",
        "data": {
            "nodes": nodes,
            "edges": edges
        }
    }


def ensure_all_nodes_inserted_and_id_mapped(client, nodes_data, task_id):
    """
    确保所有节点已插入并映射其ID，同时验证节点属于当前任务

    Args:
        client: JanusGraph客户端
        nodes_data: 节点数据列表
        task_id: 当前任务ID

    Returns:
        节点名称到节点ID的映射字典
    """
    name_to_id = {}
    missing_nodes = []

    for node in nodes_data:
        label = node['label']
        properties = node['properties']
        name = properties['name']

        # 查询节点ID并确保属于当前任务
        result = client.nodes_id(label, name, task_id)

        if result and len(result) > 0:
            # 验证节点的task_id属性
            node_data = result[0]
            if node_data['task_id'] == task_id:
                name_to_id[name] = node_data
            else:
                logger.warning(f"节点 {name} 存在但task_id不匹配，预期: {task_id}，实际: {node_data['task_id']}")
                missing_nodes.append(name)
        else:
            logger.error(f"节点 {label}:{name} 在任务 {task_id} 中未找到")
            missing_nodes.append(name)

    # 处理缺失的节点
    if missing_nodes:
        logger.warning(f"找到 {len(missing_nodes)} 个缺失的节点: {missing_nodes}")

    return name_to_id

def vertex_to_dict(graph, vertex):
    """
    将 Vertex 的信息转换为字典
    :param graph: 图遍历源，类型：GraphTraversalSource
    :param vertex: 顶点对象，Vertex(id, label)
    :return: 顶点信息字典
    """
    properties = graph.V(vertex).valueMap().toList()[0]
    for key in properties.keys():
        properties[key] = properties[key][0]
    return {
        'id': vertex.id,
        'label': vertex.label,
        'properties': properties
    }

def get_edge_id(edge):
    return str(edge.id)

def edge_to_dict(graph, edge):
    """
    将 Edge 的信息转换为字典
    :param graph: 图遍历源，类型：GraphTraversalSource
    :param edge: 边对象，Edge(id, label)
    :return: 边信息字典
    """
    properties = graph.E(edge.id).elementMap().next()  # 获取完整的边信息字典

    # 处理边 ID，转换为字符串，确保可 JSON 序列化
    edge_id = str(properties[T.id]) if T.id in properties else str(edge.id)

    # 处理 source（起点ID）和 target（终点ID）
    source_id = str(properties[Direction.OUT][T.id]) if Direction.OUT in properties else str(edge.outV.id)
    target_id = str(properties[Direction.IN][T.id]) if Direction.IN in properties else str(edge.inV.id)

    # 处理边的标签
    edge_label = properties[T.label] if T.label in properties else edge.label

    # 过滤出真正的属性（去掉 ID、方向、标签）
    edge_properties = {
        k: v for k, v in properties.items()
        if k not in [T.id, T.label, Direction.OUT, Direction.IN]
    }

    return {
        "id": edge_id,
        "label": edge_label,
        "source": source_id,
        "target": target_id,
        "properties": edge_properties
    }

def docx_analysis(file):
    """
    docx解析工具, 用于解析文档中的信息
    :param file: api接收到的文件对象
    :return: 解析后的信息
    """

    doc = Document(file)
    # full_text = '\n'.join([para.text for para in doc.paragraphs])
    structured_content = []
    # 标题级别和对应的文本内容
    heading_levels = {
        'Heading 1': 1, '标题 1': 1,
        'Heading 2': 2, '标题 2': 2,
        'Heading 3': 3, '标题 3': 3,
        'Heading 4': 4, '标题 4': 4,
    }
    for para in doc.paragraphs:
        text = para.text.strip()
        if not text:  # 跳过空行
            continue
        # 获取段落样式名称
        style_name = para.style.name if para.style else None
        # 检查是否为标题
        if style_name and style_name in heading_levels:
            level = heading_levels[style_name]
            structured_content.append({
                'type': str(level) + '级标题',
                'text': text
            })
        else:  # 正文
            structured_content.append({
                'type': '正文',
                'text': text
            })
    # 将结构化内容转换为Markdown格式的字符串
    if structured_content:
        document_content = "\n\n".join([
            f"# {item['text']}" if item['type'] == '1级标题' else
            f"## {item['text']}" if item['type'] == '2级标题' else
            f"### {item['text']}" if item['type'] == '3级标题' else
            f"#### {item['text']}" if item['type'] == '4级标题' else
            f"{item['text']}"
            for item in structured_content
        ])
        # print(f'docx解析成功:{document_content}')
        return document_content, 1
    else:
        # print('docx解析失败')
        return '', 0


def judge_vertex_in_graph(graph, vertex_dict):
    vertices = graph.g.V().hasLabel(vertex_dict['label']).toList()
    for vertex in vertices:
        if all(graph.g.V(vertex.id).values(k).next() == v for k, v in vertex_dict['properties'].items()):
            return True
    return False

def get_sub_graph(graph, vertices=None, edges=None, vertex_properties=None):
    pass  # 暂时不需要填充

def query_edges_of_vertex(graph, v_id):
    pass  # 暂时不需要填充

def query_near_vertex(graph, v_id):
    pass  # 暂时不需要填充

def extract_text_from_pdf(file_path):
    text = ""
    with open(file_path, 'rb') as file:
        reader = PdfReader(file)
        for page in reader.pages:
            text += page.extract_text()
    return text

def extract_text_from_docx(file_path):
    doc = Document(file_path)
    text = "\n".join([para.text for para in doc.paragraphs])
    return text

def extract_text_from_txt(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        text = file.read()
    return text

# def extract_text_from_pptx(file_path):
#     from pptx import Presentation
#     prs = Presentation(file_path)
#     text = ""
#     for slide in prs.slides:
#         for shape in slide.shapes:
#             if hasattr(shape, "text"):
#                 text += shape.text + "\n"
#     return text

def store_text_in_knowledge_graph(graph, text):
    pass  # 暂时不需要填充

# 新增处理文件上传的任务函数
# @celery_app.task
# def process_file_upload(temp_file_path, file_type, task_id):
#     try:
#         # 更新任务状态为处理中
#         task = Task.objects.get(task_id=task_id)
#         task.status = 'processing'
#         task.save()
#         # 判断文件类型并解析
#         mime = magic.Magic(mime=True)
#         detected_type = mime.from_file(temp_file_path)
        
#         if detected_type == 'application/pdf':
#             text = extract_text_from_pdf(temp_file_path)
#         elif detected_type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
#             text = extract_text_from_docx(temp_file_path)
#         elif detected_type == 'text/plain':
#             text = extract_text_from_txt(temp_file_path)
#         elif detected_type == 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
#             text = extract_text_from_pptx(temp_file_path)
#         else:
#             # 删除临时文件
#             os.remove(temp_file_path)
#             os.rmdir(os.path.dirname(temp_file_path))
#             print(f"Task {task_id}: Unsupported file type")
#             return
        
#         # 删除临时文件
#         os.remove(temp_file_path)
#         os.rmdir(os.path.dirname(temp_file_path))
        
#         print(f"Task {task_id}: File processed successfully")
#         # 调用知识点萃取函数
#         # store_text_in_knowledge_graph(client.g, text)
#         # 更新任务状态为完成,后续这个状态的改变会放在萃取函数中，直至萃取完成才改变
#         task.status = 'completed'
#         task.result = text  # 保存提取的文本内容
#         task.save()
#     except Task.DoesNotExist:
#         print(f"Task {task_id}: Task does not exist")
#     except Exception as e:
#         print(f"Task {task_id}: Error processing file - {str(e)}")
#         task.status = 'failed'
#         task.save()

# 处理文件上传
# def process_file_upload(file_path, task_id):
#     try:
#         # 更新任务状态为处理中
#         status[task_id]['status'] = 'processing'
#         print("---------------------------------")
#         print(f"file path is {file_path}")
#         # 检查文件路径是否存在
#         if not os.path.exists(file_path):
#             print(f"Task {task_id}: File does not exist at {file_path}")
#             status[task_id]['status'] = 'failed'
#             return
#         # 判断文件类型并解析
#         # mime = magic.Magic(mime=True)
#         # detected_type = mime.from_file(file_path)
#         # 使用 mimetypes 模块检测文件类型
#         detected_type, _ = mimetypes.guess_type(file_path)
#         print(f"File type detected - {detected_type}")
#         if detected_type == 'application/pdf':
#             text = extract_text_from_pdf(file_path)
#         elif detected_type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
#             text = extract_text_from_docx(file_path)
#         elif detected_type == 'text/plain':
#             text = extract_text_from_txt(file_path)
#         elif detected_type == 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
#             text = extract_text_from_pptx(file_path)
#         else:
#             # 删除临时文件
#             os.remove(file_path)
#             os.rmdir(os.path.dirname(file_path))
#             print(f"Task {task_id}: Unsupported file type")
#             status[task_id]['status'] = 'failed'
#             return
#
#         # 删除临时文件
#         os.remove(file_path)
#         os.rmdir(os.path.dirname(file_path))
#
#         print(f"Task {task_id}: File processed successfully")
#
#         # 调用知识点萃取函数（暂时不需要填充）
#         # store_text_in_knowledge_graph(client.g, text)
#
#         # 更新任务状态为完成
#         status[task_id]['status'] = 'completed'
#         status[task_id]['result'] = text  # 保存提取的文本内容
#     except Exception as e:
#         print(f"Task {task_id}: Error processing file - {str(e)}")
#         status[task_id]['status'] = 'failed'