from datetime import datetime
import uuid
import json
from .prompt import *
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.http import StreamingHttpResponse
from rest_framework import viewsets, permissions
from rest_framework.decorators import action
from .janusgraph_client import JanusGraphClient
from .janusgraph_client import run_janusgraph_sync_in_thread
import json
from langchain_openai import ChatOpenAI
# from langchain.prompts import ChatPromptTemplate
from langchain_core.prompts import PromptTemplate
from rest_framework.decorators import api_view
from gremlin_python.process.traversal import T
from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
from .utils import *
from django.shortcuts import get_object_or_404
from .models import GraphTask
from .tasks import extract_knowledge_graph
from .serializers import GraphTaskSerializer
from course.models import Course
from django.conf import settings
from course.serializers import Course<PERSON>hapterKnowledgeSerializer
from rest_framework.pagination import PageNumberPagination
# from .janusgraph_course_client import run_janusgraph_sync_in_thread
from .janusgraph_course_client import JanusGraphSyncTool
import uuid
# from .models import Task,status
import logging

logger = logging.getLogger(__name__)

# janusgraph_url = 'ws://172.16.1.239:8182/gremlin'  # 根据实际情况修改

class CustomPagination(PageNumberPagination):
    page_size = 10  # 设置每页显示的条目数
    page_size_query_param = 'page_size'  # URL中用于指定每页条目数的参数
    max_page_size = 100  # 最大每页条目数限制


class KnowledgeGraphViewSet(viewsets.ViewSet):
    client = JanusGraphClient(settings.JANUSGRAPH_URL)
    course_client = JanusGraphSyncTool(settings.JANUSGRAPH_URL)
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = CustomPagination

    @action(detail=False, methods=['get'], url_path='get_all_data')
    def get_all_data(self, request):
        """
        获取所有节点和边
        """
        try:
            data = self.client.get_all_data()
            return api_response(200, 'success', data)
        except Exception as e:
            return api_response(400, 'error', str(e))

    @action(detail=False, methods=['post'], url_path='retrieve_data')
    def retrieve_data(self, request):
        """
        根据用户id获取节点和边
        """
        task_id = request.data.get('taskId')
        if not task_id:
            return api_response(400, 'error', '缺少参数')
        try:
            if not self.client.is_connected():
                logger.info("连接丢失，尝试重连...")
                self.client.reconnect()
            data = self.client.by_id_get_all_data(task_id)
            return api_response(200, 'success', data)
        except ConnectionError as e:
            logger.error(f"连接错误: {str(e)}")
            self.client.reconnect()
            try:
                # 重连后重新获取数据
                data = self.client.by_id_get_all_data(task_id)
                return api_response(200, 'success', data)
            except Exception as e:
                logger.error(f"重试失败: {str(e)}")
                return api_response(400, 'error', '网络连接失败，请稍后再试')
        except Exception as e:
            return api_response(400, 'error', str(e))

    @action(detail=False, methods=['post'], url_path='classify_data')
    def classify_data(self, request):
        """
        根据分类获取节点和边
        """
        label = request.data.get('label')
        if not label:
            return api_response(400, 'error', '缺少参数')
        try:
            data = self.client.classify_all_data(label)
            return api_response(200, 'success', data)
        except Exception as e:
            return api_response(400, 'error', str(e))

    @action(detail=False, methods=['post'], url_path='add_node')
    def add_node(self, request):
        """
        添加图谱节点
        """
        label = request.data.get("label")
        properties = request.data.get("properties", {})
        # user_id = request.data.get("userID")
        task_id = request.data.get("task_id")

        if not label or not properties:
            return api_response(400, 'error', '缺少参数')
        if not task_id:
            return api_response(400,'error','缺少taskID参数')
        user = self.request.user
        user_id = user.id
        try:
            vertex = self.client.add_node(label, properties, user_id, task_id)
            data = vertex_to_dict(self.client.g, vertex)
            # 更新图任务的节点数量
            graph_task = GraphTask.objects.get(id=task_id)
            if graph_task:
                graph_task.node_count = graph_task.node_count + 1
                graph_task.save()
            return api_response(200, 'success', data)
        except GraphTask.DoesNotExist:
            return api_response(400, 'error', '指定的任务不存在')
        except Exception as e:
            return api_response(400, 'error', str(e))

    @action(detail=False, methods=['post'], url_path='add_nodes_and_edges')
    def add_nodes_and_edges(self, request):
        """
        添加图谱节点，并自动连接边
        """
        label = request.data.get("label")
        properties = request.data.get("properties", {})
        task_id = request.data.get("task_id")
        from_vertex_id = request.data.get("from_vertex_id")  # 起始节点
        # to_vertex_id = request.data.get("to_vertex_id")
        edge_label = request.data.get("edge_label", "暂无")  # 边的标签，默认值

        if not label or not properties:
            return api_response(400, 'error', '缺少参数')
        if not task_id:
            return api_response(400, 'error', '缺少taskID参数')
        if not from_vertex_id:
            return api_response(400, 'error', '至少需要提供from_vertex_id')

        user = self.request.user
        user_id = user.id

        try:
            # 添加节点
            vertex = self.client.add_node(label, properties, user_id, task_id)
            data = vertex_to_dict(self.client.g, vertex)
            graph_task = GraphTask.objects.get(id=task_id)
            if graph_task:
                graph_task.node_count = graph_task.node_count + 1
                graph_task.save()
            logger.info(f"添加节点成功: {data}")

            # 获取新节点的ID (JanusGraph内部ID)
            to_vertex_id = data.get('id')  # 这是JanusGraph内部生成的ID
            if not to_vertex_id:
                raise ValueError("无法获取新节点的ID")

            logger.info(f"新节点ID: {to_vertex_id}")

            # 如果存在连接边的需求，添加边
            if to_vertex_id:
                if not to_vertex_id:
                    to_vertex_id = to_vertex_id

                # 创建边的属性，自动添加user_id和task_id
                edge_properties = {
                    'userID': user_id,
                    'taskId': task_id,
                    **request.data.get('edge_properties', {})  # 合并用户提供的边属性
                }

                # 创建边
                result = self.client.add_edge(from_vertex_id, to_vertex_id, edge_label, edge_properties)
                if result:
                    edge_data = self.client._edge_to_dict(result)
                    data['edge'] = edge_data  # 返回边的信息
                    logger.info(f"添加边成功: {edge_data}")
                else:
                    logger.warning(f"添加边失败: from={from_vertex_id}, to={to_vertex_id}, label={edge_label}")

            return api_response(200, 'success', data)
        except Exception as e:
            logger.error(f"添加节点和边失败: {str(e)}")
            return api_response(400, 'error', str(e))

    @action(detail=False, methods=['post'], url_path='get_node_by_id')
    def get_node_by_id(self, request):
        """
        根据id获取节点
        """
        vertex_id = request.data.get('vertex_id')
        if not vertex_id:
            return api_response(400, 'error', '参数错误')
        try:
            data = self.client.get_node(vertex_id)
            return api_response(200, 'success', data)
        except Exception as e:
            return api_response(400, 'error', str(e))

    @action(detail=False, methods=['put'], url_path='update_node')
    def update_node(self, request):
        """
        根据id修改节点
        """
        node_id = request.data.get('vertex_id')
        properties = request.data.get('properties', {})
        if not node_id or not properties:
            return api_response(400, 'error', '参数错误')
        user = self.request.user
        if 'user_id' not in properties:
            properties['user_id'] = user.id
        try:
            result = self.client.update_node(node_id, properties)
            return api_response(200, 'success', result)
        except Exception as e:
            return api_response(400, 'error', str(e))

    @action(detail=False, methods=['post'], url_path='delete_node')
    def delete_node(self, request):
        """
        根据id删除节点
        """
        node_id = request.data.get('vertex_id')
        task_id = request.data.get('task_id')
        if not node_id or not task_id:
            return api_response(400, 'error', '参数错误，请传入节点id或任务id')
        try:
            graph_task = GraphTask.objects.get(id=task_id)
            result = self.client.delete_node(node_id)
            if graph_task.node_count > 0:  # 防止节点数量变为负数
                graph_task.node_count -= 1
                graph_task.save()
            return api_response(200, 'success', result)
        except GraphTask.DoesNotExist:
            return api_response(400, 'error', '指定的任务不存在')
        except Exception as e:
            return api_response(400, 'error', str(e))

    @action(detail=False, methods=['get'], url_path='get_all_node')
    def get_all_node(self, request):
        """
        获取所有节点
        """
        try:
            data = self.client.get_all_nodes()
            return api_response(200, 'success', data)
        except Exception as e:
            return api_response(400, 'error', str(e))

    @action(detail=False, methods=['post'], url_path='add_edge')
    def add_edge(self, request):
        """
        增加边
        """
        from_vertex_id = request.data.get('from_vertex_id')
        if not from_vertex_id:
            return api_response(400, 'error', '参数错误，未传入主节点')
        to_vertex_id = request.data.get('to_vertex_id')
        if not to_vertex_id:
            return api_response(400, 'error', '参数错误，未传入次节点')
        label = request.data.get('label')
        # if not label:
        #     return api_response(400, 'error', '参数错误，未传入标签')
        properties = request.data.get('properties', {})
        if not properties:
            return api_response(400, 'error', '参数错误，未传入内容')
        user = self.request.user
        userID = user.id
        properties['userID'] = userID
        try:
            result = self.client.add_edge(from_vertex_id, to_vertex_id, label, properties)
            data = self.client._edge_to_dict(result)
            return api_response(200, 'success', data)
        except Exception as e:
            return api_response(400, 'error', str(e))

    @action(detail=False, methods=['post'], url_path='get_edge_by_id')
    def get_edge_by_id(self, request):
        """
        根据id查询边
        """
        edge_id = request.data.get('edge_id')
        if not edge_id:
            return api_response(400, 'error', '参数错误')
        try:
            data = self.client.get_edge(edge_id)
            return api_response(200, 'success', data)
        except Exception as e:
            return api_response(400, 'error', str(e))

    @action(detail=False, methods=['put'], url_path='update_edge')
    def update_edge(self, request):
        """
        根据id修改边
        """
        edge_id = request.data.get('edge_id')
        # label = request.data.get('label')
        properties = request.data.get('properties', {})
        if not edge_id or not properties:
            return api_response(400, 'error', '参数错误')
        user = self.request.user
        if 'userID' not in properties:
            properties['userID'] = user.id
        try:
            result = self.client.update_edge(edge_id, properties)
            return api_response(200, 'success', result)
        except Exception as e:
            return api_response(400, 'error', str(e))

    @action(detail=False, methods=['put'], url_path='update_label')
    def update_label(self, request):
        """
        根据id修改边的label
        """
        edge_id = request.data.get('edge_id')
        label = request.data.get('label')
        # properties = request.data.get('properties', {})
        if not edge_id or not label:
            return api_response(400, 'error', '参数错误')
        try:
            result = self.client.update_edge_label(edge_id, label)
            return api_response(200, 'success', result)
        except Exception as e:
            return api_response(400, 'error', str(e))

    @action(detail=False, methods=['post'], url_path='delete_edge')
    def delete_edge(self, request):
        """
        根据id删除边
        """
        edge_id = request.data.get('edge_id')
        if not edge_id:
            return api_response(400, 'error', '参数错误，请传入节点id')
        if isinstance(edge_id, str) and edge_id.strip().startswith("{") and "'@type'" in edge_id:
            return api_response(400, 'error', '边 ID 格式错误，不允许 RelationIdentifier 格式')
        try:
            result = self.client.delete_edge(edge_id)
            return api_response(200, 'success', result)
        except Exception as e:
            return api_response(400, 'error', str(e))

    @action(detail=False, methods=['get'], url_path='get_all_edge')
    def get_all_edge(self, request):
        """
        获取所有边
        """
        try:
            data = self.client.get_all_edges_with_details()
            return api_response(200, 'success', data)
        except Exception as e:
            return api_response(400, 'error', str(e))

    @action(detail=False, methods=['delete'], url_path='delete_all')
    def delete_all(self, request):
        """
        删除所有数据
        """
        try:
            data = self.client.delete_nodes()
            return api_response(200, 'success')
        except Exception as e:
            return api_response(400, 'error', str(e))

    @action(detail=False, methods=['post'], url_path='create_graph')
    def create_graph(self, request):
        """创建知识图谱提取任务"""
        name = request.data.get('name')
        theme = request.data.get('theme')
        upload_file = request.FILES.get('file')

        if not name:
            return api_response(400, 'error', '知识图谱名称不能为空')

        # 校验当前用户是否已经存在同名图谱
        existing = GraphTask.objects.filter(name=name, user=request.user).exists()
        if existing:
            return api_response(400, 'error', f'您已创建名为“{name}”的图谱，请使用不同名称')

        if not theme and not upload_file:
            return api_response(400, 'error', '必须提供文本或上传附件中的一个')

        combined_content = ''
        if upload_file:
            file = document_parsing(upload_file)
            combined_content = file['content_preview']

        text_chunks = []
        if theme and combined_content:
            final_content = f"主题内容：\n{theme}\n\n参考文档内容：\n{combined_content}"
            text_chunks = text_into_chunks(final_content)
        elif theme:
            text_chunks = text_into_chunks(theme)
        else:
            text_chunks = text_into_chunks(combined_content)

        # print('分割后的数据',text_chunks)

        # 创建新任务，包含知识图谱名称
        task = GraphTask.objects.create(
            user=request.user if request.user.is_authenticated else None,
            name=name,
            status='pending'
        )

        # 异步执行知识图谱提取
        extract_knowledge_graph.delay(str(task.id), text_chunks)

        data = {
            'task_id': task.id,
            'name': task.name,
            'status': task.status,
            'created_at': task.created_at
        }

        return api_response(200, 'success', data)

    @action(detail=False, methods=['get'], url_path='list_graphs')
    def list_graphs(self, request):
        """获取当前用户的知识图谱列表"""
        if not request.user.is_authenticated:
            return api_response(401, 'error', '请先登录')

        name_query = request.query_params.get('name', '').strip()

        # 仅筛选该用户的任务
        user_tasks = GraphTask.objects.filter(user=request.user)

        # tasks = GraphTask.objects.filter(user=request.user).order_by('-created_at')
        # 若传入 name，进行模糊查询
        if name_query:
            user_tasks = user_tasks.filter(name__icontains=name_query)

        user_tasks = user_tasks.order_by('-created_at')
        user_tasks = user_tasks.select_related('lesson_id')
        # 分页
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(user_tasks, request)

        result_data = [
            {
                'id': task.id,
                'name': task.name,
                'status': task.status,
                'status_display': task.get_status_display(),
                'node_count': task.node_count or 0,
                'edge_count': task.edge_count or 0,
                'total_entities': (task.node_count or 0) + (task.edge_count or 0),
                'created_at': task.created_at,
                'updated_at': task.update_at,
                'course_title': task.lesson_id.title if task.lesson_id else None  # 获取课程标题
            }
            for task in page
        ]
        paginated_response = paginator.get_paginated_response(result_data)
        data = paginated_response.data

        return api_response(200, 'success', data)


    @action(detail=False, methods=['delete'], url_path='by_taskId_delete')
    def by_taskId_delete(self, request):
        """
        根据taskID删除任务和节点数据
        """
        ids = request.data.get('ids', [])
        if not ids or not isinstance(ids, list):
            return api_response(400, 'error', '参数错误，需提供ids列表')

        try:
            for task_id in ids:
                self.client.delete_node_and_edge(task_id)
        except Exception as e:
            return api_response(500, 'error', f'删除图数据库失败：{str(e)}')

        queryset = GraphTask.objects.filter(id__in=ids)
        deleted_count = queryset.delete()
        if deleted_count == 0:
            return api_response(400, 'error', '找不到对应的记录')

        queryset.delete()
        return api_response(200,'success','删除成功')

    @action(detail=True, methods=['post'], url_path='associate_course')
    def associate_course(self, request, pk=None):
        """将知识图谱关联到指定课程"""
        # 获取知识图谱任务
        graph_task = get_object_or_404(GraphTask, pk=pk)

        # 权限校验：仅创建者可操作
        if request.user.is_authenticated and graph_task.user != request.user:
            return api_response(403, 'error', '您无权操作此知识图谱')

        # 校验请求参数
        course_id = request.data.get('course_id')
        if not course_id:
            return api_response(400, 'error', '请提供课程ID')

        try:
            # 验证课程是否存在
            course = Course.objects.get(id=course_id)
        except Course.DoesNotExist:
            return api_response(404, 'error', '指定的课程不存在')

        # 执行关联
        graph_task.lesson_id = course
        graph_task.save()

        # 返回成功响应
        data = {
            'graph_id': graph_task.id,
            'graph_name': graph_task.name,
            'course_id': course.id,
            'course_name': course.title  # 假设Course模型有name字段
        }
        return api_response(200, 'success', data)

    @action(detail=True, methods=['post'], url_path='disassociate_course')
    def disassociate_course(self, request, pk=None):
        """解除知识图谱与课程的关联"""
        graph_task = get_object_or_404(GraphTask, pk=pk)

        # 权限校验
        if request.user.is_authenticated and graph_task.user != request.user:
            return api_response(403, 'error', '您无权操作此知识图谱')

        # 解除关联
        graph_task.lesson_id = None
        graph_task.save()

        return api_response(200, 'success', '已解除关联')


    @action(detail=False, methods=['get'], url_path='status_task')
    def status_task(self, request):
        """获取任务状态"""
        pk = request.GET.get('pk', None)
        if pk is None:
            return api_response(200, 'error', '参数错误，未提供参数')

        task = get_object_or_404(GraphTask, pk=pk)
        serializer = GraphTaskSerializer(task)
        return api_response(200, 'success', serializer.data)


    @action(detail=False, methods=['post'], url_path='get_knowledge_graph')
    def get_knowledge_graph(self, request):
        """
        自动提取知识图谱
        """
        theme = request.data.get('theme')
        upload_file = request.FILES.get('file')

        if not theme and not upload_file:
            return api_response(400, 'error', '必须提供文本或上传附件中的一个')

        combined_content = ''
        if upload_file:
            file = document_parsing(upload_file)
            combined_content = file['content_preview']
            # print('combined_content', combined_content)

        text_chunks = []
        if theme and combined_content:
            final_content = f"主题内容：\n{theme}\n\n参考文档内容：\n{combined_content}"
            text_chunks = split_text_into_chunks(final_content)
        elif theme:
            # final_content = f"主题内容：\n{theme}"
            text_chunks = split_text_into_chunks(theme)
        else:
            # final_content = f"参考文档内容：\n{combined_content}"
            text_chunks = split_text_into_chunks(combined_content)

        # datas = split_text(final_content)
        for i, chunk in enumerate(text_chunks):
            print(f"Chunk {i + 1}: {chunk[:50]}...（共 {len(chunk)} 字）")
        # print(text_chunks, '分块内容')
        # return api_response(200, 'success')

        with open(
                "./apps/graph/generate_format/data_format.json", "r", encoding="utf-8"
        ) as f:
            data_format = json.load(f)

        with open(
                "./apps/graph/generate_format/data_format_example.json", "r", encoding="utf-8"
        ) as f:
            data_format_example = json.load(f)

        llm = ChatOpenAI(
            model=settings.LLM_NAME,
            openai_api_key=settings.LLM_API_KEY,
            openai_api_base=settings.LLM_BASE_URL_LESSON,
            max_tokens=4000,
            temperature=1,
            streaming=True,
        )

        # 创建 ChatPromptTemplate，明确指定所有变量
        prompt = ChatPromptTemplate.from_messages([
            SystemMessagePromptTemplate.from_template(graph_prompt()),
            HumanMessagePromptTemplate.from_template("请从以下内容中抽取与主题相关的实体与关系：{chunk_text}")
        ])

        all_nodes = []
        all_edges = []
        for idx, chunk in enumerate(text_chunks):
            print(f"处理第 {idx + 1} 块文本...")
            # 格式化提示消息，提供所有必需的变量
            messages = prompt.format_messages(
                data_format=json.dumps(data_format, ensure_ascii=False, indent=2),
                data_format_example=json.dumps(data_format_example, ensure_ascii=False, indent=2),
                chunk_text=chunk
                # final_content=final_content,
                # combined_content = {combined_content if combined_content else "无参考文档"}
            )
            response = ""
            for r in llm.stream(messages):
                if hasattr(r, 'content'):
                    response += r.content

            parsed = parse_llm_response(response)
            print('huoqude shuju',parsed)
            if parsed["status"] == "success":
                all_nodes.extend(parsed["data"]["nodes"])
                all_edges.extend(parsed["data"]["edges"])
            else:
                print(f"原始第 {idx + 1} 块 LLM 响应：\n{response}")
                print(f"第 {idx + 1} 块解析失败: {parsed['message']}")

        print('未去重后的节点',all_nodes)
        print('未去重后的边',all_edges)

        # unique_nodes, unique_edges = merge_nodes_and_edges(all_nodes, all_edges)
        #
        # print('去重后的节点', unique_nodes)
        # print('去重后的边', unique_edges)
        # # return api_response(200, 'success', {})
        #
        # # # return api_response(200, 'success')
        # # # response = llm(messages)
        # # # print('666666',response)
        # #
        # # # 调用LLM进行实体抽取
        # # # response = ""
        # # # for chunk in llm.stream(messages):
        # # #     if hasattr(chunk, 'content'):
        # # #         response += chunk.content
        # # #
        # # # # 打印原始响应用于调试
        # # # # print(f"原始LLM响应: {response}")
        # user_id = self.request.user.id
        # # print('获取到的用户id', user_id)
        # # # # 解析LLM响应
        # # # parsed_result = parse_llm_response(response)
        # # #
        # # # nodes_data = parsed_result['data']['nodes']
        # # # edges_data = parsed_result['data']['edges']
        # # #
        # # # print('输出响应', nodes_data)
        # task_id = 666
        # for node in unique_nodes:
        #     label = node['label']
        #     properties = node['properties']
        #     vertex = self.client.add_node(label, properties, user_id, task_id)
        #
        # # print('输出响应边', edges_data)
        #
        # name_to_id = ensure_all_nodes_inserted_and_id_mapped(self.client, unique_nodes, task_id)
        # print(name_to_id, '<UNK>')
        # # return api_response(200, 'success', name_to_id)
        #
        # # 存储边
        # stored_edges = []
        # skipped_edges = []
        #
        # for edge in unique_edges:
        #     edge_label = edge["label"]
        #     from_entity = edge["from_entity"]
        #     to_entity = edge["to_entity"]
        #     # properties = edge["properties"]
        #     properties = {
        #         **edge["properties"],  # 保留原有属性
        #         "userID": user_id  # 添加字符串类型的userID
        #     }
        #
        #     # 获取起点和终点的ID
        #     from_id = name_to_id.get(from_entity)
        #     to_id = name_to_id.get(to_entity)
        #     print(from_id, '起点的id')
        #     print(to_id, '终点的id')
        #
        #     # 如果两个实体ID都存在，则创建边
        #     if from_id and to_id:
        #         # 提取实际的顶点ID（使用<T.id: 1>键）
        #         from_vertex_id = from_id[T.id]
        #         to_vertex_id = to_id[T.id]
        #
        #         if from_vertex_id and to_vertex_id:
        #             # 存储边
        #             edge_result = self.client.add_edge(
        #                 from_vertex_id,
        #                 to_vertex_id,
        #                 edge_label,
        #                 properties,
        #             )
        #             stored_edges.append({
        #                 "edge": edge,
        #                 "result": edge_result
        #             })
        #             print(f"已存储边: {from_entity} -{edge_label}-> {to_entity}")
        #         else:
        #             skipped_edges.append({
        #                 "edge": edge,
        #                 "reason": f"无法提取顶点ID - from: {from_vertex_id}, to: {to_vertex_id}"
        #             })
        #             print(f"无法提取顶点ID - from: {from_vertex_id}, to: {to_vertex_id}")
        #
        #     else:
        #         skipped_edges.append({
        #             "edge": edge,
        #             "reason": f"找不到实体ID - from: {from_entity}, to: {to_entity}"
        #         })
        #         print(stored_edges,f"找不到实体ID - from: {from_entity}, to: {to_entity}",'报错信息')
        #
        #
        # if parsed["status"] == "success":
        #     return api_response(200, 'success', parsed["data"])
        # else:
        #     return api_response(200, 'error', parsed["message"])

    @action(detail=False, methods=['get'], url_path='get_course_details')
    def get_course_details(self, request):
        course_id = request.query_params.get('course_id', None)
        user = self.request.user
        if not course_id:
            api_response(400, 'error', '未传入参数')
        # try:
        #     if isinstance(course_ids, str):
        #         course_ids = json.loads(course_ids)
        #     elif isinstance(course_ids, list):
        #         course_ids = course_ids
        #     else:
        #         raise ValueError("course_id 必须是一个字符串或列表")
        #     course_ids = [int(cid) for cid in course_ids]
        # except (ValueError, TypeError) as e:
        #     return JsonResponse({'code': 400, 'message': 'error', 'data': f'无效的 course_id 格式: {str(e)}'})
        try:
            course = Course.objects.get(id=course_id)
        except Course.DoesNotExist:
            return api_response(404, 'error', '课程不存在')

        # 同步数据到JanusGraph
        try:
            has_graph = self.client.by_chapter_id_get_nodes(course_id, user.id)
            logger.info(f"课程 {course_id} 的知识图谱是否已创建: {has_graph}")
            if not has_graph:
                # 课程图谱不存在，进行同步
                serializer = CourseChapterKnowledgeSerializer(course)
                result = run_janusgraph_sync_in_thread([serializer.data], user.id, course_id)
            else:
                logger.info('课程图谱已存在，跳过同步')
            data = self.client.by_course_id_get_all_data(course_id, user.id)
            return api_response(200, 'succese', data)
        except Exception as e:
            return api_response(400, 'error', f"同步到JanusGraph失败: {e}")

    @action(detail=False, methods=['post'], url_path='get_course_nodes')
    def get_course_nodes(self, request):
        chapter_id = request.data.get('chapter_id')
        course_id = request.data.get('course_id')
        knowledge_data = request.data.get('knowledge_data')
        user = self.request.user
        print('用户',user.id)
        result = self.client.add_knowledge_point_to_chapter(chapter_id, course_id, user.id, knowledge_data)
        # result = self.client.by_chapter_id_get_nodes(course_id, user.id)
        return api_response(200, 'succese', result)

    @action(detail=False, methods=['post'], url_path='get_chapter_nodes')
    def get_chapter_nodes(self, request):
        # chapter_id = request.data.get('chapter_id')
        course_id = request.data.get('course_id')
        chapter_data = request.data.get('chapter_data')
        user = self.request.user
        print('用户', user.id)
        result = self.client.add_chapter(course_id, user.id, chapter_data)
        return api_response(200, 'succese', result)

    @action(detail=False, methods=['post'], url_path='update_chapter_nodes')
    def update_chapter_nodes(self, request):
        course_id = request.data.get('course_id')
        node_type = request.data.get('node_type')
        properties = request.data.get('properties')
        user = self.request.user
        print('用户', user.id)
        result = self.client.update_node_from_properties(properties, node_type, user.id, course_id)
        return api_response(200, 'succese', result)









