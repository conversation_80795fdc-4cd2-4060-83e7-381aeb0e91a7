"""
AI 阅卷服务
"""

import json
import re
from datetime import datetime
from django.conf import settings
from langchain_ollama import ChatOllama
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END, START
from pydantic import BaseModel
from langchain_core.tools import tool
from typing import Dict, List, TypedDict

class ScoreState(TypedDict):
    """评分流程状态结构"""

    stem: str  # 题目题干
    student_answer: str  # 学生答案
    sample_answer: str  # 标准答案
    explanation: str  # 参考答案解析
    initial_score: int  # 初始评分数
    calibrated_score: int  # 校准后的分数
    justification: str  # 评分理由
    error_types: str  # 错误类型
    max_score: float  # 满分值


class AIMarkingService:
    """AI评分服务"""

    _instance = None

    def __new__(cls):
        """
        单例模式实现
        """
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            # cls._instance.llm = ChatOllama(model=model_name, base_url=base_url)
            cls._instance.llm = ChatOpenAI(
                model=settings.LLM_NAME,
                openai_api_key=settings.LLM_API_KEY,
                openai_api_base=settings.LLM_BASE_URL_LESSON
            )
            
        return cls._instance

    # --------------------核心AI服务方法--------------------
    def grade_essay(self, stem, explanation, sample_answer, student_answer, max_score):
        """
        问答题批阅-主入口
        """
        initial_state = ScoreState(
            stem=stem,
            student_answer=student_answer,
            sample_answer=sample_answer,
            explanation=explanation,
            initial_score=0,
            calibrated_score=0,
            justification="",
            error_types="",
            max_score=max_score
        )

        workflow = self.build_scoring_flow()
        final_state = workflow.invoke(initial_state)

        # 确保分数是数值类型
        calibrated_score = self._ensure_numeric(final_state["calibrated_score"])
        initial_score = self._ensure_numeric(final_state["initial_score"])

        return {
            "get_score": min(max_score, calibrated_score),
            "initial_score": initial_score,
            "calibrated_score": calibrated_score,
            "justification": final_state["justification"],
            "error_types": final_state["error_types"],
        }
    
    def build_scoring_flow(self):
        """
        构建评分流程图
        """
        builder = StateGraph(ScoreState)

        # 添加节点
        builder.add_node("InitialScoring", self._scoring_node)
        builder.add_node("ScoreCalibration", self._calibration_node)
        builder.add_node("ErrorAnalysis", self._error_analysis_node)
        # 设置流程路径
        builder.add_edge(START, "InitialScoring")
        builder.add_edge("InitialScoring", "ScoreCalibration")
        builder.add_edge("ScoreCalibration", "ErrorAnalysis")
        builder.add_edge("ErrorAnalysis", END)

        return builder.compile()

    # --------------------流程节点--------------------
    def _scoring_node(self, state: ScoreState) -> ScoreState:
        """初评流程节点"""
        prompt = f"""
        /no_think
        【问答题评分】
        【题目】{state["stem"]}
        【学生答案】{state["student_answer"]}
        【参考答案】{state["sample_answer"]}
        【解析】{state["explanation"]}
        【满分】{state["max_score"]}
        请返回JSON格式评分结果：
        {{
        "initial_score": "0-{state['max_score']}的分数",
        "justification": "评分理由(50字内)"
        }}
        """

        try:
            response = self._call_ai(prompt)
            print(f"初评response:{response}")
            data = self._extract_json(response)

            # 确保initial_score是数值类型
            initial_score = self._ensure_numeric(data.get("initial_score", 0))
            
            # 验证必要字段
            required_fields = ["initial_score", "justification"]
            for field in required_fields:
                if field not in data:
                    raise ValueError(f"初评结果缺少{field}字段: {data}")
            
            return {
                **state,
                "initial_score": initial_score,
                "justification": data["justification"],
            }
        except Exception as e:
            print(f"初评节点错误: {str(e)}")
            # 发生错误时，返回默认值
            return {
                **state,
                "initial_score": 0,
                "justification": f"初评失败: {str(e)}",
                "error": str(e)
            }

    def _calibration_node(self, state: ScoreState) -> ScoreState:
        """校准评分流程节点"""
        prompt = f"""
        /no_think
        【评分校准】
        【题目】{state["stem"]}
        【学生答案】{state["student_answer"]}
        【参考答案】{state["sample_answer"]}
        【解析】{state["explanation"]}
        【满分】{state["max_score"]}
        【初始评分数】{state["initial_score"]}
        【评分数理由】{state["justification"]}
        请返回JSON格式校准评分结果：
        {{
        "calibrated_score": "0-{state['max_score']}的分数"
        }}
        """
        try:
            response = self._call_ai(prompt)
            data = self._extract_json(response)

            # 确保calibrated_score是数值类型
            calibrated_score = self._ensure_numeric(data.get("calibrated_score", state["initial_score"]))
            # 验证关键字段存在
            if "calibrated_score" not in data:
                raise ValueError(f"校准评分结果缺少calibrated_score字段: {data}")

            return {
                **state,
                "calibrated_score": calibrated_score
            }
        except Exception as e:
            # 发生错误时，使用初始分数作为校准分数
            print(f"校准节点错误: {str(e)}")
            return {
                **state,
                "calibrated_score": state["initial_score"],  # 使用初始分数作为回退
                "error": str(e)
            }

    def _error_analysis_node(self, state: ScoreState) -> ScoreState:
        """错误分类节点"""
        prompt = f"""
        /no_think
        【错误分类】
        【学生答案】{state["student_answer"]}
        【解析】{state["explanation"]}
        【评分数理由】{state["justification"]}
        请用一句话描述主要错误类型：
        {{
        "error_types": "错误类型"
        }}
        """
        try:
            response = self._call_ai(prompt)
            data = self._extract_json(response)
            return {
                **state,
                "error_types": data.get("error_types", "无明确错误")
            }
        except Exception as e:
            print(f"错误分析节点错误: {str(e)}")
            return {
                **state,
                "error_types": "AI分析失败",
                "error": str(e)
            }


    def _call_ai(self, prompt):
        """调用AI的统一接口"""
        try:
            response = self.llm.invoke(prompt)
            return response.content
        except Exception as e:
            return json.dumps({
                "error": f"调用AI接口失败: {str(e)}",
                "get_score": 0,
                "justification": "AI服务不可用"
            })

    def _extract_json(self, text):
        """从文本提取JSON内容"""
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            match = re.search(r"\{.*\}", text, re.DOTALL)
            if match:
                return json.loads(match.group())
            raise ValueError("无法解析JSON响应")
        
    def _ensure_numeric(self, value):
        """确保值为数值类型"""
        if isinstance(value, (int, float)):
            return value
        try:
            return float(value)
        except (ValueError, TypeError):
            print(f"无法将值转换为数值: {value}")
            return 0

