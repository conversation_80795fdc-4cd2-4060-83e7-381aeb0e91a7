from django.db import models
from django.contrib.auth import get_user_model
from course.models import Course  # 导入课程模型

User = get_user_model()


class KnowledgeBase(models.Model):
    """知识库模型"""
    id = models.CharField(max_length=100, primary_key=True, verbose_name="知识库ID")
    name = models.CharField(max_length=200, verbose_name="知识库名称")
    description = models.TextField(blank=True, null=True, verbose_name="知识库描述")
    document_count = models.IntegerField(default=0, verbose_name="文档数量")

    # 关联用户字段
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name="创建者",
        related_name="created_knowledge_bases",
        null=True,
        blank=True
    )

    # 时间字段
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")

    class Meta:
        db_table = "knowledge_bases"
        verbose_name = "知识库"
        verbose_name_plural = "知识库"
        ordering = ['-created_at']
        default_permissions = ()
        # 定义权限
        permissions = (
            ('view_knowledge_bases', '查看知识库'),
            ('add_knowledge_bases', '创建知识库'),
            ('change_knowledge_bases', '修改知识库'),
            ('delete_knowledge_bases', '删除知识库'),

            ('view_knowledge_bases_documents', '查看知识库文档'),
            ('upload_knowledge_bases_documents', '上传知识库文档'),
            ('delete_knowledge_bases_documents', '删除知识库文档'),
            ('update_knowledge_bases_documents', '更新知识库文档配置'),
            ('parse_operate_knowledge_bases_documents','知识库文档解析操作'),
            ('download_knowledge_bases_documents', '下载知识库文档'),


            ('view_knowledge_bases_chunks', '查看知识库块'),
            ('add_knowledge_bases_chunks', '添加知识库块'),
            ('delete_knowledge_bases_chunks', '删除知识库块'),
            ('update_knowledge_bases_chunks', '更新知识库块'),

            ('link_course_to_knowledge_bases', '绑定知识库课程'),
            ('unlink_course_from_knowledge_bases', '解绑知识库课程'),
        )

    def __str__(self):
        return self.name

    def get_course_count(self):
        """获取关联的课程数量"""
        return self.course_kb_links.filter(is_active=True).count()

    def get_course_names(self):
        """获取关联的课程名称列表"""
        return [link.course.title for link in self.course_kb_links.filter(is_active=True)]
    def get_course_ids(self):
        """获取关联的课程id列表"""
        return [link.course.id for link in self.course_kb_links.filter(is_active=True)]
    def get_course_names_text(self):
        """获取关联的课程名称文本"""
        names = self.get_course_names()
        return ", ".join(names) if names else "无关联课程"

    @property
    def creator_name(self):
        """获取创建者用户名"""
        return self.user.username if self.user else "未知用户"

    @property
    def user_id(self):
        """获取创建者ID（兼容性属性）"""
        return self.user.id if self.user else None

class CourseKBLink(models.Model):
    """课程-知识库关联模型"""
    course = models.ForeignKey(
        Course,  # 引用course app的Course模型
        on_delete=models.CASCADE,
        verbose_name="课程",
        related_name="course_kb_links"
    )
    knowledge_base = models.ForeignKey(
        KnowledgeBase,
        on_delete=models.CASCADE,
        verbose_name="知识库",
        related_name="course_kb_links"
    )
    linked_at = models.DateTimeField(auto_now_add=True, verbose_name="关联时间")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")


    # priority = models.IntegerField(default=0, verbose_name="优先级")
    # notes = models.TextField(blank=True, null=True, verbose_name="备注")

    class Meta:
        db_table = "course_kb_links"
        verbose_name = "课程知识库关联"
        verbose_name_plural = "课程知识库关联"
        unique_together = [['course', 'knowledge_base']]
        ordering = ['-linked_at']


    def __str__(self):
        return f"{self.course.title} -> {self.knowledge_base.name}"


class ChunkQA(models.Model):
    """Chunk问题答案存储模型"""
    id = models.AutoField(primary_key=True, verbose_name="主键ID")

    # chunk相关字段
    chunk_id = models.CharField(max_length=100, verbose_name="Chunk ID", db_index=True)
    question = models.TextField(verbose_name="问题内容")
    content = models.TextField(verbose_name="答案内容")

    # 关联字段
    document_id = models.CharField(max_length=100, verbose_name="文档ID", db_index=True)

    # 知识库外键关联（用于级联删除）
    knowledge_base = models.ForeignKey(
        KnowledgeBase,
        on_delete=models.CASCADE,
        verbose_name="关联知识库",
        related_name="chunk_qas",
        null=True,
        blank=True
    )

    # 时间字段
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")

    class Meta:
        db_table = "chunk_qa"
        verbose_name = "Chunk问答"
        verbose_name_plural = "Chunk问答"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['chunk_id']),
            models.Index(fields=['document_id']),
            models.Index(fields=['knowledge_base']),
            models.Index(fields=['created_at']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"Chunk {self.chunk_id} - {self.question[:50]}"

    @property
    def question_preview(self):
        """问题预览（前50个字符）"""
        return self.question[:50] + "..." if len(self.question) > 50 else self.question

    @property
    def content_preview(self):
        """内容预览（前100个字符）"""
        return self.content[:100] + "..." if len(self.content) > 100 else self.content

    @property
    def dataset_id(self):
        """获取知识库ID（兼容性属性）"""
        return self.knowledge_base.id if self.knowledge_base else None
# from django.db import models
# from django.contrib.auth import get_user_model
#
# User = get_user_model()
#
#
# class KnowledgeBase(models.Model):
#     """简化的知识库模型 - 包含创建者信息"""
#     id = models.CharField(max_length=100, primary_key=True, verbose_name="知识库ID")
#     name = models.CharField(max_length=200, verbose_name="知识库名称")
#     description = models.TextField(blank=True, null=True, verbose_name="知识库描述")
#     document_count = models.IntegerField(default=0, verbose_name="文档数量")
#
#     # 添加用户关联字段
#     user = models.ForeignKey(
#         User,
#         on_delete=models.CASCADE,
#         verbose_name="创建者",
#         related_name="created_knowledge_bases",
#         null=True,  # 添加这行
#         blank=True  # 添加这行
#     )
#
#     # 时间字段
#     created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
#     updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
#
#     # 状态字段
#     is_active = models.BooleanField(default=True, verbose_name="是否激活")
#
#     class Meta:
#         db_table = "knowledge_bases"
#         verbose_name = "知识库"
#         verbose_name_plural = "知识库"
#         ordering = ['-created_at']
#         default_permissions = ()
#         permissions = (
#             ("add_knowledge_base", "知识库添加"),
#             ("view_knowledge_base", "知识库查询"),
#             ("change_knowledge_base", "知识库修改"),
#             ("delete_knowledge_base", "知识库删除"),
#         )
#
#     def __str__(self):
#         return self.name
#
#     def get_course_count(self):
#         """获取关联的课程数量"""
#         #7.3修改
#         return self.course_kb_links.filter(is_active=True).count()
#
#     def get_course_names(self):
#         """获取关联的课程名称列表"""
#         return [link.course.title for link in self.course_kb_links.filter(is_active=True)]
#
#     def get_course_names_text(self):
#         """获取关联的课程名称文本"""
#         names = self.get_course_names()
#         return ", ".join(names) if names else "无关联课程"
#
#     @property
#     def creator_name(self):
#         """获取创建者用户名"""
#         return self.user.username if self.user else "未知用户"
#
#     @property
#     def user_id(self):
#         """获取创建者ID（兼容性属性）"""
#         return self.user.id if self.user else None
#
#
# class UserKBLink(models.Model):
#     """用户-知识库关联模型"""
#     user = models.ForeignKey(
#         User,
#         on_delete=models.CASCADE,
#         verbose_name="用户",
#         related_name="user_kb_links"
#     )
#     knowledge_base = models.ForeignKey(
#         KnowledgeBase,
#         on_delete=models.CASCADE,
#         verbose_name="知识库",
#         related_name="user_kb_links"
#     )
#     authorized_at = models.DateTimeField(auto_now_add=True, verbose_name="授权时间")
#     is_active = models.BooleanField(default=True, verbose_name="是否激活")
#
#     class Meta:
#         db_table = "user_kb_links"
#         verbose_name = "用户知识库关联"
#         verbose_name_plural = "用户知识库关联"
#         unique_together = [['user', 'knowledge_base']]
#         ordering = ['-authorized_at']
#
#     def __str__(self):
#         return f"{self.user.username} -> {self.knowledge_base.name}"
#
#
# class CourseKBLink(models.Model):
#     """课程-知识库关联模型"""
#     course = models.ForeignKey(
#         'course.Course',
#         on_delete=models.CASCADE,
#         verbose_name="课程",
#         related_name="course_kb_links"
#     )
#     knowledge_base = models.ForeignKey(
#         KnowledgeBase,
#         on_delete=models.CASCADE,
#         verbose_name="知识库",
#         related_name="course_kb_links"
#     )
#     linked_at = models.DateTimeField(auto_now_add=True, verbose_name="关联时间")
#     is_active = models.BooleanField(default=True, verbose_name="是否激活")
#
#     class Meta:
#         db_table = "course_kb_links"
#         verbose_name = "课程知识库关联"
#         verbose_name_plural = "课程知识库关联"
#         unique_together = [['course', 'knowledge_base']]
#         ordering = ['-linked_at']
#
#     def __str__(self):
#         return f"{self.course.title} -> {self.knowledge_base.name}"
